import { <PERSON><PERSON>erWindow, dialog } from 'electron'
import { BaseProxy, method } from './Base'
import { AppState, Architecture, DialogProperty, IWin, Platform } from '../interfaces/IWin'

export class Win extends BaseProxy<IWin> implements IWin {
  private _wins: Record<number, BrowserWindow>
  private _ids: Record<string, number>
  private _state: AppState

  constructor(win?: BrowserWindow, master = false) {
    super(IWin.NAME, master, win)

    this._wins = {}
    this._ids = {}
    this._state = AppState.AP_MAIN

    this.property('platform', this.platform)
    this.property('arch', this.arch)

    this.method('showOpenDialog', this, this.showOpenDialog.bind(this))
    this.method('switchState', this, this.switchState.bind(this))
    this.method('getState', this, this.getState.bind(this))

    this.hookMainWindow()
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  get arch() {
    return process.arch as Architecture
  }

  get platform() {
    return (process?.env?.NODE_PLAT as Platform) || process.platform
  }

  async openWindow(url: string, icon: string, title: string, width: number, height: number) {
    let id = this._ids[url]
    let win = (id && this._wins[id]) || null
    if (!win) {
      win = new BrowserWindow({
        title,
        icon,
        width,
        height,
        frame: false,
        show: false
      })
      win.loadURL(url)
      id = win.id
      win.once('closed', () => {
        delete this._wins[id]
        delete this._ids[url]
      })
    }
    win?.show()

    return id
  }

  async closeWindow(id: number) {
    const win = this._wins[id]
    const result = !!win
    win?.close()

    return result
  }

  async closeAll() {
    const wins = Object.values(this._wins)
    wins.forEach(win => win.close())

    return true
  }

  @method()
  async showOpenDialog(title: string, properties: DialogProperty[]) {
    const result =
      (this.win_ &&
        (await dialog.showOpenDialog(this.win_, {
          title,
          properties
        }))) ||
      undefined

    if (!result || result.canceled || result?.filePaths.length < 1) {
      return ''
    }

    return result?.filePaths[0] || ''
  }

  @method()
  async switchState(state: AppState) {
    if (state !== this._state) {
      this._state = state

      this.emit(IWin.ONSTATE, this._state)
    }
  }

  @method()
  async getState() {
    return this._state
  }

  private hookMainWindow() {
    if (!this.master) {
      return
    }

    this.win_?.on('close', (event: any) => {
      if (this._state === AppState.AP_ENGINE) {
        this.switchState(AppState.AP_MAIN)
        event.preventDefault()
      }
    })
  }

  private async loadOptions(force?: boolean) {}

  private async clearOptions() {}
}

# IWin 接口文档
## 相关文档
- [类型定义](type.md) - 包含 `WinConfig` 等类型定义
- [IBase](IBase.md) - 基础接口
- [ISdk](ISdk.md) - SDK 基础接口
- [ICfg](ICfg.md) - 配置接口
- [ISvc](ISvc.md) - 服务接口
- [IMenu](IMenu.md) - 菜单接口
- [IPage](IPage.md) - 页面接口
- [IApp](IApp.md) - 应用程序接口
- [IEngine](IEngine.md) - 引擎接口
- [IDisplay](IDisplay.md) - 显示接口

## 概述
`IWin` 是一个抽象类，继承自 `IBase<IWin>`，用于定义窗口管理的基本功能。它提供了平台信息获取、对话框显示、窗口状态管理和窗口操作等功能。

## 类型定义
### 平台类型
```typescript
export type [Platform](type.md#平台类型) =
  | 'aix'
  | 'android'
  | 'darwin'
  | 'freebsd'
  | 'haiku'
  | 'linux'
  | 'openbsd'
  | 'sunos'
  | 'win32'
  | 'cygwin'
  | 'netbsd'
```
### 架构类型
```typescript
export type [Architecture](type.md#架构类型) =
  | ''
  | 'arm'
  | 'arm64'
  | 'ia32'
  | 'mips'
  | 'mipsel'
  | 'ppc'
  | 'ppc64'
  | 's390'
  | 's390x'
  | 'x64'
```
### 对话框属性
```typescript
export type [DialogProperty](type.md#对话框属性) =
  | 'openFile'
  | 'openDirectory'
  | 'multiSelections'
  | 'showHiddenFiles'
  | 'createDirectory'
  | 'promptToCreate'
  | 'noResolveAliases'
  | 'treatPackageAsDirectory'
  | 'dontAddToRecent'
```
### 应用状态
```typescript
export enum [AppState](type.md#应用状态) {
  AP_MAIN = 0,  // 主界面
  AP_ENGINE = 1 // 发动机
}
```
## 接口定义
### IWin 抽象类
```typescript
export abstract class IWin extends IBase<IWin> {
  abstract get platform(): [Platform](type.md#平台类型)
  abstract get arch(): [Architecture](type.md#架构类型)
  abstract showOpenDialog(title: string, properties: [DialogProperty](type.md#对话框属性)[]): Promise<string>
  abstract getState(): Promise<[AppState](type.md#应用状态)>
  abstract switchState(state: [AppState](type.md#应用状态)): Promise<void>
  abstract openWindow(
    url: string,
    icon: string,
    title: string,
    width: number,
    height: number
  ): Promise<number>
  abstract closeWindow(id: number): Promise<boolean>
  abstract closeAll(): Promise<boolean>

  static override get NAME() { return 'Win' }
  static get ONSTATE() { return 'App.ONSTATE' }
}
```
## 方法说明
### 平台信息
- `platform`: 获取当前平台类型
- `arch`: 获取当前系统架构
### 对话框操作
- `showOpenDialog`: 显示打开文件对话框
  - 参数：
    - `title`: 对话框标题
    - `properties`: 对话框属性数组
  - 返回：Promise<string>
### 状态管理
- `getState`: 获取当前应用状态
  - 返回：Promise<AppState>
- `switchState`: 切换应用状态
  - 参数：
    - `state`: 目标状态
  - 返回：Promise<void>
### 窗口操作
- `openWindow`: 打开新窗口
  - 参数：
    - `url`: 窗口URL
    - `icon`: 窗口图标
    - `title`: 窗口标题
    - `width`: 窗口宽度
    - `height`: 窗口高度
  - 返回：Promise<number>
- `closeWindow`: 关闭指定窗口
  - 参数：
    - `id`: 窗口ID
  - 返回：Promise<boolean>
- `closeAll`: 关闭所有窗口
  - 返回：Promise<boolean>
## 静态属性
- `NAME`: 返回窗口管理器名称
- `ONSTATE`: 返回状态改变事件名称
## 类型定义
```typescript
export type WinMtr = BaseMtr<IWin>
export type WinItr = BaseItr<IWin>
```
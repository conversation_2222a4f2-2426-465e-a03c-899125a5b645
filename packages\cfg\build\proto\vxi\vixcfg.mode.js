import { types } from '@wuk/wkp'

export const RangeValue = {
  $keys: ['min', 'max', 'unit'],

  min: types.double,
  max: types.double,
  unit: types.string
}

export const VxiCalcLib = {
  $keys: ['type', 'min', 'max', 'data'],

  type: types.int32,
  min: types.double,
  max: types.double,
  data: types.arrayOf(types.double)
}

export const VxiEngine = {
  $keys: ['name', 'rate_group', 'signal_range', 'scale_range', 'progfilter', 'sensitivity', 'measwindow',
    'calib_mode', 'calib', 'calib_data', 'alias', 'dfilter'
  ],

  name: types.string,
  rate_group: types.string,
  signal_range: RangeValue,
  scale_range: RangeValue,
  progfilter: types.int32,
  sensitivity: types.int32,
  measwindow: types.double,
  calib_mode: types.int32,
  calib: types.int32,
  calib_data: VxiCalcLib,
  alias: types.string,
  dfilter: types.int32
}

export const VxiSignal = {
  $keys: ['name', 'channel', 'type', 'rate_group', 'signal_range', 'scale_range', 'progfilter', 'sensitivity', 'measwindow',
    'dfilter', 'calib_mode', 'calib', 'calib_data', 'engines'
  ],

  name: types.string,
  channel: types.int32,
  type: types.int32,
  rate_group: types.string,
  signal_range: RangeValue,
  scale_range: RangeValue,
  progfilter: types.int32,
  sensitivity: types.int32,
  measwindow: types.double,
  dfilter: types.int32,
  calib_mode: types.int32,
  calib: types.int32,
  calib_data: VxiCalcLib,
  engines: types.arrayOf(VxiEngine)
}

export const VixCfg = {
  $MAX: 8000,
  $MIN: 1000,
  $keys: ['name', 'rate_groups', 'scp', 'board_calcs_on', 'value1', 'value2', 'value3'],

  name: types.string,
  rate_groups: types.arrayOf(types.int32),
  scp: types.arrayOf(types.int32),
  board_calcs_on: types.boolean,
  value1: types.int32,
  value2: types.int32,
  value3: types.int32
}

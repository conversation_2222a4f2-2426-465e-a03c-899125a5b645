/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let QuestionItem={$keys:["type","id","prompt","answer","prev_param","operator","answer_set","enum","format"],type:types.string,id:types.string,prompt:types.string,answer:types.string,prev_param:types.string,operator:types.string,answer_set:types.string,enum:types.string,format:types.string},Questions={$MAX:2e3,$MIN:90,$keys:["type","name","question_vec"],type:types.string,name:types.string,question_vec:types.arrayOf(QuestionItem)};export{QuestionItem,Questions};
import { BaseItr, BaseMtr, IBase } from './IBase'
import {
  CrtControlType,
  CrtStatusType,
  DateFormatType,
  InputParamOrderType,
  TestHistoryType,
  TimeFormatType
} from './type'

export interface AppEngine {
  name: string
  label: string
}

export interface RgbItem {
  r: number
  g: number
  b: number
  a: number
  name: string
}

export interface AudoItem {
  name: string
  file: string
  repeat: number
  priority: number
}

export interface CrtItem {
  name: string
  x_address: string
  status: CrtStatusType
  resolution: string
  control: CrtControlType
}

export interface Hardware {
  id: string
  name: string
  inter: string
  printer_type: string
  scan_rate: number
  inst_addr: number
}

export interface SystemOptions {
  /**
   * Initial Alarm State
   */
  initial_alarm_state: boolean
  /**
   * Input Param Order
   */
  input_param_order: InputParamOrderType
  /**
   * Reference Test
   */
  reference_test: boolean
  /**
   * Automatic Open On Startup
   */
  automatic_open_on_startup: boolean
  /**
   * Store Messages To Database
   */
  store_messages_to_database: boolean
  /**
   * Rcs Control
   */
  rcs_control: boolean
  /**
   * Test History
   */
  test_history: TestHistoryType
  /**
   * Invalid Value
   */
  invalid_value: string
  /**
   * Test ID Prompt
   */
  test_id_prompt: string
  /**
   * System Scan Rate
   */
  system_scan_rate: string
  /**
   * Trigger Control Paramter
   */
  trigger_control_paramter: string
}

export interface PrintModeOptions {
  /**
   * Print Messages
   */
  print_messages: boolean
  /**
   * Print Stored Scans
   */
  print_stored_scans: boolean
  /**
   * Print Stored Comments
   */
  print_stored_comments: boolean
  /**
   * Print Stored Displays
   */
  print_stored_displays: boolean
}

export interface DataFormatOptions {
  /**
   * Time Format
   */
  time_format: TimeFormatType
  /**
   * Date Format
   */
  date_format: DateFormatType
  /**
   * Param Color
   */
  param_color: string
}

export interface CRSRecordingOptions {
  /**
   * switch
   */
  is_on: boolean
  /**
   * Number of mins
   */
  number_of_mins: number
  /**
   * Division of scan rate
   */
  division_of_scan_rate: number
}

export interface CRSOptions {
  /**
   * Auto Backup
   */
  auto_backup: boolean
  /**
   * Recording Control
   */
  recording_control: boolean
  /**
   * Maximum number of tests
   */
  maximum_number_of_tests: string
  /**
   * Disk space to leave free（Mb）
   */
  disk_space_to_leave_free: string
  /**
   * File System
   */
  file_system: string
  /**
   * Continuous Recording
   */
  continuous_recording: CRSRecordingOptions
  /**
   * Cyclic Recording
   */
  cyclic_recording: CRSRecordingOptions
}

export interface CrtOptions {
  /**
   * Display Update Rate
   */
  rate: number
  /**
   * Crts List
   */
  list: CrtItem[]
}

export interface AudioOptions {
  /**
   * Audio Host Address
   */
  audio_host_address: string
  /**
   * Sound List
   */
  list: AudoItem[]
}

export interface DeviceOptions {
  list: Hardware[]
}

export interface ColorOptions {
  list: RgbItem[]
}

export interface ResolutionItem {
  width: number
  height: number
  name: string
}

export interface ResolutionOptions {
  list: ResolutionItem[]
}

export abstract class ISystem extends IBase<ISystem> {
  abstract get engines(): Map<string, AppEngine>

  // System Options
  abstract readSystemOptions(): Promise<SystemOptions>
  abstract writeSystemOptions(val: Partial<SystemOptions>): Promise<boolean>

  // Print Modes
  abstract readPrintModes(): Promise<PrintModeOptions>
  abstract writePrintModes(val: Partial<PrintModeOptions>): Promise<boolean>

  // Data Formats
  abstract readDataFormat(): Promise<DataFormatOptions>
  abstract writeDataFormat(val: Partial<DataFormatOptions>): Promise<boolean>

  // CRS Options
  abstract readCRSOptions(): Promise<CRSOptions>
  abstract writeCRSOptions(val: Partial<CRSOptions>): Promise<boolean>

  // Crts Options
  abstract readCrtOptions(): Promise<CrtOptions>
  abstract setCrtRate(val: number): Promise<boolean>
  abstract removeCrt(index: number): Promise<boolean>
  abstract addCrt(val: CrtItem, index?: number): Promise<boolean>
  abstract modifyCrt(index: number, val: Partial<CrtItem>): Promise<boolean>

  // Audio Options
  abstract readAudioOptions(): Promise<AudioOptions>
  abstract setHostAddress(val: string): Promise<boolean>
  abstract removeAudio(index: number): Promise<boolean>
  abstract addAudio(val: AudoItem, index?: number): Promise<boolean>
  abstract modifyAudio(index: number, val: Partial<AudoItem>): Promise<boolean>

  // Device Options
  abstract readDeviceOptions(): Promise<DeviceOptions>
  abstract removeDevice(index: number): Promise<boolean>
  abstract addDevice(val: Hardware, index?: number): Promise<boolean>
  abstract modifyDevice(index: number, val: Partial<Hardware>): Promise<boolean>

  // Color Options
  abstract readColorOptions(): Promise<ColorOptions>
  abstract removeColor(index: number): Promise<boolean>
  abstract addColor(val: RgbItem, index?: number): Promise<boolean>
  abstract modifyColor(index: number, val: Partial<RgbItem>): Promise<boolean>

  // Resoltion Options
  abstract readResolutionOptions(): Promise<ResolutionOptions>
  abstract removeResolution(index: number): Promise<boolean>
  abstract addResolution(val: ResolutionItem, index?: number): Promise<boolean>
  abstract modifyResolution(index: number, val: Partial<ResolutionItem>): Promise<boolean>

  static override get NAME() {
    return 'System'
  }

  static get OnChanged() {
    return 'System.OnChanged'
  }

  static get OnSystemOptions() {
    return 'System.OnSystemOptions'
  }

  static get OnPrintModes() {
    return 'System.OnPrintModes'
  }

  static get OnDataFormat() {
    return 'System.OnDataFormat'
  }

  static get OnCRSOptions() {
    return 'System.OnCRSOptions'
  }

  static get OnCrtOptions() {
    return 'System.OnCrtOptions'
  }

  static get OnAudioOptions() {
    return 'System.OnAudioOptions'
  }

  static get OnDeviceOptions() {
    return 'System.OnDeviceOptions'
  }

  static get OnColorOptions() {
    return 'System.OnColorOptions'
  }

  static get OnResolutionOptions() {
    return 'System.OnResolutionOptions'
  }
}

export type SystemItr = BaseItr<ISystem>
export type SystemMtr = BaseMtr<ISystem>

$TABLEGROUP T_Del
$FORMAT ascii
$TABLE DeltaECSWB_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=0 extrap=3 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$TABLE DeltaECSPB_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=1 extrap=3 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$TABLE DeltaECSTB_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=2 extrap=3 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$TABLE DeltaECSWF_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=3 extrap=3 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$TABLE DeltaECSEGT_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=4 extrap=3 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE DeltaMESWB_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=5 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$TABLE DeltaMESPB_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=6 extrap=3 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$TABLE DeltaMESTB_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=7 extrap=3 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$TABLE DeltaMESWF_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=8 extrap=3 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$TABLE DeltaMESEGT_T
format="%f" ascii=1 dim=2 size=131 save=0 dynexp=0 offset=9 extrap=3 xparam=CITAvg 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$END-TABLEGROUP


$TABLEGROUP T_Temp
$TABLE EgtEng1_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=0 extrap=3 xparam=EgtEng1 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EgtEng2_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=1 extrap=3 xparam=EgtEng2 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT1_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=2 extrap=3 xparam=CIT1 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT2_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=3 extrap=3 xparam=CIT2 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT3_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=4 extrap=3 xparam=CIT3 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT4_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=5 extrap=3 xparam=CIT4 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT5_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=6 extrap=3 xparam=CIT5 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT6_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=7 extrap=3 xparam=CIT6 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT7_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=8 extrap=3 xparam=CIT7 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT8_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=9 extrap=3 xparam=CIT8 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT9_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=10 extrap=3 xparam=CIT9 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT10_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=11 extrap=3 xparam=CIT10 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT11_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=12 extrap=3 xparam=CIT11 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT12_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=13 extrap=3 xparam=CIT12 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT13_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=14 extrap=3 xparam=CIT13 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT14_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=15 extrap=3 xparam=CIT14 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT15_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=16 extrap=3 xparam=CIT15 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CIT16_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=17 extrap=3 xparam=CIT16 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT1_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=18 extrap=3 xparam=EGT1 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT2_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=19 extrap=3 xparam=EGT2 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT3_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=20 extrap=3 xparam=EGT3 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT4_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=21 extrap=3 xparam=EGT4 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT5_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=22 extrap=3 xparam=EGT5 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT6_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=23 extrap=3 xparam=EGT6 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT7_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=24 extrap=3 xparam=EGT7 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT8_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=25 extrap=3 xparam=EGT8 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT9_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=26 extrap=3 xparam=EGT9 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT10_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=27 extrap=3 xparam=EGT10 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT11_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=28 extrap=3 xparam=EGT11 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT12_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=29 extrap=3 xparam=EGT12 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE TCell_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=30 extrap=3 xparam=TCell 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE TB1_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=31 extrap=3 xparam=TB1 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE TB2_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=32 extrap=3 xparam=TB2 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE TB3_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=33 extrap=3 xparam=TB3 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE TB4_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=34 extrap=3 xparam=TB4 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE TCD1_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=35 extrap=3 xparam=TCD1 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE TCD2_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=36 extrap=3 xparam=TCD2 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE OriTemp1_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=37 extrap=3 xparam=OriTemp1 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE OriTemp2_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=38 extrap=3 xparam=OriTemp2 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE FMTemp_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=39 extrap=3 xparam=FMTemp 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE OilTemp_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=40 extrap=3 xparam=OilTemp 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EngFuelTemp_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=41 extrap=3 xparam=EngFuelTemp 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE EGT13_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=42 extrap=3 xparam=EGT13 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE
$END-TABLEGROUP


$TABLEGROUP T_Vol

$TABLE PCell_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=0 extrap=3 xparam=PCell 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE OilOutPress_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=1 extrap=3 xparam=OilOutPress 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE OilSumpPress_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=2 extrap=3 xparam=OilSumpPress 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE FPress_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=3 extrap=3 xparam=FPress 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE PB_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=4 extrap=3 xparam=PB 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE OriInltPress_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=5 extrap=3 xparam=OriInltPress 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE OriDiffPress_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=6 extrap=3 xparam=OriDiffPress 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CompDisPress_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=7 extrap=3 xparam=CompDisPress 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE PS9_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=8 extrap=3 xparam=PS9 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE CBVP_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=9 extrap=3 xparam=CBVP 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE FBVP_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=10 extrap=3 xparam=FBVP 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE GenVA_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=11 extrap=3 xparam=GenVA 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE GenVB_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=12 extrap=3 xparam=GenVB 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE GenVC_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=13 extrap=3 xparam=GenVC 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE GenAmpsA_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=14 extrap=3 xparam=GenAmpsA 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE GenAmpsB_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=15 extrap=3 xparam=GenAmpsB 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE GenAmpsC_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=16 extrap=3 xparam=GenAmpsC 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE StartVolA_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=17 extrap=3 xparam=StartVolA 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE StartVolB_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=18 extrap=3 xparam=StartVolB 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE StartVolC_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=19 extrap=3 xparam=StartVolC 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$END-TABLEGROUP


$TABLEGROUP T_Vib

$TABLE VB_1_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=0 extrap=3 xparam=VB_1 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE VB_2_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=1 extrap=3 xparam=VB_2 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE VB_3_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=2 extrap=3 xparam=VB_3 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE N1_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=3 extrap=3 xparam=N1 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE N2_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=4 extrap=3 xparam=N2 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE


$TABLE FW1_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=5 extrap=3 xparam=FW1 
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$TABLE FW2_T
format="%f" ascii=1 dim=2 size=2 save=0 dynexp=0 offset=6 extrap=3 xparam=FW2
exp=TRUE 
comment=""
revdate=""
mandate=""
manrev=""
$END-TABLE

$END-TABLEGROUP
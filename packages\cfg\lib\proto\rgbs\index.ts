/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Rgb as RgbModal,
  Rgbs as RgbsModal
} from '../modal/rgbs.mode'

export class Rgb extends MapProto<Rgb> {
  r = 0
  g = 0
  b = 0
  a = 0
  name = ''

  constructor(val?: Partial<Rgb>) {
    super(RgbModal, 'Rgb')
    val && this.assign(val)
  }
}

export class Rgbs extends RootProto<Rgbs> {
  rgbs: Array<Rgb> = []

  constructor(val?: Partial<Rgbs>) {
    super(RgbsModal, Rgbs.key)
    val && this.assign(val)
  }

  static get maxType() {
    return RgbsModal.$MAX
  }

  static get minType() {
    return RgbsModal.$MIN
  }

  static get uri() {
    return URI(RgbsModal.$MAX, RgbsModal.$MIN)
  }

  static get types(): [number, number] {
    return [RgbsModal.$MAX, RgbsModal.$MIN]
  }

  static get key() {
    return 'Rgbs'
  }
}

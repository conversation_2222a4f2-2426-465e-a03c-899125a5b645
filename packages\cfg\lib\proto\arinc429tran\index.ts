/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  BitValue as BitValueModal,
  RangeValue as RangeValueModal,
  ArincTranSignal as ArincTranSignalModal,
  Arinc429TranCfg as Arinc429TranCfgModal
} from '../modal/arinc429_tran.mode'

export class BitValue extends MapProto<BitValue> {
  name = ''
  bit_num = 0
  val_0 = ''
  val_1 = ''
  lable = ''

  constructor(val?: Partial<BitValue>) {
    super(BitValueModal, 'BitValue')
    val && this.assign(val)
  }
}

export class RangeValue extends MapProto<RangeValue> {
  min = 0.0
  max = 0.0
  unit = ''

  constructor(val?: Partial<RangeValue>) {
    super(RangeValueModal, 'RangeValue')
    val && this.assign(val)
  }
}

export class ArincTranSignal extends MapProto<ArincTranSignal> {
  name = ''
  label = ''
  data = ''
  start_bit = 0
  stop_bit = 0
  rate = 0
  sdi = ''
  ssm = ''
  bin_range = 0.0
  sig_bits = 0
  signal_range = new RangeValue()
  bits: Array<BitValue> = []

  constructor(val?: Partial<ArincTranSignal>) {
    super(ArincTranSignalModal, 'ArincTranSignal')
    val && this.assign(val)
  }
}

export class Arinc429TranCfg extends RootProto<Arinc429TranCfg> {
  channel = 0
  eec = ''
  speed = ''
  parity = ''
  active = ''
  tran_signals: Array<ArincTranSignal> = []

  constructor(val?: Partial<Arinc429TranCfg>) {
    super(Arinc429TranCfgModal, Arinc429TranCfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return Arinc429TranCfgModal.$MAX
  }

  static get minType() {
    return Arinc429TranCfgModal.$MIN
  }

  static get uri() {
    return URI(Arinc429TranCfgModal.$MAX, Arinc429TranCfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [Arinc429TranCfgModal.$MAX, Arinc429TranCfgModal.$MIN]
  }

  static get key() {
    return 'Arinc429TranCfg'
  }
}

# IPage 接口文档
## 相关文档
- [类型定义](type.md) - 包含 `PageConfig` 等类型定义
- [IBase](IBase.md) - 基础接口
- [ISdk](ISdk.md) - SDK 基础接口
- [ICfg](ICfg.md) - 配置接口
- [ISvc](ISvc.md) - 服务接口
- [IWin](IWin.md) - 窗口接口
- [IMenu](IMenu.md) - 菜单接口
- [IApp](IApp.md) - 应用程序接口
- [IEngine](IEngine.md) - 引擎接口
- [IDisplay](IDisplay.md) - 显示接口

## 概述
`IPage` 是一个抽象类，继承自 `Invoker<K>`，用于定义页面的基本功能和生命周期。它提供了页面状态管理、SDK 状态监控等核心功能。

## 接口定义
### IPage 抽象类
```typescript
export abstract class IPage<K extends string = string> extends Invoker<K> {
  // 获取 SDK 就绪状态
  abstract get sdkReady(): boolean

  // 获取页面 ID
  abstract get pageId(): string

  // 获取页面名称
  abstract get pageName(): string

  // 获取页面名称
  static override get NAME() {
    return 'Page'
  }

  // SDK 启动事件
  static get ONSDKUP() {
    return 'Page.ONSDKUP'
  }

  // SDK 停止事件
  static get ONSDKDOWN() {
    return 'Page.ONSDKDOWN'
  }

  // SDK 就绪事件
  static get ONSDKREADY() {
    return 'Page.ONSDKREADY'
  }
}
```

## 属性说明

### 抽象属性
- `sdkReady`: 获取 SDK 的就绪状态
  - 类型：`boolean`
  - 说明：表示 SDK 是否已经准备就绪

- `pageId`: 获取页面的唯一标识符
  - 类型：`string`
  - 说明：用于唯一标识一个页面实例

- `pageName`: 获取页面的名称
  - 类型：`string`
  - 说明：页面的显示名称

### 静态属性
- `NAME`: 返回页面接口的名称
  - 类型：`string`
  - 值：`'Page'`

- `ONSDKUP`: SDK 启动事件名称
  - 类型：`string`
  - 值：`'Page.ONSDKUP'`

- `ONSDKDOWN`: SDK 停止事件名称
  - 类型：`string`
  - 值：`'Page.ONSDKDOWN'`

- `ONSDKREADY`: SDK 就绪事件名称
  - 类型：`string`
  - 值：`'Page.ONSDKREADY'`

## 使用示例
```typescript
class MyPage extends IPage {
  private _sdkReady: boolean = false;
  private _pageId: string;
  private _pageName: string;

  constructor(pageId: string, pageName: string) {
    super();
    this._pageId = pageId;
    this._pageName = pageName;
  }

  get sdkReady(): boolean {
    return this._sdkReady;
  }

  get pageId(): string {
    return this._pageId;
  }

  get pageName(): string {
    return this._pageName;
  }

  // 实现其他必要的方法
}
```

## 注意事项
1. 这是一个抽象类，需要被具体实现类继承
2. 提供了 SDK 生命周期事件的管理
3. 支持泛型参数，可以扩展代理对象类型
4. 所有抽象属性都需要在具体实现类中实现
5. 事件名称使用静态属性定义，便于统一管理
6. 继承自 `Invoker` 类，具有调用器功能 
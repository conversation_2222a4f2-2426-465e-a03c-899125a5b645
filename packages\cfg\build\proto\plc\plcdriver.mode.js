import { types } from '@wuk/wkp'

export const RangeValue = {
  $keys: ['min', 'max', 'unit'],

  min: types.double,
  max: types.double,
  unit: types.string
}

export const PlcCalcLib = {
  $keys: ['type', 'min', 'max', 'data'],

  type: types.int32,
  min: types.double,
  max: types.double,
  data: types.arrayOf(types.double)
}

export const PlcSignal = {
  $keys: ['name', 'signal_range', 'units_range', 'type', 'scale_range', 'scale_factor', 'calib_mode', 'comment', 'calib', 'calib_data', 'comments'],

  name: types.string,
  signal_range: RangeValue,
  units_range: RangeValue,
  type: types.string,
  scale_range: RangeValue,
  scale_factor: types.double,
  calib_mode: types.int32,
  comment: types.int32,
  calib: types.int32,
  calib_data: PlcCalcLib,
  comments: types.arrayOf(types.string)
}

export const PlcSignals = {
  $keys: ['input_float', 'list'],

  input_float: types.boolean,
  list: types.arrayOf(PlcSignal)
}

export const PlcDevice = {
  $keys: ['type', 'addr', 'num', 'signals'],

  type: types.int32,
  addr: types.int32,
  num: types.int32,
  signals: PlcSignals
}

export const PlcCfg = {
  $keys: ['device_name', 'scan_rate', 'devices'],

  device_name: types.string,
  scan_rate: types.int32,
  devices: types.arrayOf(PlcDevice)
}

export const PlcDriverCfg = {
  $MAX: 10000,
  $MIN: 10,
  $keys: ['scan_rate', 'plcs'],

  scan_rate: types.int32,
  plcs: types.arrayOf(PlcCfg)
}

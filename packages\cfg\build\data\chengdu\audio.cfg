$AUDIO /dev/dsp
efso_act efso_act.au 2 1
engine_overlimits engine_overlimits.au 2 1
enginestart enginestart.au 2 1
fire_loop_1 fire_loop_1.au 2 1
fire_loop_2 fire_loop_2.au 2 1
fire_loop_3 fire_loop_3.au 2 1
fire_loop_4 fire_loop_4.au 2 1
set_pl_to_ext set_pl_to_ext.au 2 1
chimes chimes.wav 2 2
fire fire.au 3 2
gday gday.au 3 2
warning bell.au 3 2
alarm_siren tada.wav 3 2
ding ding.wav 5 2
tada tada.wav 2 2
mytestaudio mytest.au 2 2
Eidle E_idle.wav 2 1
FtankL1 tank1less10.wav 2 2
FtankL2 tank2less10.wav 2 2
$END-AUDIO

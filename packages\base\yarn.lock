# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@types/node@*":
  version "22.10.5"
  resolved "https://npm-registry.yy.com/@types/node/download/@types/node-22.10.5.tgz#95af89a3fb74a2bb41ef9927f206e6472026e48b"
  integrity sha1-la+Jo/t0ortB75kn8gbmRyAm5Is=
  dependencies:
    undici-types "~6.20.0"

"@types/node@^16.11.9":
  version "16.18.123"
  resolved "https://npm-registry.yy.com/@types/node/download/@types/node-16.18.123.tgz#9073e454ee52ce9e2de038e7e0cf90f65c9abd56"
  integrity sha1-kHPkVO5Szp4t4Djn4M+Q9lyavVY=

"@types/yauzl@^2.10.0":
  version "2.10.3"
  resolved "https://npm-registry.yy.com/@types/yauzl/download/@types/yauzl-2.10.3.tgz#e9b2808b4f109504a03cda958259876f61017999"
  integrity sha1-6bKAi08QlQSgPNqVglmHb2EBeZk=
  dependencies:
    "@types/node" "*"

"@types/yazl@^2.4.2":
  version "2.4.5"
  resolved "https://npm-registry.yy.com/@types/yazl/download/@types/yazl-2.4.5.tgz#0e21674799c7690afa23aeaff59806be5fe7494d"
  integrity sha1-DiFnR5nHaQr6I66v9ZgGvl/nSU0=
  dependencies:
    "@types/node" "*"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://npm-registry.yy.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://npm-registry.yy.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://npm-registry.yy.com/brace-expansion/download/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "https://npm-registry.yy.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://npm-registry.yy.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

crypto@^1.0.1:
  version "1.0.1"
  resolved "https://npm-registry.yy.com/crypto/download/crypto-1.0.1.tgz#2af1b7cad8175d24c8a1b0778255794a21803037"
  integrity sha1-KvG3ytgXXSTIobB3glV5SiGAMDc=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://npm-registry.yy.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://npm-registry.yy.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

glob@^7.1.3:
  version "7.2.3"
  resolved "https://npm-registry.yy.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://npm-registry.yy.com/hasown/download/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://npm-registry.yy.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2:
  version "2.0.4"
  resolved "https://npm-registry.yy.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://npm-registry.yy.com/is-core-module/download/is-core-module-2.16.1.tgz#2a98801a849f43e2add644fbb6bc6229b19a4ef4"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://npm-registry.yy.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.5:
  version "9.0.5"
  resolved "https://npm-registry.yy.com/minimatch/download/minimatch-9.0.5.tgz#d74f9dd6b57d83d8e98cfb82133b03978bc929e5"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

once@^1.3.0:
  version "1.4.0"
  resolved "https://npm-registry.yy.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://npm-registry.yy.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://npm-registry.yy.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

pend@~1.2.0:
  version "1.2.0"
  resolved "https://npm-registry.yy.com/pend/download/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"
  integrity sha1-elfrVQpng/kRUzH89GY9XI4AelA=

resolve@>=1.9.0:
  version "1.22.10"
  resolved "https://npm-registry.yy.com/resolve/download/resolve-1.22.10.tgz#b663e83ffb09bbf2386944736baae803029b8b39"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://npm-registry.yy.com/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://npm-registry.yy.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

ttypescript@^1.5.13:
  version "1.5.15"
  resolved "https://npm-registry.yy.com/ttypescript/download/ttypescript-1.5.15.tgz#e45550ad69289d06d3bc3fd4a3c87e7c1ef3eba7"
  integrity sha1-5FVQrWkonQbTvD/Uo8h+fB7z66c=
  dependencies:
    resolve ">=1.9.0"

typescript-transform-paths@^3.3.1:
  version "3.5.3"
  resolved "https://npm-registry.yy.com/typescript-transform-paths/download/typescript-transform-paths-3.5.3.tgz#bb456773abf3382d06a372cca6dce23218a0daed"
  integrity sha1-u0Vnc6vzOC0Go3LMptziMhig2u0=
  dependencies:
    minimatch "^9.0.5"

typescript@^4.7.3:
  version "4.9.5"
  resolved "https://npm-registry.yy.com/typescript/download/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

undici-types@~6.20.0:
  version "6.20.0"
  resolved "https://npm-registry.yy.com/undici-types/download/undici-types-6.20.0.tgz#8171bf22c1f588d1554d55bf204bc624af388433"
  integrity sha1-gXG/IsH1iNFVTVW/IEvGJK84hDM=

wrappy@1:
  version "1.0.2"
  resolved "https://npm-registry.yy.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

yauzl@^3.0.0:
  version "3.2.0"
  resolved "https://npm-registry.yy.com/yauzl/download/yauzl-3.2.0.tgz#7b6cb548f09a48a6177ea0be8ece48deb7da45c0"
  integrity sha1-e2y1SPCaSKYXfqC+js5I3rfaRcA=
  dependencies:
    buffer-crc32 "~0.2.3"
    pend "~1.2.0"

yazl@^2.4.3:
  version "2.5.1"
  resolved "https://npm-registry.yy.com/yazl/download/yazl-2.5.1.tgz#a3d65d3dd659a5b0937850e8609f22fffa2b5c35"
  integrity sha1-o9ZdPdZZpbCTeFDoYJ8i//orXDU=
  dependencies:
    buffer-crc32 "~0.2.3"

import { BrowserWindow } from 'electron'
import { ICfg, ICalibrate, IEngine, VirSignalItem, CfgFileType } from '../interfaces'
import { BaseProxy, method } from './Base'
import { calcvxi } from '../proto'
import { fileName } from 'lib/utils'

export class Calibrate extends BaseProxy<ICalibrate> implements ICalibrate {
  private static readonly kCalibrate = 'calibrate'
  private static readonly kCommon = 'common'
  private _options: VirSignalItem[]

  constructor(private readonly cfg: ICfg, win?: BrowserWindow, master = false) {
    super(ICalibrate.NAME, master, win)

    this._options = []
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readVirSignals(): Promise<Array<VirSignalItem>> {
    return this._options
  }

  @method()
  async removeVirSignal(index: number): Promise<boolean> {
    const item = this._options[index]
    if (!item) {
      return false
    }

    if (!(await this.cfg.removeCalc(item.file, Calibrate.kCalibrate))) {
      return false
    }
    this.cfg.remove(calcvxi.CalcVxi, item.group_name, Calibrate.kCalibrate)
    this._options.splice(index, 1)

    const changed = await this.writeVirSignalOption()
    changed && this.emit(ICalibrate.OnVirSignals)

    return changed
  }

  @method()
  async addVirSignal(val: VirSignalItem, index?: number): Promise<boolean> {
    const name = val.group_name
    const idx = this._options.findIndex(v => v.group_name === name)
    if (idx >= 0) {
      return false
    }
    const file = (name && (await this.cfg.createCalc(name, Calibrate.kCalibrate))) || ''
    if (!file) {
      return false
    }
    val.file = file

    if (index === undefined || index === null) {
      this._options.push(val)
    } else {
      this._options.splice(index, 0, val)
    }

    const changed = await this.writeVirSignalOption()
    changed && this.emit(ICalibrate.OnVirSignals)

    return changed
  }

  @method()
  async modifyVirSignal(index: number, val: Partial<VirSignalItem>): Promise<boolean> {
    const item = this._options[index]
    if (!item) {
      return false
    }
    const name = val.group_name
    if (name && name !== item.group_name) {
      const file = await this.cfg.renameCalc(item.group_name, name, Calibrate.kCalibrate)
      if (!file) {
        return false
      }
      val.file = file
    }

    if (name) {
      item.group_name = name
    }

    if (val.file !== undefined) item.file = val.file
    if (val.type !== undefined) item.type = val.type

    const changed = await this.writeVirSignalOption()
    changed && this.emit(ICalibrate.OnVirSignals)

    return changed
  }

  private async loadVirSignalOption(force?: boolean) {
    const { includes = [] } = (await this.cfg.read(calcvxi.CalcVxi, Calibrate.kCalibrate)) || {}
    this._options = includes.map(inc => {
      const file = inc.file
      const group_name = fileName(file)
      const type = file.includes(Calibrate.kCommon) ? CfgFileType.Common : CfgFileType.Calibrate

      return { group_name, type, file }
    })
  }

  private async writeVirSignalOption(force?: boolean) {
    const includes: calcvxi.IncludeFile[] = this._options.map(
      val => new calcvxi.IncludeFile({ file: val.file })
    )
    let changed = this.cfg.assign(calcvxi.CalcVxi, { includes }, '', Calibrate.kCalibrate)
    changed = changed && (await this.cfg.write(calcvxi.CalcVxi, Calibrate.kCalibrate))

    return changed
  }

  private async loadOptions(force?: boolean) {
    await this.loadVirSignalOption(force)
  }

  private async clearOptions() {
    this.cfg.remove(calcvxi.CalcVxi, '', Calibrate.kCalibrate)
  }
}

import { IAttribute } from './IAttribute'
import { IBase, BaseItr, BaseMtr } from './IBase'
import { ICalc } from './ICalc'
import { ICalibrate } from './ICalibrate'
import { ICfg } from './ICfg'
import { IDisplay } from './IDisplay'
import { IEngine } from './IEngine'
import { IHardwares } from './IHardwares'
import { IMenu } from './IMenu'
import { AppEngine, ISystem } from './ISystem'
import { ITable } from './ITable'
import { IWin } from './IWin'

export interface AppCustomer {
  name: string
  label: string
  version: string
}

export interface CfgVersion {
  name: string
  md5: string
  comments: string
}

export abstract class IApp extends IBase<IApp> {
  abstract get menu(): IMenu
  abstract get win(): IWin
  abstract get cfg(): ICfg
  abstract get eng(): IEngine
  abstract get calc(): ICalc
  abstract get system(): ISystem
  abstract get display(): IDisplay
  abstract get attribute(): IAttribute
  abstract get table(): ITable
  abstract get calibrate(): ICalibrate
  abstract get hardwares(): IHardwares

  abstract appInfo(): Promise<Record<string, any>>
  abstract uploadImage(path: string): Promise<string>
  abstract customerURL(fileName: string): Promise<string>
  abstract imageURL(path: string): Promise<string>
  abstract test(): Promise<Record<string, any>>

  abstract loadCustomers(): Promise<AppCustomer[]>
  abstract loadVersions(): Promise<CfgVersion[]>
  abstract loadEngines(): Promise<AppEngine[]>

  abstract getCustomer(): Promise<AppCustomer | undefined>
  abstract getVersion(): Promise<CfgVersion | undefined>
  abstract changeCustomer(name: string): Promise<boolean>
  abstract importVerison(name: string): Promise<boolean>
  abstract exportVerison(comments: string): Promise<boolean>

  abstract getEngine(): Promise<AppEngine | undefined>
  abstract openEngine(name: string): Promise<boolean>
  abstract createEngine: (name: string) => Promise<boolean>
  abstract copyEngine(name: string, newName: string): Promise<boolean>
  abstract deleteEngine(name: string): Promise<boolean>

  static override get NAME() {
    return 'App'
  }

  static get ONTEST() {
    return 'App.ONTEST'
  }

  static get ONCHANGED() {
    return 'App.ONCHANGED'
  }

  static get OnCustomer() {
    return 'App.OnCustomer'
  }

  // Customers change event
  static get OnCustomers() {
    return 'App.OnCustomers'
  }

  static get OnVersion() {
    return 'App.OnVersion'
  }
  // Versions change event
  static get OnVersions() {
    return 'App.OnVersions'
  }

  static get OnEngine() {
    return 'App.OnEngine'
  }
  // Engines change event
  static get OnEngines() {
    return 'App.OnEngines'
  }
}

export type AppItr = BaseItr<IApp>
export type AppMtr = BaseMtr<IApp>

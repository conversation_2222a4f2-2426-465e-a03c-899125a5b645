import { types } from '@wuk/wkp'

export const CustEngine = {
  $keys: ['name', 'label'],

  name: types.string,
  label: types.string,
}

export const CustCrt = {
  $keys: ['x_address', 'name', 'status', 'resolution', 'control'],

  x_address: types.string,
  name: types.string,
  status: types.string,
  resolution: types.string,
  control: types.string
}

export const CustCrts = {
  $keys: ['rate', 'list'],

  rate: types.int32,
  list: types.arrayOf(CustCrt)
}

export const Hardware = {
  $keys: ['id', 'name', 'inter', 'printer_type', 'scan_rate', 'inst_addr'],

  id: types.string,
  name: types.string,
  inter: types.string,
  printer_type: types.string,
  scan_rate: types.int32,
  inst_addr: types.int32
}

export const CrsRecording = {
  $keys: ['is_on', 'division_of_scan_rate', 'number_of_mins'],

  is_on: types.boolean,
  division_of_scan_rate: types.int32,
  number_of_mins: types.int32,
}

export const Crs = {
  $keys: ['auto_backup', 'recording_control', 'maximum_number_of_tests', 'disk_space_to_leave_free', 'continuous_recording', 'cyclic_recording', 'file_system'],

  auto_backup: types.boolean,
  recording_control: types.boolean,
  maximum_number_of_tests: types.string,
  disk_space_to_leave_free: types.string,
  continuous_recording: CrsRecording,
  cyclic_recording: CrsRecording,
  file_system: types.string
}

export const FieldValueItem = {
  $keys: ['value', 'label'],

  value: types.string,
  label: types.string
}

export const RequiredField = {
  $keys: ['name', 'type', 'value_required', 'label', 'value_vec'],

  name: types.string,
  type: types.int32,
  value_required: types.boolean,
  label: types.string,
  value_vec: types.arrayOf(FieldValueItem)
}

export const Cust = {
  $MAX: 1000,
  $MIN: 20,
  $keys: ['customer_name', 'engine_list', 'date_format', 'time_format', 'reference_test', 'shared_mem', 'crt',
    'hardware_list', 'input_param_order', 'param_color', 'crs', 'system_scan_rate', 'automatic_open_on_startup', 'rcs_control',
    'initial_alarm_state', 'test_history', 'store_messages_to_database', 'test_id_prompt', 'invalid_value', 'trigger_control_paramter', 
    'print_messages', 'print_stored_scans', 'print_stored_comments', 'print_stored_displays', 'answer_process', 'hide_open_question_cancel',
    'interface_style', 'field_vec'],

  customer_name: types.string,
  engine_list: types.arrayOf(CustEngine),
  date_format: types.string,
  time_format: types.string,
  reference_test: types.boolean,
  shared_mem: types.string,
  crt: CustCrts,
  hardware_list: types.arrayOf(Hardware),
  input_param_order: types.string,
  param_color: types.string,
  crs: Crs,
  system_scan_rate: types.string,
  automatic_open_on_startup: types.boolean,
  rcs_control: types.boolean,
  initial_alarm_state: types.boolean,
  test_history: types.boolean,
  store_messages_to_database: types.boolean,
  test_id_prompt: types.string,
  invalid_value: types.string,
  trigger_control_paramter: types.string,

  print_messages: types.boolean,
  print_stored_scans: types.boolean,
  print_stored_comments: types.boolean,
  print_stored_displays: types.boolean,

  answer_process: types.boolean,
  hide_open_question_cancel: types.boolean,
  interface_style: types.int32,
  field_vec: types.arrayOf(RequiredField)
}

import { types } from '@wuk/wkp'

export const Quad = {
  $keys: ['position', 'change_display_mode', 'list'],

  position: types.int32,
  change_display_mode: types.string,
  list: types.arrayOf(types.string)
}

export const Crt = {
  $keys: ['name', 'quads'],

  name: types.string,
  quads: types.arrayOf(Quad)
}

export const Crts = {
  $MAX: 2000,
  $MIN: 60,
  $keys: ['crts'],

  crts: types.arrayOf(Crt),
}

import { app, ipcMain, IpcMainInvokeEvent, ipc<PERSON><PERSON>er, IpcRendererEvent } from 'electron'
import { IMethod } from '../base'
import { IBaseProxy, IEmiter, IpcEvent } from '../interfaces'
import { BaseProxy } from '../proxy/Base'

let kIpcMainHandlers: string[] = []
let kIpcRendererListeners: string[] = []

const kHandlers: Record<string, IMethod> = {}
const kEvents: Record<string, IMethod> = {}

export const clearBridge = () => {
  console.info('clearBridge====', kIpcMainHandlers, kIpcRendererListeners)
  kIpcMainHandlers.forEach(key => {
    ipcMain.removeHandler(key)
    ipcMain.removeAllListeners(key)
  })
  kIpcRendererListeners.forEach(key => ipcRenderer.removeAllListeners(key))
  kIpcMainHandlers = []
  kIpcRendererListeners = []

  ipcMain.removeAllListeners()
}

export const createBridge = <T>(
  name: string,
  master: boolean,
  target: IEmiter,
  proxy: IBaseProxy,
  setup?: (key: T, target: object, method: IMethod) => void
) => {
  const result: Record<string, any> = {}
  for (const key in proxy) {
    const value = proxy[key]
    if (typeof value === 'function') {
      const method = value.bind(target)

      const evt = IpcEvent(name, key)
      const added = master
        ? method
        : async (...args: any[]) => {
            console.info(`${name} invoke key=${key} args=${JSON.stringify(args)}`)
            return await ipcRenderer.invoke(evt, ...args)
          }
      setup?.(key as T, target, added)
      result[key] = added

      if (master) {
        kIpcMainHandlers.push(evt)
        ipcMain.handle(evt, (event: IpcMainInvokeEvent, ...args: any[]) => {
          console.info(`${name} master invoke key=${key} args=${JSON.stringify(args)}`)
          return method?.apply(this || null, args, event)
        })
      }
    } else if (value instanceof BaseProxy) {
      const { proxy, method: handleSetup } = value
      result[key] = createBridge(name + ':' + key, master, value, proxy, handleSetup.bind(value))
    } else {
      result[key] = value
    }
  }
  if (master) {
    let evt = IpcEvent(name, IEmiter.ON)
    kIpcMainHandlers.push(evt)
    ipcMain.handle(evt, (event: IpcMainInvokeEvent, key: string) => {
      const handler = (...args: any[]) => {
        const evt = IpcEvent(name, IEmiter.EMIT)
        console.info(`${name} master send key=${evt} args=${JSON.stringify(args)}`)
        event.sender.send(evt, key, ...args)
      }
      target.on(key, handler)
      kHandlers[`${name}:${key}`] = handler
      return true
    })
    evt = IpcEvent(name, IEmiter.OFF)
    kIpcMainHandlers.push(evt)
    ipcMain.handle(evt, (event: IpcMainInvokeEvent, key: string) => {
      const handler = kHandlers[`${name}:${key}`]
      handler && target.off(key, handler)
      return true
    })
  } else {
    const handleOn = async (key: string, fn: (...args: any[]) => void) => {
      const evt = IpcEvent(name, IEmiter.ON)
      const result = await ipcRenderer.invoke(evt, key)
      result && (kEvents[`${name}:${key}`] = fn)
      return result
    }
    result[IEmiter.ON] = handleOn.bind(result)

    const handleOff = async (key: string, fn: (...args: any[]) => void) => {
      const evt = IpcEvent(name, IEmiter.OFF)
      const result = await ipcRenderer.invoke(evt, key)
      result && delete kEvents[`${name}:${key}`]
      return result
    }
    result[IEmiter.OFF] = handleOff.bind(result)

    const evt = IpcEvent(name, IEmiter.EMIT)
    kIpcRendererListeners.push(evt)
    ipcRenderer.on(evt, (event: IpcRendererEvent, key: string, ...args: any[]) => {
      console.info(`${name} event recv evt=${key} args=${JSON.stringify(args)}`)
      const handler = kEvents[`${name}:${key}`]
      return handler?.(...args)
    })
  }

  return result
}

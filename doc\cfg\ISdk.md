# ISdk 接口文档

## 相关文档
- [类型定义](type.md) - 包含 `SdkConfig` 等类型定义
- [IBase](IBase.md) - 基础接口
- [ICfg](ICfg.md) - 配置接口
- [ISvc](ISvc.md) - 服务接口
- [IWin](IWin.md) - 窗口接口
- [IMenu](IMenu.md) - 菜单接口
- [IPage](IPage.md) - 页面接口
- [IApp](IApp.md) - 应用程序接口
- [IEngine](IEngine.md) - 引擎接口
- [IDisplay](IDisplay.md) - 显示接口

## 概述
`ISdk` 是一个抽象类，继承自 `Invoker<K>`，用于定义 SDK 的基本功能。它提供了 SDK 的初始化和标识符检查功能。

## 接口定义

### ISdk 抽象类
```typescript
export abstract class ISdk<T extends IBaseProxy = object, K = string> extends Invoker<K> {
  abstract override init: (config: [SdkConfig](type.md#配置接口), proxy: T) => Promise<void>
  abstract has: (id: K) => boolean

  static get NAME() { return 'Sdk' }
}
```

### 方法说明

#### init
```typescript
abstract override init: (config: [SdkConfig](type.md#配置接口), proxy: T) => Promise<void>
```
初始化 SDK 的方法：
- 参数：
  - `config`: SDK 配置信息
  - `proxy`: 代理对象
- 返回：Promise<void>
- 说明：异步初始化 SDK，设置配置和代理

#### has
```typescript
abstract has: (id: K) => boolean
```
检查指定标识符是否存在：
- 参数：
  - `id`: 要检查的标识符
- 返回：boolean
- 说明：检查给定的标识符是否在 SDK 中注册

### 静态属性
```typescript
static get NAME() { return 'Sdk' }
```
返回 SDK 的名称标识符。

## 使用示例
```typescript
class MySdk extends ISdk<MyProxy> {
  async init(config: SdkConfig, proxy: MyProxy): Promise<void> {
    // 实现初始化逻辑
  }

  has(id: string): boolean {
    // 实现标识符检查逻辑
    return true;
  }
}
```

## 注意事项
1. 继承自 Invoker 类，具有调用器功能
2. 使用泛型支持不同类型的代理对象和标识符
3. init 方法是异步的，需要处理 Promise
4. 所有抽象方法都需要在具体实现类中实现
5. 代理对象需要实现 IBaseProxy 接口 
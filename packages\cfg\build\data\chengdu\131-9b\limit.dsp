$DISPLAY "Limit" background=NewGray editres=1280:448 movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$STOREDISPLAY Overwrite
$OBJECT Text font=1 startx=20 starty=10 endx=1240 endy=50
Red direction=1 alignment=1 fontweight=500 fontsize=22
"Limits"
$END-OBJECT
***********
$OBJECT Line font=1 startx=600 starty=35 endx=660 endy=37
linewidth=2 linecolor=Red
shading=0
$END-OBJECT
***********
$OBJECT Box font=1 startx=750 starty=10 endx=950 endy=40
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Text font=1 startx=750 starty=15 endx=950 endy=40
Black direction=1 alignment=1 fontweight=400 fontsize=16
"131_98"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=60 starty=80 endx=168 endy=235
labelspace=5 unitspace=5 spacing=8 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=8 prec=1 label="RTL Speed%" units="%" 
Digital2 type="FLOAT" width=8 prec=1 label="Max Oil Temp" units="Deg F"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=203 starty=80 endx=341 endy=315
labelspace=5 unitspace=5 spacing=8 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=8 prec=1 label="Rotor Low RPM" units="RPM" 
Digital2 type="FLOAT" width=8 prec=1 label="Min Oil Press Limit" units="PSI"
Digital3 type="FLOAT" width=8 prec=1 label="Max Gen Volts" units="VAC" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=353 starty=80 endx=495 endy=303
labelspace=5 unitspace=5 spacing=8 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=8 prec=1 label="100% Rotor RPM" units="RPM" 
Digital2 type="FLOAT" width=8 prec=1 label="Min Oil Press Warn" units="PSI"
Digital3 type="FLOAT" width=8 prec=1 label="Max Inlet" units="Deg F" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=510 starty=80 endx=651 endy=303
labelspace=5 unitspace=5 spacing=8 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=8 prec=1 label="Rotor High RPM" units="%" 
Digital2 type="FLOAT" width=8 prec=1 label="Max Oil Press Warn" units="Deg F"
Digital3 type="FLOAT" width=8 prec=1 label="sss" units="KW" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=660 starty=80 endx=770 endy=303
labelspace=5 unitspace=5 spacing=8 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=8 prec=1 label="RTL Speed%" units="%" 
Digital2 type="FLOAT" width=8 prec=1 label="Max Oil Temp" units="Deg F"
Digital3 type="FLOAT" width=8 prec=1 label="sss" units="KW" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=810 starty=80 endx=920 endy=300
labelspace=5 unitspace=5 spacing=8 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=8 prec=1 label="RTL Speed%" units="%" 
Digital2 type="FLOAT" width=8 prec=1 label="Max Oil Temp" units="Deg F"
Digital3 type="FLOAT" width=8 prec=1 label="sss" units="KW" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=960 starty=80 endx=1070 endy=300
labelspace=5 unitspace=5 spacing=8 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=8 prec=1 label="RTL Speed%" units="%" 
Digital2 type="FLOAT" width=8 prec=1 label="Max Oil Temp" units="Deg F"
Digital3 type="FLOAT" width=8 prec=1 label="sss" units="KW" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=1110 starty=80 endx=1220 endy=300
labelspace=5 unitspace=5 spacing=8 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=8 prec=1 label="RTL Speed%" units="%" 
Digital2 type="FLOAT" width=8 prec=1 label="Max Oil Temp" units="Deg F"
Digital3 type="FLOAT" width=8 prec=1 label="sss" units="KW" 
$END-OBJECT
***********
$OBJECT FuncButton font=22 startx=1145 starty=380 endx=1230 endy=430
type="CHANGEDSP" label="Close" backgroundcolor=DarkGray textcolor=Red fontsize=18 fontweight=400
crtname="Test CRT" quadindx=1 displayname="Main Second"
$END-OBJECT
***********
$END-DISPLAY

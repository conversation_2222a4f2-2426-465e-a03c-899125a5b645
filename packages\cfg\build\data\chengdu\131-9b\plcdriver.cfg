$SCANRATE 10
$PLC device="FF1_Upstream" rate=5
*
$DEVICE type=4 addr=247 num=4
$SIGNAL-LIST
$INPUT-FLOAT
FF1_MassFlowRate_RAW SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF1_Density SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF1_Temperature SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF1_VolumeRate SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
$END-SIGNAL-LIST
$END-DEVICE
*
$DEVICE type=1 addr=259 num=2
$SIGNAL-LIST
$INPUT-FLOAT
FF1_Totalizer SIGNALRANGE="0.000000 32000.000000 111" SCALERANGE="0.000000 32000.000000 111" SCALEFACTOR="1.000000" CALIBMODE=2 COMMENT=1 CALIB=1
$CALIB 2 0.000000 100000.000000
0.000000 32000.000000
0.000000 32000.000000
$END-CALIB
$COMMENT
111111
122121

121212

12221212
$END-COMMENT
FF1_TotalMass SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF1_TotalVolume SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
$END-SIGNAL-LIST
$END-DEVICE
*
$DEVICE type=4 addr=291 num=1
$SIGNAL-LIST
$INPUT-FLOAT
FF1_DriveGain SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
$END-SIGNAL-LIST
$END-DEVICE
*
$END-PLC
********
*
$PLC device="FF2_Downstream" rate=5
*
$DEVICE type=4 addr=247 num=4
$SIGNAL-LIST
$INPUT-FLOAT
FF2_MassFlowRate_RAW SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF2_Density SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF2_Temperature SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF2_VolumeRate SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
$END-SIGNAL-LIST
$END-DEVICE
*
$DEVICE type=4 addr=259 num=2
$SIGNAL-LIST
$INPUT-FLOAT
FF2_Totalizer SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF2_TotalMass SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF2_TotalVolume SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
$END-SIGNAL-LIST
$END-DEVICE
*
$DEVICE type=4 addr=291 num=1
$SIGNAL-LIST
$INPUT-FLOAT
FF2_DriveGain SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
$END-SIGNAL-LIST
$END-DEVICE
*
$END-PLC
********
*
$PLC device="FF3_Return2Tank" rate=5
*
$DEVICE type=4 addr=247 num=4
$SIGNAL-LIST
$INPUT-FLOAT
FF3_MassFlowRate_STD SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF3_Density SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF3_Temperature SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF3_VolumeRate SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
$END-SIGNAL-LIST
$END-DEVICE
*
$DEVICE type=4 addr=259 num=2
$SIGNAL-LIST
$INPUT-FLOAT
FF3_Totalizer SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF3_TotalMass SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
FF3_TotalVolume SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
$END-SIGNAL-LIST
$END-DEVICE
*
$DEVICE type=4 addr=291 num=1
$SIGNAL-LIST
$INPUT-FLOAT
FF3_DriveGain SIGNALRANGE="0.000000 32000.000000" UNITSRANGE="0.000000 32000.000000"
$END-SIGNAL-LIST
$END-DEVICE
*
$END-PLC

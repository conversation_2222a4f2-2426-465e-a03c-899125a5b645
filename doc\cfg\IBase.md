# IBase 接口文档

## 相关文档
- [类型定义](type.md) - 包含 `BaseConfig`、`IEmiter` 等类型定义

## 概述
`IBase` 是一个基础抽象类，提供了事件发射器和基础代理功能。它是其他接口的基础类，提供了事件处理和代理访问的基本功能。

## 类型定义

### [BaseConfig](type.md#基础类型)
```typescript
export type BaseConfig = Record<string, any>
```
基础配置类型，用于存储键值对配置信息。

### [IEmiter](type.md#基础类型) 抽象类
```typescript
export abstract class IEmiter {
  abstract emit(event: string, ...args: any[]): boolean
  abstract on(event: string, fn: (...args: any[]) => void): this
  abstract off(event: string, fn: (...args: any[]) => void): this

  static get EMIT() { return 'emit' }
  static get ON() { return 'on' }
  static get OFF() { return 'off' }
}
```
事件发射器抽象类，提供事件处理的基本功能：
- `emit`: 触发事件
- `on`: 注册事件监听器
- `off`: 移除事件监听器

### [IBase](type.md#基础类型) 抽象类
```typescript
export abstract class IBase<T extends IBaseProxy = object, K = string> extends IEmiter {
  abstract get proxy(): T
  abstract method(name: K, invoker: object, method: IMethod): void
  abstract property(name: K, value: any): void

  static get NAME() { return 'Base' }
}
```
基础抽象类，继承自 IEmiter，提供代理访问和方法调用功能：
- `proxy`: 获取代理对象
- `method`: 注册方法
- `property`: 设置属性
- `NAME`: 静态属性，返回基础名称

### 工具类型
```typescript
export type BaseMtr<T extends IBase> = Exclude<MethodPicks<IBase, T>, MethodNames<IBase>>
export type BaseItr<T extends IBase> = Omit<T, 'emit' | 'proxy' | 'method' | 'property'>
```
- `BaseMtr`: 排除基础方法后的方法类型
- `BaseItr`: 排除基础属性后的接口类型

### 工具函数
```typescript
export const IpcEvent = (name: string, key: string) => `${name}:${key}`
```
用于生成 IPC 事件名称的工具函数。

## 使用示例
```typescript
class MyBase extends IBase<MyProxy> {
  get proxy(): MyProxy {
    return new MyProxy();
  }

  method(name: string, invoker: object, method: IMethod): void {
    // 实现方法注册逻辑
  }

  property(name: string, value: any): void {
    // 实现属性设置逻辑
  }
}
```

## 注意事项
1. 所有继承 IBase 的类都需要实现抽象方法
2. 事件处理机制支持链式调用
3. 代理对象需要实现 IBaseProxy 接口
4. 方法注册和属性设置是异步操作
5. 事件名称格式为 "name:key" 
import { app, BrowserWindow } from 'electron'
import { IApp, IWin, AppState, ICfg, ISystem, Hardware } from '../interfaces'
import { BaseProxy, method, property } from './Base'
import { copyLocalFile, existed, Path } from '../utils'
import { cust } from '../proto'

import { Menu } from './Menu'
import { Win } from './Win'
import { Cfg } from './Cfg'
import { Engine } from './Engine'
import { Calc } from './Calc'
import { System } from './System'

import { Rcs } from '../utils/rcs'
import Zmq from '../utils/zmq'
import { buffer, extract, zip } from '@wuk/base/dist/node/zip'
import { CancellationToken } from '@wuk/base/dist/cancellation'
import { BaseProto, ProtoClass } from '@wuk/wkp'
import path from 'node:path'
import { pathToFileURL } from 'node:url'
import { Display } from './Display'
import { Attribute } from './Attribute'
import { Table } from './Table'
import { Calibrate } from './Calibrate'
import { Hardwares } from './Hardwares'

export class App extends BaseProxy<IApp> implements IApp {
  private _menu: Menu
  private _win: Win
  private _cfg: Cfg
  private _eng: Engine
  private _calc: Calc
  private _system: System
  private _display: Display
  private _attribute: Attribute
  private _table: Table
  private _calibrate: Calibrate
  private _hardwares: Hardwares

  constructor(win?: BrowserWindow, master = false, path = '') {
    super(IApp.NAME, master, win)
    Path.appDir = path

    this._modules = [
      (this._menu = new Menu(win, master)),
      (this._win = new Win(win, master)),
      (this._cfg = new Cfg(win, master)),
      (this._eng = new Engine(this._cfg, win, master)),
      (this._calibrate = new Calibrate(this._cfg, win, master)),
      (this._system = new System(this._cfg, win, master)),
      (this._calc = new Calc(this._cfg, undefined, win, master)),
      (this._display = new Display(this._cfg, undefined, win, master)),
      (this._attribute = new Attribute(this._cfg, undefined, win, master)),
      (this._hardwares = new Hardwares(this._cfg, undefined, win, master)),
      (this._table = new Table(this._cfg, undefined, win, master))
    ]

    this.property('menu', this._menu)
    this.property('win', this._win)
    this.property('cfg', this._cfg)
    this.property('eng', this._eng)
    this.property('system', this._system)
    this.property('calc', this._calc)
    this.property('display', this._display)
    this.property('attribute', this._attribute)
    this.property('table', this._table)
    this.property('calibrate', this._calibrate)
    this.property('hardwares', this._hardwares)

    this.handleWinState = this.handleWinState.bind(this)
    this.handleCustomer = this.handleCustomer.bind(this)
    this.handleCustomers = this.handleCustomers.bind(this)
    this.handleVersion = this.handleVersion.bind(this)
    this.handleVersions = this.handleVersions.bind(this)

    this.handleSystemChanged = this.handleSystemChanged.bind(this)
  }

  get menu() {
    return this._menu
  }

  get win() {
    return this._win
  }

  get cfg() {
    return this._cfg
  }

  get eng() {
    return this._eng
  }

  get calc() {
    return this._calc
  }

  get system() {
    return this._system
  }

  get display() {
    return this._display
  }

  get attribute() {
    return this._attribute
  }

  get table() {
    return this._table
  }

  get calibrate() {
    return this._calibrate
  }

  get hardwares() {
    return this._hardwares
  }

  override async init() {
    this.log('init====== begin')

    this._win.on(IWin.ONSTATE, this.handleWinState)

    this._cfg.on(ICfg.OnCustomer, this.handleCustomer)
    this._cfg.on(ICfg.OnCustomers, this.handleCustomers)
    this._cfg.on(ICfg.OnVersion, this.handleVersion)
    this._cfg.on(ICfg.OnVersions, this.handleVersions)

    this._system.on(ISystem.OnChanged, this.handleSystemChanged)

    await this.loadOptions()
    this.initElectronApp()
    await super.init()
    this.log('init========== end')
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async appInfo() {
    const dataDir = Path.dataDir
    const toolsDir = Path.toolsDir
    const backDir = Path.backDir

    const resourcesPath = process.resourcesPath
    const isPackaged = app.isPackaged
    return { dataDir, toolsDir, backDir, isPackaged, resourcesPath }
  }

  @method()
  async uploadImage(imagePath: string) {
    const customer = this._cfg.customer
    if (!existed(imagePath) || !customer) {
      return ''
    }
    const fileName = path.basename(imagePath)
    const destination = Path.join(Path.ensureImageDir(customer), fileName)
    if (await copyLocalFile(imagePath, destination, { overwrite: true })) {
      return fileName // Path.customerURL(customer, Path.imageURL(fileName))
    }

    return ''
  }

  @method()
  async customerURL(fileName: string) {
    const customer = this._cfg.customer
    if (!customer) {
      return ''
    }
    return Path.customerURL(customer, Path.imageURL(fileName))
  }

  async imageURL(path: string) {
    return Path.imageURL(path)
  }

  @method()
  test(): Promise<Record<string, any>> {
    // TODO: TEST
    // const req = new biz.TestReq()
    // req.value1 = 900.01111
    // const tran = this._svc.fetch(kMainSvcId)
    // setInterval(() => {
    //   tran.send({value1: 900.01111})
    // }, 1000)
    // tran.onUri(biz.TestReq, rsp => {
    //   this.log('test recv: data={%1}', rsp)
    // })
    // Zmq.HandleSend()

    const root = app.getPath('home').replace(/\/[^/]+$/, '')
    // Rcs.init(root)

    console.info('000000000000', root)
    const test_zip = () => {
      // const
    }
    const test_rcs = async () => {
      // let ret = await Rcs.add(root, 'c1.txt', 'add file')
      // console.info('8888888888888888888 Rcs.add ret=', ret)
      // ret = await Rcs.ci(root, 'c1.txt', 'add file1111')
      // console.info('8888888888888888888 Rcs.ci ret=', ret)
      // ret = await Rcs.co(root, 'c1.txt')
      // console.info('8888888888888888888 Rcs.co ret=', ret)
      // ret = await Rcs.ci(root, 'c1.txt', 'add file22222')
      // console.info('8888888888888888888 Rcs.co ret=', ret)
      // const logs = await Rcs.logs(root, 'c1.txt')
      // console.info('8888888888888888888 Rcs.logs logs=', logs)
      // await zip(`${root}/c1.cfg`, [
      //   { localPath: `${root}/c1.txt`, path: `ce/c1.txt` },
      //   { localPath: `${root}/c2.txt`, path: `c2.txt` }
      // ])
      // await extract(`${root}/c1.cfg`, `${root}/111`, { overwrite: true }, CancellationToken.None)
      // const buf = await buffer(`${root}/c1.cfg`, `c2.txt`)
      // console.info('999999999999999', buf.toString())
    }

    test_rcs()

    this.emit(IApp.ONTEST, { data: '000000000test eve' })
    return Promise.resolve({ test: 'sdfasdfasdfasdfas' })
  }

  @method()
  async getCustomer() {
    const result = await this._cfg.loadCustomer()

    this.log('getCustomer======result={%1}', result)
    return result
  }

  @method()
  async getVersion() {
    const result = await this._cfg.loadVersion()
    this.log('getVersion======result={%1}', result)

    return result
  }

  @method()
  async changeCustomer(name: string): Promise<boolean> {
    const result = await this._cfg.changeCustomer(name)

    if (result) {
      this.loadOptions(true)
    }
    this.log('changeCustomer======name={%1} result={%2}', name, result)

    return result
  }

  @method()
  async loadVersions() {
    const result = await this._cfg.loadVersions()
    this.log('loadVersions======versions={%1}', result)

    return result
  }

  @method()
  async loadEngines() {
    const result = Array.from(this._system.engines.values())
    this.log('loadEngines======result={%1}', result)

    return result
  }

  @method()
  async getEngine() {
    const engineName = this._eng.engineName
    const result = this._system.engines.get(engineName)

    this.log('getEngine======engine={%1} result={%2}', engineName, result)
    return result
  }

  @method()
  async openEngine(name: string) {
    const engineName = this._eng.engineName
    const result = engineName !== name && this._system.engines.has(name)
    if (result) {
      await this._eng.openEngine(name)
      this.emit(IApp.OnEngine)
    }

    this.log('openEngine======engine={%1} result={%2}', name, result)
    return result
  }

  @method()
  async loadCustomers() {
    const result = await this._cfg.loadCustomers()
    this.log('loadCustomers======result={%1}', result)

    return result
  }

  @method()
  async importVerison(name: string) {
    const result = await this._cfg.importCfg(name)
    this.log('importVerison======name={%1} result={%2}', name, result)

    if (result) {
      await this.loadOptions(true)
      this.emit(IApp.OnVersion)
    }
    return result
  }

  @method()
  async exportVerison(comments: string) {
    const result = await this._cfg.exportCfg(comments)

    if (result) {
      this.emit(IApp.OnVersions)
    }

    this.log('exportVerison======comments={%1} result={%2}', comments, result)

    return result
  }

  @method()
  async createEngine(name: string) {
    name = name.toLowerCase()
    let result = false
    if (!this._system.engines.has(name)) {
      if (await this._cfg.createEngine(name)) {
        const label = name.toUpperCase()
        this._system.engines.set(name, { name, label })

        await this.writeEngines()

        this.emit(IApp.OnEngines)
        result = true
      }
    }

    this.log('createEngine====== name={%1} result={%2}', name, result)

    return result
  }

  @method()
  async copyEngine(name: string, newName: string) {
    name = name.toLowerCase()
    newName = newName.toLowerCase()
    let result = false
    if (this._system.engines.has(name) && !this._system.engines.has(newName)) {
      if (await this._cfg.copyEngine(name, newName)) {
        const label = newName.toUpperCase()
        this._system.engines.set(name, { name, label })

        await this.writeEngines()

        this.emit(IApp.OnEngines)
        result = true
      }
    }

    this.log('copyEngine====== name={%1} newName={%2} result={%3}', name, newName, result)

    return result
  }

  @method()
  async deleteEngine(name: string) {
    name = name.toLowerCase()
    let result = false
    if (this._system.engines.has(name)) {
      if (await this._cfg.deleteEngine(name)) {
        const label = name.toUpperCase()
        this._system.engines.set(name, { name, label })

        await this.writeEngines()

        this.emit(IApp.OnEngines)
        result = true
      }
    }

    this.log('deleteEngine====== name={%1} result={%2}', name, result)

    return result
  }

  private async writeEngines() {
    const engine_list = Array.from(this._system.engines.values()).map(el => {
      return new cust.CustEngine(el)
    })
    const changed = this._cfg.assign(cust.Cust, { engine_list })
    return changed && (await this._cfg.write(cust.Cust))
  }

  private handleWinState(state: AppState) {
    const engineName = this._eng.engineName
    if (state === AppState.AP_MAIN && engineName) {
      this._eng.closeEngine()

      this.emit(IApp.OnEngine)
    }
  }

  private async loadOptions(force?: boolean) {}

  private async clearOptions() {}

  private handleCustomer() {
    this.emit(IApp.OnCustomer)
  }

  private handleCustomers() {
    this.emit(IApp.OnCustomers)
  }

  private handleVersion() {
    this.emit(IApp.OnVersion)
  }

  private handleVersions() {
    this.emit(IApp.OnVersions)
  }

  private handleSystemChanged() {
    this.emit(IApp.OnEngines)
  }

  private initElectronApp() {}
}

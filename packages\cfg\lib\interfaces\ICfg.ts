import { ScanDirResult } from 'lib/utils'
import { BaseItr, BaseMtr, IBase } from './IBase'
import { BaseProto, ProtoClass } from '@wuk/wkp'

export enum ErrorCode {
  OK = 0,
  ParamErr = -1, // 暂未支持
  UnSupported = -2, // 暂未支持
  FileLoadErr = -3, // 文件加载错误
  FileReadErr = -4, // 文件读取错误
  FileWriteErr = -5 // 文件写入错误
}

export interface CfgEvent<T = any> {
  uri: string
  code?: ErrorCode
  message?: string

  readonly data?: T
}

export interface MessageInfo {
  timestamp: number
  message: string
}

export abstract class ICfg extends IBase<ICfg> {
  abstract get customer(): string
  abstract get cfgDir(): string
  abstract read<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    engineName?: string,
    force?: boolean,
    fileName?: string
  ): Promise<T | undefined>
  abstract write<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    engineName?: string,
    fileName?: string
  ): Promise<boolean>
  abstract remove<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    fileName?: string,
    engineName?: string
  ): Promise<void>
  abstract assign<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    val: Record<string, any>,
    fileName?: string,
    engineName?: string
  ): boolean

  abstract readText<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    engineName?: string,
    fileName?: string,
    force?: boolean
  ): Promise<string | undefined>
  abstract writeText<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    data: string,
    engineName?: string,
    fileName?: string
  ): Promise<boolean>

  abstract createDisplay(engine: string, name: string): Promise<string>
  abstract removeDisplay(engine: string, name: string): Promise<boolean>
  abstract renameDisplay(engine: string, oldName: string, newName: string): Promise<string>

  abstract createCalc(name: string, engine?: string): Promise<string>
  abstract removeCalc(name: string, engine?: string): Promise<boolean>
  abstract renameCalc(oldName: string, newName: string, engine?: string): Promise<string>

  abstract removeFile(name: string, path?: string): Promise<boolean>
  abstract moveFile(name: string, from: string, to: string): Promise<boolean>
  abstract copyFile(path: string, fromName: string, toName: string): Promise<boolean>
  abstract createFile(name: string, path: string): Promise<boolean>

  abstract writeMessage(message: string): Promise<void>
  abstract loadMessage(): Promise<MessageInfo[]>

  abstract scanFiles(
    engine: string,
    recurse?: boolean,
    exclude?: string[],
    exts?: string[]
  ): Promise<ScanDirResult>

  static override get NAME() {
    return 'Cfg'
  }

  static get CHANGED() {
    return 'Cfg.CHANGED'
  }

  static get OnCustomer() {
    return 'Cfg.OnCustomer'
  }

  // Customers change event
  static get OnCustomers() {
    return 'Cfg.OnCustomers'
  }

  static get OnVersion() {
    return 'Cfg.OnVersion'
  }
  // Versions change event
  static get OnVersions() {
    return 'Cfg.OnVersions'
  }
  static get OnMessage() {
    return 'Cfg.OnMessage'
  }
}

export type CfgMtr = BaseMtr<ICfg>
export type CfgItr = BaseItr<ICfg>

import { <PERSON><PERSON><PERSON><PERSON>indow } from 'electron'
import {
  ICfg,
  IEngine,
  ITimer,
  kTimerType,
  kTimerTypeVal,
  TimerItem,
  TimersOptions
} from '../interfaces'
import { BaseProxy, method } from './Base'

import { timers } from '../proto'

export class Timer extends BaseProxy<ITimer> implements ITimer {
  private _options: TimersOptions

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(ITimer.NAME, master, win)

    this._options = {
      list: []
    }
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readOptions(): Promise<TimersOptions> {
    return this._options
  }

  @method()
  async removeTimer(index: number): Promise<boolean> {
    this._options.list.splice(index, 1)

    const changed = await this.writeTimersCache()
    changed && this.emit(ITimer.OnOptions)

    return changed
  }

  @method()
  async addTimer(val: TimerItem, index?: number): Promise<boolean> {
    if (index === undefined || index === null) {
      this._options.list.push(val)
    } else {
      this._options.list.splice(index, 0, val)
    }

    const changed = await this.writeTimersCache()
    changed && this.emit(ITimer.OnOptions)

    return changed
  }

  @method()
  async modifyTimer(index: number, val: Partial<TimerItem>): Promise<boolean> {
    if (this._options.list.length <= index) {
      return false
    }
    const item = this._options.list[index]
    if (val.name !== undefined) item.name = val.name
    if (val.equation !== undefined) item.equation = val.equation
    if (val.type !== undefined) item.type = val.type

    const changed = await this.writeTimersCache()
    changed && this.emit(ITimer.OnOptions)

    return changed
  }

  private get engineName() {
    return this.engine?.engineName || 'common'
  }

  private async loadTimers(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    const { timers: list = [] } = (await this.cfg.read(timers.Timers, this.engineName, force)) || {}
    this._options.list = list.map(val => {
      const { name, param_id: equation, type } = val
      return { name, equation, type: kTimerType[type] }
    })
    this.emit(ITimer.OnOptions)
  }

  private async writeTimersCache() {
    if (this.engine && !this.engine.engineName) {
      return false
    }

    const list: timers.Timer[] = this._options.list.map(val => {
      const { name, equation: param_id, type } = val

      return new timers.Timer({ name, param_id, type: kTimerTypeVal[type] })
    })
    let changed = this.cfg.assign(timers.Timers, { timers: list }, '', this.engineName)
    changed = changed && (await this.cfg.write(timers.Timers, this.engineName))

    return changed
  }

  private async loadOptions(force?: boolean) {
    await this.loadTimers(force)
  }

  private async clearOptions() {
    this.cfg.remove(timers.Timers, '', this.engineName)
  }
}

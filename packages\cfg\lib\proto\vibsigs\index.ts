/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  VibSignal as VibSignalModal,
  VibSigsCfg as VibSigsCfgModal
} from '../modal/vibsigs.mode'

export class VibSignal extends MapProto<VibSignal> {
  name = ''
  filter_type = 0
  sig_type = 0
  calib_mode = 0

  constructor(val?: Partial<VibSignal>) {
    super(VibSignalModal, 'VibSignal')
    val && this.assign(val)
  }
}

export class VibSigsCfg extends RootProto<VibSigsCfg> {
  signals: Array<VibSignal> = []

  constructor(val?: Partial<VibSigsCfg>) {
    super(VibSigsCfgModal, VibSigsCfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return VibSigsCfgModal.$MAX
  }

  static get minType() {
    return VibSigsCfgModal.$MIN
  }

  static get uri() {
    return URI(VibSigsCfgModal.$MAX, VibSigsCfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [VibSigsCfgModal.$MAX, VibSigsCfgModal.$MIN]
  }

  static get key() {
    return 'VibSigsCfg'
  }
}

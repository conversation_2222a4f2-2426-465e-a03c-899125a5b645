/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import * as roots from './roots'

export const kUris: Record<string, any> = {
  '1000:10': roots.audios.Audios,
  '1000:20': roots.cust.Cust,
  '1000:30': roots.entry.Entry,
  '1000:40': roots.rgbs.Rgbs,
  '1000:50': roots.versions.Versions,
  '6000:100': roots.calc.Calc,
  '6000:1000': roots.calcs.Calcs,
  '3000:1': roots.display.UBox,
  '3000:2': roots.display.ULine,
  '3000:3': roots.display.UText,
  '3000:4': roots.display.UString,
  '3000:5': roots.display.UDigital,
  '3000:6': roots.display.UBar,
  '3000:7': roots.display.UIndicator,
  '3000:8': roots.display.UGauge,
  '3000:9': roots.display.USwitch,
  '3000:10': roots.display.UButton,
  '3000:11': roots.display.UECMButton,
  '3000:12': roots.display.UPlot,
  '3000:13': roots.display.UFuncButton,
  '3000:14': roots.display.UImage,
  '3000:15': roots.display.UInput,
  '3000:100': roots.display.Display,
  '3000:1000': roots.displays.Displays,
  '2000:10': roots.attribs.Attribs,
  '2000:20': roots.calcfinal.CalcFinal,
  '2000:30': roots.calcinit.CalcInit,
  '2000:40': roots.calcmain.CalcMain,
  '2000:50': roots.calcvxi.CalcVxi,
  '2000:60': roots.crts.Crts,
  '2000:70': roots.engine.Engine,
  '2000:80': roots.main.Main,
  '2000:90': roots.questions.Questions,
  '4000:100': roots.limits.Limits,
  '10000:10': roots.plcdriver.PlcDriverCfg,
  '9000:2000': roots.psi9000.Psi9000Cfg,
  '9000:1000': roots.psicfg.PsiCfg,
  '5000:2000': roots.tablecfg.TableCfg,
  '5000:1000': roots.tables.Tables,
  '7000:100': roots.timers.Timers,
  '7000:1000': roots.vibcfg.VibCfg,
  '7000:2000': roots.vibsetup.VibSetupCfg,
  '7000:3000': roots.vibsigs.VibSigsCfg,
  '8000:10': roots.arinc429.Arinc429Cfg,
  '8000:11': roots.arinc429recv.Arinc429RecvCfg,
  '8000:12': roots.arinc429tran.Arinc429TranCfg,
  '8000:100': roots.vixcards.VxiCards,
  '8000:1000': roots.vixcfg.VixCfg,
  // ====End=====
}

export * from './roots'

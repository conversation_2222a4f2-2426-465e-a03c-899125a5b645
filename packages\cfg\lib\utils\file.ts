import fs from 'node:fs'
import path from 'node:path'

export const join = (...paths: string[]) => {
  return path.join(...paths)
}

export const copyDir = (src: string, dest: string, exclude: string[] = []) => {
  const entries = fs.readdirSync(src, { withFileTypes: true })

  fs.mkdirSync(dest, { recursive: true })
  for (const entry of entries) {
    const name = entry.name

    if (exclude.indexOf(name) > -1) {
      continue
    }
    const srcPath = path.join(src, name)
    const destPath = path.join(dest, name)

    if (entry.isDirectory()) {
      copyDir(srcPath, destPath, exclude)
    } else {
      // fs.chmodSync(destPath, 0o755)
      writeFileSync(destPath, readFile(srcPath))
      // fs.copyFileSync(srcPath, destPath)
    }
  }
}

export const writeFileSync = (
  path: string,
  contents: NodeJS.ArrayBufferView | string,
  encoding: BufferEncoding = 'utf8'
) => {
  fs.writeFileSync(path, contents, encoding)
}
const dirCache: { [dir: string]: boolean } = {}
export const writeFile = (
  filePath: string,
  contents: NodeJS.ArrayBufferView | string,
  encoding: BufferEncoding = 'utf8'
) => {
  const ensureDirs = (dirPath: string) => {
    if (dirCache[dirPath]) {
      return
    }
    dirCache[dirPath] = true

    ensureDirs(path.dirname(dirPath))
    if (fs.existsSync(dirPath)) {
      return
    }
    fs.mkdirSync(dirPath)
  }
  ensureDirs(path.dirname(filePath))
  writeFileSync(filePath, contents, encoding)
}

export const readFile = (path: string, encoding: BufferEncoding = 'utf8') => {
  return fs.readFileSync(path, encoding) || ''
}

const copied: { [fileName: string]: boolean } = {}
export const copyFile = (srcPath: string, dstPath: string) => {
  if (copied[srcPath]) {
    return
  }
  copied[srcPath] = true
  writeFile(dstPath, readFile(srcPath))
}

export async function copyFilePromise(source: string, destination: string) {
  try {
    await fs.promises.copyFile(source, destination)
  } catch (err) {
    console.error('拷贝出错:', err)
  }
}

export function copyFileSync(source: string, destination: string) {
  try {
    fs.copyFileSync(source, destination)
  } catch (err) {
    console.error('拷贝出错:', err)
  }
}

export async function copyLocalFile(
  source: string,
  destination: string,
  options: { overwrite?: boolean; errorOnExist?: boolean; preserveTimestamps?: boolean } = {}
) {
  const {
    overwrite = true, // 是否覆盖已存在的文件
    errorOnExist = false, // 如果文件存在是否抛出错误
    preserveTimestamps = true // 是否保留时间戳
  } = options

  try {
    // 检查源文件是否存在
    await fs.promises.access(source)

    // 检查目标文件是否存在
    const destExists = await fs.promises
      .access(destination)
      .then(() => true)
      .catch(() => false)

    if (destExists) {
      if (errorOnExist) {
        throw new Error('目标文件已存在')
      }
      if (!overwrite) {
        console.log('目标文件已存在，跳过拷贝')
        return false
      }
    }

    // 确保目标目录存在
    await fs.promises.mkdir(path.dirname(destination), { recursive: true })

    // 拷贝文件
    await fs.promises.copyFile(source, destination)

    if (preserveTimestamps) {
      // 复制文件的时间戳
      const stats = await fs.promises.stat(source)
      await fs.promises.utimes(destination, stats.atime, stats.mtime)
    }
    return true
  } catch (err) {
    console.error('拷贝出错:', err)
    return false
  }
}

export const isDirectory = (path: string) => {
  const stats = fs.statSync(path)
  return !!stats?.isDirectory()
}

export const existed = (path: string) => {
  return fs.existsSync(path)
}

export const rename = (oldPath: string, newPath: string) => {
  fs.renameSync(oldPath, newPath)
}

export type ScanDirResult = { type: string; path: string; name: string; entry: string }[]
export const scanDir = (
  dirPath: string,
  recurse = true,
  exclude: string[] = [],
  exts: string[] = []
): ScanDirResult => {
  const entries = fs.readdirSync(dirPath)
  const result: ScanDirResult = []
  for (const entry of entries) {
    const ext = path.extname(entry)
    const name = entry.replace(ext, '')
    const type = ext.substring(1)
    const filePath = path.join(dirPath, entry)
    const stats = fs.statSync(filePath)
    if (stats.isDirectory() && recurse) {
      result.push(...scanDir(filePath, recurse, exclude, exts))
    } else if (exclude.indexOf(type) < 0 && (!exts.length || exts.includes(ext))) {
      result.push({ type, path: filePath, name, entry })
    }
  }

  return result
}

export const fileName = (name: string) => {
  name = path.basename(name)
  const ext = path.extname(name)
  return name.replace(ext, '')
}

export const baseName = (filePath: string) => {
  return path.basename(filePath)
}

export const formatFileTitle = (str: string) => {
  return str
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

export const formatFileName = (str: string) => {
  return str
    .replaceAll('_', ' ')
    .split(' ')
    .filter(it => !!it)
    .map(word => word.toLowerCase())
    .join('_')
}

export const removeFile = (fileName: string) => {
  fs.unlinkSync(fileName)
}

export const removeDirSync = (dirPath: string) => {
  fs.rmSync(dirPath, { recursive: true })
}

export const removeDir = (dirPath: string) => {
  if (!existed(dirPath)) {
    return
  }
  const entries = fs.readdirSync(dirPath, { withFileTypes: true })
  for (const entry of entries) {
    const srcPath = path.join(dirPath, entry.name)
    if (entry.isDirectory()) {
      removeDir(srcPath)
    } else {
      removeFile(srcPath)
    }
  }
  removeDirSync(dirPath)
}

export const makeDir = (dirPath: string) => {
  if (existed(dirPath)) {
    return true
  }
  if (makeDir(path.dirname(dirPath))) {
    fs.mkdirSync(dirPath)
    return true
  }
  return false
}

import { <PERSON><PERSON>ethod, <PERSON><PERSON><PERSON><PERSON>, MethodPicks } from '../base'
import { IBaseProxy } from './type'

export type BaseConfig = Record<string, any>

export abstract class IEmiter {
  abstract emit(event: string, ...args: any[]): boolean
  abstract on(event: string, fn: (...args: any[]) => void): this
  abstract off(event: string, fn: (...args: any[]) => void): this

  static get EMIT() {
    return 'emit'
  }

  static get ON() {
    return 'on'
  }

  static get OFF() {
    return 'off'
  }
}

export abstract class IBase<T extends IBaseProxy = object, K = string> extends IEmiter {
  abstract get proxy(): T
  abstract method(name: K, invoker: object, method: IMethod): void
  abstract property(name: K, value: any): void

  static get NAME() {
    return 'Base'
  }
}

export type BaseMtr<T extends IBase> = Exclude<MethodPicks<IBase, T>, MethodNames<IBase>>
export type BaseItr<T extends IBase> = Omit<T, 'emit' | 'proxy' | 'method' | 'property'>

export const IpcEvent = (name: string, key: string) => `${name}:${key}`

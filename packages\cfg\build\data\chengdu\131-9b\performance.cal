$GROUP "performance" 
#if (compare(RepairType, "Heavy") & PerType = 0) 
WbSpe: 155.0
PbSpe: 51.2 
TbSpe: 445
WfSpe: 0
EgtSpe: 1090
#elseif (compare(RepairType, "Heavy") & PerType = 1) 
WbSpe: 0
PbSpe: 54.5
TbSpe: 445
WfSpe: 0
EgtSpe: 1080
#elseif (~compare(RepairType, "Heavy") & PerType = 0) 
WbSpe: 155.0
PbSpe: 51.2 
TbSpe: 445
WfSpe: 0
EgtSpe: 1115
#elseif (~compare(RepairType, "Heavy") & PerType = 1) 
WbSpe: 155.0
PbSpe: 53.7
TbSpe: 445
WfSpe: 0
EgtSpe: 1105
#else
WbSpe: 0
PbSpe: 0
TbSpe: 0
WfSpe: 0
EgtSpe: 0
#endif

Delta: PBar/29.92

#if (PerType = 0) 
DelWb: lookup(DeltaECSWB_T)
DelPb: lookup(DeltaECSPB_T)
DelTb: lookup(DeltaECSTB_T)
DelWf: lookup(DeltaECSWF_T)
DelEgt: lookup(DeltaECSEGT_T)
#elseif (PerType = 1) 
DelWb: lookup(DeltaMESWB_T)
DelPb: lookup(DeltaMESPB_T)
DelTb: lookup(DeltaMESTB_T)
DelWf: lookup(DeltaMESWF_T)
DelEgt: lookup(DeltaMESEGT_T)
#else 
DelWb: 0
DelPb: 0
DelTb: 0
DelWf: 0
DelEgt: 0
#endif

PbReq: PbSpe

WbCor: WB / Delta + DelWb - 4
PbCor: CalibPB / Delta + DelPb - 1.6
TbCor: TBAvg + DelTb
WfCor: (WFAvg * FLHV / Delta * 18550) + DelWf + 8 * ((CalibPCell - PS9Psia) / Delta) + 0.6 - 4 * (PbCor - PbReq) 
EgtCor: EGTAvg + DelEgt + 18 + 33 * ((CalibPCell - PS9Psia) / Delta) + 30  - 10 * (PbCor - PbReq)

#if (PerType = 0) 
WbMargin: WbCor - WbSpe
PbMargin: PbCor - PbSpe
TbMargin: TbSpe - TbCor 
WfMargin: 0 
EgtMargin: EgtSpe - EgtCor 
#elseif (PerType = 1) 
WbMargin: 0
PbMargin: PbCor - PbSpe
TbMargin: TbSpe - TbCor 
WfMargin: WfSpe - WfCor
EgtMargin: EgtSpe - EgtCor  
#else 
WbMargin: 0
PbMargin: 0
TbMargin: 0 
WfMargin: 0
EgtMargin: 0  
#endif

$END-GROUP

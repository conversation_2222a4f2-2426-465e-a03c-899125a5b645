import { base64, BaseProto, ProtoClass, utf8 } from '@wuk/wkp'
import { exec } from 'child_process'
import { cfg } from '../proto'
import { Path } from './path'
import { existed, writeFileSync } from './file'

const isWin = () => (process?.env?.NODE_PLAT || process.platform) === 'win32'

export class Cmd {
  private static kExeName = 'cfg-reader'
  private static READ = '-r'
  private static WRITE = '-w'
  private static WRITEF = '-wf'
  private static RTEXT = '-t'
  private static VERIFY = '-v'
  private static VERIFYF = '-vf'
  private static SAVE = '-s'
  private static WSAVE = '-ws'
  private static kMaxLen = 6000

  static async read<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    customer?: string,
    engineName?: string,
    fileName = ''
  ) {
    let rsp: T | undefined = undefined
    let code = -1
    let cfg_name = fileName
    let file_path = ''
    try {
      const exe = Path.join(Path.toolsDir, this.exeName)
      customer = customer?.toLowerCase()
      file_path = Path.join(Path.dataDir, customer || '', engineName || '')
      let cmd = `"${exe}" "${file_path}" ${Cmd.READ} ${cls.uri}`
      fileName && (cmd = `${cmd} "${Path.join('/', fileName)}"`)
      const data = await Cmd.invoke(cmd)
      const bytes = base64.decode(data)
      const packet = new cfg.CfgPacket()
      packet.unmarshal(bytes)
      code = packet.code
      if (code === 0) {
        rsp = new cls()
        rsp.unmarshal(packet.data)
      }
      cfg_name = packet.cfg_name

      console.info(
        `Cmd=======read uri=${cls.uri} customer=${customer} engineName=${engineName} fileName=${fileName} code=${code}`
      )
    } catch (error) {
      console.info(`Cmd=======read error uri=${cls.uri} error=${error}`)
      rsp = undefined
      code = -200
    }

    return { code, rsp, cfg_name, file_path }
  }

  static async text<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    customer?: string,
    engineName?: string,
    fileName = ''
  ) {
    let rsp: string | undefined = undefined
    let code = -1
    let cfg_name = fileName
    let file_path = ''
    try {
      const exe = Path.join(Path.toolsDir, this.exeName)
      customer = customer?.toLowerCase()
      file_path = Path.join(Path.dataDir, customer || '', engineName || '')
      let cmd = `"${exe}" "${file_path}" ${Cmd.RTEXT} ${cls.uri}`
      fileName && (cmd = `${cmd} "${Path.join('/', fileName)}"`)
      const data = await Cmd.invoke(cmd)
      const bytes = base64.decode(data)
      const packet = new cfg.CfgPacket()
      packet.unmarshal(bytes)
      code = packet.code
      if (code === 0) {
        rsp = utf8.encode(packet.data)
      }
      cfg_name = packet.cfg_name

      console.info(
        `Cmd=======text uri=${cls.uri} customer=${customer} engineName=${engineName} fileName=${fileName} code=${code}`
      )
    } catch (error) {
      console.info(`Cmd=======text error uri=${cls.uri} error=${error}`)
      rsp = undefined
      code = -200
    }

    return { code, rsp, cfg_name, file_path }
  }

  static async write<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    val: Partial<T>,
    customer?: string,
    engineName?: string,
    fileName = ''
  ) {
    let code = -1
    let file_path = ''
    let cfg_name = fileName
    try {
      const req = new cls()
      const data = req.marshal(val)
      const uri = cls.uri
      let packet = new cfg.CfgPacket()
      let bytes = packet.marshal({ data, uri })

      let b64 = base64.encode(bytes)
      let key = Cmd.WRITE
      const toolsDir = Path.toolsDir
      if (isWin() && b64.length >= Cmd.kMaxLen) {
        const fileName = `content_${uri.replace(':', '_')}.temp`
        const cachFile = Path.join(toolsDir, fileName)
        writeFileSync(cachFile, b64)
        if (existed(cachFile)) {
          b64 = cachFile
          key = Cmd.WRITEF
        }
      }

      const exe = Path.join(toolsDir, this.exeName)
      customer = customer?.toLowerCase()
      file_path = Path.join(Path.dataDir, customer || '', engineName || '')
      let cmd = `"${exe}" "${file_path}" ${key} ${cls.uri}`
      fileName && (cmd = `${cmd} "${Path.join('/', fileName)}"`)
      cmd = `${cmd} ${b64}`
      b64 = await Cmd.invoke(cmd)

      bytes = base64.decode(b64)
      packet = new cfg.CfgPacket()
      packet.unmarshal(bytes)
      code = packet.code
      cfg_name = packet.cfg_name

      console.info(
        `Cmd=======write uri=${cls.uri} customer=${customer} engineName=${engineName} fileName=${fileName} code=${code}`
      )
    } catch (error) {
      console.info(`Cmd=======write error uri=${cls.uri} error=${error}`)
      code = -200
    }

    return { code, file_path, cfg_name }
  }

  static async save<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    data: string,
    customer?: string,
    engineName?: string,
    fileName = ''
  ) {
    let code = -1
    let file_path = ''
    let cfg_name = fileName
    try {
      const uri = cls.uri
      let packet = new cfg.CfgPacket()
      packet.data = utf8.decode(data)
      let bytes = packet.marshal({ uri })

      let b64 = base64.encode(bytes)
      let key = Cmd.SAVE
      const toolsDir = Path.toolsDir
      if (isWin() && b64.length >= Cmd.kMaxLen) {
        const fileName = `content_${uri.replace(':', '_')}.temp`
        const cachFile = Path.join(toolsDir, fileName)
        writeFileSync(cachFile, b64)
        if (existed(cachFile)) {
          b64 = cachFile
          key = Cmd.WSAVE
        }
      }

      const exe = Path.join(toolsDir, this.exeName)
      customer = customer?.toLowerCase()
      file_path = Path.join(Path.dataDir, customer || '', engineName || '')
      let cmd = `"${exe}" "${file_path}" ${key} ${cls.uri}`
      fileName && (cmd = `${cmd} "${Path.join('/', fileName)}"`)
      cmd = `${cmd} ${b64}`
      b64 = await Cmd.invoke(cmd)

      bytes = base64.decode(b64)
      packet = new cfg.CfgPacket()
      packet.unmarshal(bytes)
      code = packet.code
      cfg_name = packet.cfg_name

      console.info(
        `Cmd=======save uri=${cls.uri} customer=${customer} engineName=${engineName} fileName=${fileName} code=${code}`
      )
    } catch (error) {
      console.info(`Cmd=======save error uri=${cls.uri} error=${error}`)
      code = -200
    }

    return { code, file_path, cfg_name }
  }

  private static invoke(cmd: string): Promise<string> {
    return new Promise((resolve, reject) => {
      console.info('Cmd invoke====', cmd)
      exec(cmd, (error, stdout, stderr) => {
        if (error || stderr) {
          reject('Invoke error')
        } else {
          resolve(stdout || '')
        }
      })
    })
  }

  private static get exeName() {
    let result = Cmd.kExeName
    if (isWin()) {
      result += '.exe'
    }

    return result
  }
}

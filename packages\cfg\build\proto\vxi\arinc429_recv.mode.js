import { types } from '@wuk/wkp'

export const BitValue = {
  $keys: ['name', 'bit_num', 'val_0', 'val_1', 'lable'],

  name: types.string,
  bit_num: types.int32,
  val_0: types.string,
  val_1: types.string,
  lable: types.string
}

export const RangeValue = {
  $keys: ['min', 'max', 'unit'],

  min: types.double,
  max: types.double,
  unit: types.string
}

export const ArincRecvSignal = {
  $keys: ['name', 'label', 'data', 'bin_range', 'sig_bits', 'rate', 'rate_limit', 'signal_range', 'bits'],

  name: types.string,
  label: types.string,
  data: types.string,
  bin_range: types.double,
  sig_bits: types.int32,
  rate: types.int32,
  rate_limit: types.int32,
  signal_range: RangeValue,
  bits: types.arrayOf(BitValue)
}

export const Arinc429RecvCfg = {
  $MAX: 8000,
  $MIN: 11,
  $keys: ['channel', 'eec', 'speed', 'parity', 'active', 'recv_signals'],

  channel: types.int32,
  eec: types.string,
  speed: types.string,
  parity: types.string,
  active: types.string,
  
  recv_signals: types.arrayOf(ArincRecvSignal)
}

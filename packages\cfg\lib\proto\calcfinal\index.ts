/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  IncludeFile as IncludeFileModal,
  CalcFinal as CalcFinalModal
} from '../modal/calcfinal.mode'

export class IncludeFile extends MapProto<IncludeFile> {
  file = ''
  uri = ''
  depends: Array<string> = []

  constructor(val?: Partial<IncludeFile>) {
    super(IncludeFileModal, 'IncludeFile')
    val && this.assign(val)
  }
}

export class CalcFinal extends RootProto<CalcFinal> {
  includes: Array<IncludeFile> = []

  constructor(val?: Partial<CalcFinal>) {
    super(CalcFinalModal, CalcFinal.key)
    val && this.assign(val)
  }

  static get maxType() {
    return CalcFinalModal.$MAX
  }

  static get minType() {
    return CalcFinalModal.$MIN
  }

  static get uri() {
    return URI(CalcFinalModal.$MAX, CalcFinalModal.$MIN)
  }

  static get types(): [number, number] {
    return [CalcFinalModal.$MAX, CalcFinalModal.$MIN]
  }

  static get key() {
    return 'CalcFinal'
  }
}

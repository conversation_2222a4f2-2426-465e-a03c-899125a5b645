/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  VibSystem as VibSystemModal,
  VibInput as VibInputModal,
  VibBandFilter as VibBandFilterModal,
  VibTachInput as VibTachInputModal,
  VibTFInput as VibTFInputModal,
  VibSpectrum as VibSpectrumModal,
  VibSetupCfg as VibSetupCfgModal
} from '../modal/vibsetup.mode'

export class VibSystem extends MapProto<VibSystem> {
  measurement_rate = 0
  band_width = 0
  fft_lines = 0

  constructor(val?: Partial<VibSystem>) {
    super(VibSystemModal, 'VibSystem')
    val && this.assign(val)
  }
}

export class VibInput extends MapProto<VibInput> {
  index = 0
  sensitivity = 0.0
  gain = 0
  input_range = 0.0
  transducer_type = 0
  ad_gain = 0
  fft_size = 0
  sampling_rate = 0

  constructor(val?: Partial<VibInput>) {
    super(VibInputModal, 'VibInput')
    val && this.assign(val)
  }
}

export class VibBandFilter extends MapProto<VibBandFilter> {
  index = 0
  vib_channel = 0
  output_units = 0
  detector_type = 0
  time_constant = 0
  filter_shape = 0
  filter_type = 0
  upper_cutoff = 0
  lower_cutoff = 0

  constructor(val?: Partial<VibBandFilter>) {
    super(VibBandFilterModal, 'VibBandFilter')
    val && this.assign(val)
  }
}

export class VibTachInput extends MapProto<VibTachInput> {
  index = 0
  tach_processing = 0
  rotor_speed = 0.0
  tach_freq = 0.0
  sensitivity = 0.0

  constructor(val?: Partial<VibTachInput>) {
    super(VibTachInputModal, 'VibTachInput')
    val && this.assign(val)
  }
}

export class VibTFInput extends MapProto<VibTFInput> {
  index = 0
  vib_channel = 0
  tach_channel = 0
  output_units = 0
  detector_type = 0
  full_scale_units = 0.0
  full_scale_volts = 0.0
  filter_specification = 0
  filter_q = 0
  band_width = 0
  time_constant = 0
  order_tracking = 0

  constructor(val?: Partial<VibTFInput>) {
    super(VibTFInputModal, 'VibTFInput')
    val && this.assign(val)
  }
}

export class VibSpectrum extends MapProto<VibSpectrum> {
  index = 0
  output_units = 0
  start_frequency = 0
  end_frequency = 0
  detector_type = 0
  full_scale_units = 0.0

  constructor(val?: Partial<VibSpectrum>) {
    super(VibSpectrumModal, 'VibSpectrum')
    val && this.assign(val)
  }
}

export class VibSetupCfg extends RootProto<VibSetupCfg> {
  system = new VibSystem()
  vib_inputs: Array<VibInput> = []
  band_filters: Array<VibBandFilter> = []
  tach_inputs: Array<VibTachInput> = []
  tf_inputs: Array<VibTFInput> = []
  spectrums: Array<VibSpectrum> = []

  constructor(val?: Partial<VibSetupCfg>) {
    super(VibSetupCfgModal, VibSetupCfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return VibSetupCfgModal.$MAX
  }

  static get minType() {
    return VibSetupCfgModal.$MIN
  }

  static get uri() {
    return URI(VibSetupCfgModal.$MAX, VibSetupCfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [VibSetupCfgModal.$MAX, VibSetupCfgModal.$MIN]
  }

  static get key() {
    return 'VibSetupCfg'
  }
}

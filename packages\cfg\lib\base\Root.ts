import { Emitter } from './Emitter'
import { Invoke } from './Invoke'

export class Root extends Invoke {
  protected _modules: Emitter[]

  constructor(name: string) {
    super(name)

    this._modules = []
  }

  override get valid(): boolean {
    return false
  }

  override async init(...args: any[]) {
    this.log('init')
    this._modules.forEach(async module => {
      await module.init()
    })
    await super.init(...args)
  }

  override async destroy() {
    this._modules.forEach(async module => {
      await module.destroy()
    })
    await super.destroy()

    this.log('destroy')
  }

  override async start() {
    this.log('start')
    this._modules.forEach(async module => {
      await module.start()
    })
    await super.start()
  }

  override async stop() {
    this._modules.forEach(async module => {
      await module.stop()
    })
    await super.stop()
    this.log('stop')
  }

  override log(fmt = '', ...args: any[]) {
    // this._win?.log(this._name, fmt, ...args)
  }
}

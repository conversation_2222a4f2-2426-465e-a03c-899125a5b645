import { types } from '@wuk/wkp'

export const Lineable = {
  $keys: ['comments', 'type', 'name', 'unit', 'value'],

  comments: types.arrayOf(types.string),
  type: types.int32,
  name: types.string,
  unit: types.string,
  value: types.string
}

export const Groupable = {
  $keys: ['comments', 'name', 'excute', 'lines'],

  comments: types.arrayOf(types.string),
  name: types.string,
  excute: types.int32,
  lines: types.arrayOf(Lineable)
}

export const Calc = {
  $MAX: 6000,
  $MIN: 100,
  $keys: ['group'],

  group: Groupable
}

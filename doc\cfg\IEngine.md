# IEngine 接口文档

## 相关文档
- [类型定义](type.md) - 包含 `ADFullScaleType`、`AmplifierGainType`、`BandwidthType`、`BroadbanFilterType`、`BroadFilterType`、`CalcsExcuteType`、`CalcsSignalType`、`DetectorType`、`DisplayModeType`、`FFTLinesType`、`FilterQType`、`FilterShapeType`、`FilterSpecType`、`InputType`、`MeasurementRateType`、`OutputUnitsType`、`SignalCalibMode`、`SignalFilterType`、`SignalSigType`、`TachometerInputType`、`TestModeType`、`TimeConstantType`、`TimerType`、`TransducerType`、`WhenToExcuteType` 等类型定义
- [IBase](IBase.md) - 基础接口
- [IDisplay](IDisplay.md) - 显示接口

## 概述
`IEngine` 是一个抽象类，继承自 `IBase<IEngine>`，用于定义引擎的基本功能和配置管理。它提供了引擎选项、显示配置、振动信号、PLC设置等功能的管理。

## 类型定义

### 引擎选项
```typescript
export interface EngineOptions {
  /**
   * Test Mode
   */
  test_mode: TestModeType
  /**
   * Crs On Param
   */
  crs_on_param: string
  /**
   * Run Limits Param
   */
  run_limits_raram: string
  /**
   * PLA Idle Default (degs)
   */
  pla_idle_default: string
  /**
   * PLA Takeoff Default (degs)
   */
  pla_takeoff_default: string
}
```

### EEC 列表选项
```typescript
export interface EECListOption {
  /**
   * EEC Prompt String
   */
  eec_prompt_string: string
  /**
   * EEC Prompt String
   */
  list: string[]
}
```

### 仪表盘编号项
```typescript
export interface DashNumberItem {
  /**
   * Dash Number Name
   */
  name: string
  /**
   * Dash EEC KEY
   */
  eec_key: string
  /**
   * EEC List
   */
  option: EECListOption
}
```

### 仪表盘选项
```typescript
export interface DashOptions {
  /**
   * Dash Number List
   */
  list: DashNumberItem[]
}
```

### 显示四分区选项
```typescript
export interface DisplayQuadOption {
  /**
   * Position
   */
  position: number
  /**
   * Quad Name
   */
  quad_name: string
  /**
   * Change Display Mode
   */
  change_display_mode: DisplayModeType
  /**
   * list
   */
  list: string[]
}
```

### 显示四分区项
```typescript
export interface DisplayQuadItem {
  /**
   * Name
   */
  name: string
  /**
   * Quad option
   */
  quads: DisplayQuadOption[]
}
```

### 显示选项
```typescript
export interface DisplayOptions {
  /**
   * Display list
   */
  list: DisplayQuadItem[]
}
```

### 振动信号项
```typescript
export interface VibSignalItem {
  /**
   * Sig Name
   */
  sig_name: string
  /**
   * Filter Type
   */
  filter_type: SignalFilterType
  /**
   * Sig Type
   */
  sig_type: SignalSigType
  /**
   * Calib Mode
   */
  calib_mode: SignalCalibMode
}
```

### 振动信号
```typescript
export interface VibrationSignals {
  /**
   * Signals
   */
  list: VibSignalItem[]
}
```

### 振动系统设置
```typescript
export interface VibrationSystemSetup {
  /**
   * Measurement Rate
   */
  measurement_rate: MeasurementRateType
  /**
   * Bandwidth
   */
  bandwidth: BandwidthType
  /**
   * FFT Lines
   */
  fft_lines: FFTLinesType
}
```

### 振动输入设置
```typescript
export interface VibrationInputSetup {
  /**
   * Vibration Input
   */
  vibration_input: InputType
  /**
   * Sensitivity (pC/g)
   */
  sensitivity: string
  /**
   * Full Scale Input (g's)
   */
  full_scale_input: string
  /**
   * Transducer Type
   */
  transducer_type: TransducerType
  /**
   * Amplifier Gain
   */
  amplifier_gain: AmplifierGainType
  /**
   * A/D Full Scale
   */
  ad_full_scale: ADFullScaleType
}
```

### 宽带滤波器设置
```typescript
export interface BroadbandFilterSetup {
  /**
   * Broadban Filter
   */
  broadban_filter: BroadbanFilterType
  /**
   * Vibration Input
   */
  vibration_input: InputType
  /**
   * Upper Cutoff
   */
  upper_cutoff: string
  /**
   * Lower Cutoff
   */
  lower_cutoff: string
  /**
   * Output Units
   */
  output_units: OutputUnitsType
  /**
   * Filter Type
   */
  filter_type: BroadFilterType
  /**
   * Filter Shape
   */
  filter_shape: FilterShapeType
  /**
   * Time Constant
   */
  time_constant: TimeConstantType
}
```

### 转速计输入设置
```typescript
export interface TachometerInputSetup {
  /**
   * Tachometer Input
   */
  tachometer_input: TachometerInputType
  /**
   * Vibration Input
   */
  vibration_input: InputType
  /**
   * Tachometer 1:100% Rotor Speed (RPM) (enter 0 to disable)
   */
  tachometer_rotor_speed: string
  /**
   * Tachometer 1:100% Frequency (Hz)
   */
  tachometer_frequency: string
  /**
   * Tachometer 1:Sensitivity(V)
   */
  tachometer_sensitivity: string
}
```

### 频谱分析设置
```typescript
export interface SpectrumAnalysisSetup {
  /**
   * Vibration Input
   */
  vibration_input: InputType
  /**
   * Output Units
   */
  output_units: OutputUnitsType
  /**
   * Detector Type
   */
  detector_type: DetectorType
  /**
   * Sart Frequency
   */
  sart_frequency: string
  /**
   * End Frequency
   */
  end_frequency: string
  /**
   * Full Scale Output
   */
  full_scale_output: string
}
```

### 跟踪滤波器设置
```typescript
export interface TrackingFilterSetup {
  /**
   * Tracking Filter
   */
  tracking_filter: InputType
  /**
   * Vibration Input
   */
  vibration_input: InputType
  /**
   * Output Units
   */
  output_units: OutputUnitsType
  /**
   * Tachometer Input
   */
  tachometer_input: TachometerInputType
  /**
   * Filter Spec.
   */
  filter_spec: FilterSpecType
  /**
   * Detector Type
   */
  detector_type: DetectorType
  /**
   * Filter Q.
   */
  filter_q: FilterQType
  /**
   * Time Constant
   */
  time_constant: TimeConstantType
  /**
   * Order Tracking
   */
  order_tracking: string
}
```

### 属性项
```typescript
export interface AttributeItem {
  /**
   * name
   */
  name: string
  /**
   * Units
   */
  units: string
  /**
   * Long Name
   */
  long_name: string
  /**
   * Display Format width max 8
   */
  width: number
  /**
   * Display Format precision max 8
   */
  precision: number
}
```

### 属性选项
```typescript
export interface AttributeOptions {
  list: AttributeItem[]
}
```

### PLC 设置项
```typescript
export interface PLCSetupItem {
  /**
   * Device Name
   */
  device_name: string
  /**
   * Scan Rate
   */
  scan_rate: number
  /**
   * PLC Type
   */
  plc_type: string
  /**
   * Engine Specific
   */
  engine_specific: string
  /**
   * Test Mode
   */
  test_mode: TestModeType
}
```

### PLC 设置选项
```typescript
export interface PLCSetupOptions {
  list: PLCSetupItem[]
}
```

### 计算项列表项
```typescript
export interface CalsItemListItem {
  /**
   * Name
   */
  name: string
  /**
   * Equation
   */
  equation: string
  /**
   * Comments
   */
  comments: string
  /**
   * Units
   */
  units: string
}
```

### 计算项选项
```typescript
export interface CalsItemOption {
  /**
   * Table Data
   */
  list: CalsItemListItem[]
  /**
   * Text Data
   */
  text: string
}
```

### 计算公共项
```typescript
export interface CalsCommonItem {
  /**
   * Group Name
   */
  group_name: string
  /**
   * When To Excute
   */
  when_to_excute: WhenToExcuteType
  /**
   * Type
   */
  type: CalcsExcuteType
  /**
   * Option
   */
  option: CalsItemOption
}
```

### 初始计算选项
```typescript
export interface CalcsInitialOptions {
  list: CalsCommonItem[]
}
```

### 最终计算选项
```typescript
export interface CalcsFinalOptions {
  list: CalsCommonItem[]
}
```

### 信号计算项
```typescript
export interface CalsSignalItem {
  /**
   * Group Name
   */
  group_name: string
  /**
   * Type
   */
  type: CalcsSignalType
  /**
   * Option
   */
  option: CalsItemOption
}
```

### 信号计算选项
```typescript
export interface CalcsSignalOptions {
  list: CalsSignalItem[]
}
```

### 表格项选项
```typescript
export interface TableItemOption {
  /**
   * Text Data
   */
  text: string
}
```

### 表格项
```typescript
export interface TableItem {
  /**
   * Group Name
   */
  group_name: string
  /**
   * Equation
   */
  equation: string
  /**
   * Option
   */
  option: TableItemOption
}
```

### 表格选项
```typescript
export interface TablesOptions {
  list: TableItem[]
}
```

### 定时器项
```typescript
export interface TimerItem {
  /**
   * Name
   */
  name: string
  /**
   * Equation
   */
  equation: string
  /**
   * Type
   */
  type: TimerType
}
```

### 定时器选项
```typescript
export interface TimersOptions {
  list: TimerItem[]
}
```

### 显示文件项
```typescript
export interface DisplayFileItem {
  /**
   * Name
   */
  name: string
  /**
   * Description
   */
  description: string
  /**
   * File
   */
  file: string
}
```

### 显示定义选项
```typescript
export interface DisplayDefineOptions {
  files: DisplayFileItem[]
}
```

## 接口定义
### IEngine 抽象类
```typescript
export abstract class IEngine extends IBase<IEngine> {
  abstract readEngineOptions(): Promise<EngineOptions>
  abstract writeEngineOptions(val: Partial<EngineOptions>): Promise<boolean>

  abstract readDashOptions(): Promise<DashOptions>
  abstract removeDash(index: number): Promise<boolean>
  abstract addDash(val: DashNumberItem, index?: number): Promise<boolean>
  abstract modifyDash(index: number, val: Partial<DashNumberItem>): Promise<boolean>

  abstract readDisplayOptions(): Promise<DisplayOptions>
  abstract removeDisplayQuad(index: number, quadIdx?: number): Promise<boolean>
  abstract addDisplayCrt(name: string, index?: number): Promise<boolean>
  abstract addDisplayQuad(val: DisplayQuadOption, index: number, quadIdx?: number): Promise<boolean>
  abstract modifyDisplayCrt(index: number, name: string): Promise<boolean>
  abstract modifyDisplayQuad(
    index: number,
    val: Partial<DisplayQuadOption>,
    quadIdx: number
  ): Promise<boolean>

  abstract readDisplayDefinedOptions(): Promise<DisplayDefineOptions>
  abstract removeDisplayFile(index: number): Promise<boolean>
  abstract addDisplayFile(val: DisplayFileItem, index?: number): Promise<boolean>
  abstract modifyDisplayFile(index: number, val: Partial<DisplayFileItem>): Promise<boolean>

  abstract loadDisplay(index: number): Promise<UDisplay | undefined>
  abstract modifyDisplay(index: number, val: Partial<UDisplay>): Promise<boolean>
  abstract saveDisplay(index: number): Promise<boolean>

  abstract readVibrationSignals(): Promise<VibrationSignals>
  abstract removeSignals(index: number): Promise<boolean>
  abstract addSignals(val: VibSignalItem, index?: number): Promise<boolean>
  abstract modifySignals(index: number, val: Partial<VibSignalItem>): Promise<boolean>

  abstract readVibrationSystemSetup(): Promise<VibrationSystemSetup>
  abstract writeVibrationSystemSetup(val: Partial<VibrationSystemSetup>): Promise<boolean>

  abstract readVibrationInputSetup(): Promise<VibrationInputSetup>
  abstract writeVibrationInputSetup(val: Partial<VibrationInputSetup>): Promise<boolean>

  abstract readBroadbandFilterSetup(): Promise<BroadbandFilterSetup>
  abstract writeBroadbandFilterSetup(val: Partial<BroadbandFilterSetup>): Promise<boolean>

  abstract readTachometerInputSetup(): Promise<TachometerInputSetup>
  abstract writeTachometerInputSetup(val: Partial<TachometerInputSetup>): Promise<boolean>

  abstract readSpectrumAnalysisSetup(): Promise<SpectrumAnalysisSetup>
  abstract writeSpectrumAnalysisSetup(val: Partial<SpectrumAnalysisSetup>): Promise<boolean>

  abstract readTrackingFilterSetup(): Promise<TrackingFilterSetup>
  abstract writeTrackingFilterSetup(val: Partial<TrackingFilterSetup>): Promise<boolean>

  abstract readAttributes(): Promise<AttributeOptions>
  abstract removeAttribute(index: number): Promise<boolean>
  abstract addAttribute(val: AttributeItem, index?: number): Promise<boolean>
  abstract modifyAttribute(index: number, val: Partial<AttributeItem>): Promise<boolean>

  abstract readPLCSetupOptions(): Promise<PLCSetupOptions>
  abstract removePLCSetup(index: number): Promise<boolean>
  abstract addPLCSetup(val: PLCSetupItem, index?: number): Promise<boolean>
  abstract modifyPLCSetup(index: number, val: Partial<PLCSetupItem>): Promise<boolean>

  abstract readCalcsInitialOptions(): Promise<CalcsInitialOptions>
  abstract removeCalcsInitial(index: number): Promise<boolean>
  abstract addCalcsInitial(val: CalsCommonItem, index?: number): Promise<boolean>
  abstract modifyCalcsInitial(index: number, val: Partial<CalsCommonItem>): Promise<boolean>

  abstract readCalcsFinalOptions(): Promise<CalcsFinalOptions>
  abstract removeCalcsFinal(index: number): Promise<boolean>
  abstract addCalcsFinal(val: CalsCommonItem, index?: number): Promise<boolean>
  abstract modifyCalcsFinal(index: number, val: Partial<CalsCommonItem>): Promise<boolean>

  abstract readCalcsSignalOptions(): Promise<CalcsSignalOptions>
  abstract removeCalcsSignal(index: number): Promise<boolean>
  abstract addCalcsSignal(val: CalsSignalItem, index?: number): Promise<boolean>
  abstract modifyCalcsSignal(index: number, val: Partial<CalsSignalItem>): Promise<boolean>

  abstract readTablesOptions(): Promise<TablesOptions>
  abstract removeTable(index: number): Promise<boolean>
  abstract addTable(val: TableItem, index?: number): Promise<boolean>
  abstract modifyTable(index: number, val: Partial<TableItem>): Promise<boolean>

  abstract readTimersOptions(): Promise<TimersOptions>
  abstract removeTimer(index: number): Promise<boolean>
  abstract addTimer(val: TimerItem, index?: number): Promise<boolean>
  abstract modifyTimer(index: number, val: Partial<TimerItem>): Promise<boolean>

  static override get NAME() { return 'Engine' }
  static get OnEngineOptions() { return 'Engine.OnEngineOptions' }
  static get OnDashOptions() { return 'Engine.OnDashOptions' }
  static get OnDisplayOptions() { return 'Engine.OnDisplayOptions' }
  static get OnDisplayDefineOptions() { return 'Engine.OnDisplayDefineOptions' }
  static get OnDisplayFileOptions() { return 'Engine.OnDisplayFileOptions' }
  static get OnVibrationSignals() { return 'Engine.OnVibrationSignals' }
  static get OnVibrationSystemSetup() { return 'Engine.OnVibrationSystemSetup' }
  static get OnVibrationInputSetup() { return 'Engine.OnVibrationInputSetup' }
  static get OnBroadbandFilterSetup() { return 'Engine.OnBroadbandFilterSetup' }
  static get OnTachometerInputSetup() { return 'Engine.OnTachometerInputSetup' }
  static get OnSpectrumAnalysisSetup() { return 'Engine.OnSpectrumAnalysisSetup' }
  static get OnTrackingFilterSetup() { return 'Engine.OnTrackingFilterSetup' }
  static get OnAttributeOptions() { return 'Engine.OnAttributeOptions' }
  static get OnPLCSetupOptions() { return 'Engine.OnPLCSetupOptions' }
  static get OnCalcsInitialOptions() { return 'Engine.OnCalcsInitialOptions' }
  static get OnCalcsFinalOptions() { return 'Engine.OnCalcsFinalOptions' }
  static get OnCalcsSignalOptions() { return 'Engine.OnCalcsSignalOptions' }
  static get OnTablesOptions() { return 'Engine.OnTablesOptions' }
  static get OnTimersOptions() { return 'Engine.OnTimersOptions' }
}
```

## 方法说明

### 引擎选项管理
- `readEngineOptions`: 读取引擎选项
- `writeEngineOptions`: 写入引擎选项

### 仪表盘管理
- `readDashOptions`: 读取仪表盘选项
- `removeDash`: 移除仪表盘
- `addDash`: 添加仪表盘
- `modifyDash`: 修改仪表盘

### 显示管理
- `readDisplayOptions`: 读取显示选项
- `removeDisplayQuad`: 移除显示四分区
- `addDisplayCrt`: 添加显示CRT
- `addDisplayQuad`: 添加显示四分区
- `modifyDisplayCrt`: 修改显示CRT
- `modifyDisplayQuad`: 修改显示四分区

### 显示定义管理
- `readDisplayDefinedOptions`: 读取显示定义选项
- `removeDisplayFile`: 移除显示文件
- `addDisplayFile`: 添加显示文件
- `modifyDisplayFile`: 修改显示文件

### 显示操作
- `loadDisplay`: 加载显示
- `modifyDisplay`: 修改显示
- `saveDisplay`: 保存显示

### 振动信号管理
- `readVibrationSignals`: 读取振动信号
- `removeSignals`: 移除信号
- `addSignals`: 添加信号
- `modifySignals`: 修改信号

### 振动系统设置
- `readVibrationSystemSetup`: 读取振动系统设置
- `writeVibrationSystemSetup`: 写入振动系统设置

### 振动输入设置
- `readVibrationInputSetup`: 读取振动输入设置
- `writeVibrationInputSetup`: 写入振动输入设置

### 宽带滤波器设置
- `readBroadbandFilterSetup`: 读取宽带滤波器设置
- `writeBroadbandFilterSetup`: 写入宽带滤波器设置

### 转速计输入设置
- `readTachometerInputSetup`: 读取转速计输入设置
- `writeTachometerInputSetup`: 写入转速计输入设置

### 频谱分析设置
- `readSpectrumAnalysisSetup`: 读取频谱分析设置
- `writeSpectrumAnalysisSetup`: 写入频谱分析设置

### 跟踪滤波器设置
- `readTrackingFilterSetup`: 读取跟踪滤波器设置
- `writeTrackingFilterSetup`: 写入跟踪滤波器设置

### 属性管理
- `readAttributes`: 读取属性
- `removeAttribute`: 移除属性
- `addAttribute`: 添加属性
- `modifyAttribute`: 修改属性

### PLC 设置管理
- `readPLCSetupOptions`: 读取PLC设置选项
- `removePLCSetup`: 移除PLC设置
- `addPLCSetup`: 添加PLC设置
- `modifyPLCSetup`: 修改PLC设置

### 初始计算管理
- `readCalcsInitialOptions`: 读取初始计算选项
- `removeCalcsInitial`: 移除初始计算
- `addCalcsInitial`: 添加初始计算
- `modifyCalcsInitial`: 修改初始计算

### 最终计算管理
- `readCalcsFinalOptions`: 读取最终计算选项
- `removeCalcsFinal`: 移除最终计算
- `addCalcsFinal`: 添加最终计算
- `modifyCalcsFinal`: 修改最终计算

### 信号计算管理
- `readCalcsSignalOptions`: 读取信号计算选项
- `removeCalcsSignal`: 移除信号计算
- `addCalcsSignal`: 添加信号计算
- `modifyCalcsSignal`: 修改信号计算

### 表格管理
- `readTablesOptions`: 读取表格选项
- `removeTable`: 移除表格
- `addTable`: 添加表格
- `modifyTable`: 修改表格

### 定时器管理
- `readTimersOptions`: 读取定时器选项
- `removeTimer`: 移除定时器
- `addTimer`: 添加定时器
- `modifyTimer`: 修改定时器

## 静态属性及事件定义
- `NAME`: 返回引擎名称
- `OnEngineOptions`: 引擎选项变更事件
- `OnDashOptions`: 仪表盘选项变更事件
- `OnDisplayOptions`: 显示选项变更事件
- `OnDisplayDefineOptions`: 显示定义选项变更事件
- `OnDisplayFileOptions`: 显示文件选项变更事件
- `OnVibrationSignals`: 振动信号变更事件
- `OnVibrationSystemSetup`: 振动系统设置变更事件
- `OnVibrationInputSetup`: 振动输入设置变更事件
- `OnBroadbandFilterSetup`: 宽带滤波器设置变更事件
- `OnTachometerInputSetup`: 转速计输入设置变更事件
- `OnSpectrumAnalysisSetup`: 频谱分析设置变更事件
- `OnTrackingFilterSetup`: 跟踪滤波器设置变更事件
- `OnAttributeOptions`: 属性选项变更事件
- `OnPLCSetupOptions`: PLC设置选项变更事件
- `OnCalcsInitialOptions`: 初始计算选项变更事件
- `OnCalcsFinalOptions`: 最终计算选项变更事件
- `OnCalcsSignalOptions`: 信号计算选项变更事件
- `OnTablesOptions`: 表格选项变更事件
- `OnTimersOptions`: 定时器选项变更事件 
import { BaseProto, ProtoClass } from '@wuk/wkp'
import { IBase, BaseItr, BaseMtr, BaseConfig } from './IBase'

export enum TranType {
  WKP = 0,
  PB
}

export abstract class Packer {
  abstract get valid(): boolean
  abstract marshal: <T extends BaseProto>(
    req: T,
    option?: SendOption
  ) => [Uint8Array | null, string | number]
  abstract unmarshal: <T extends BaseProto>(data: Uint8Array) => ResultProps<T>[] | null
}

export interface TranConfig {
  svcId: string
  type: TranType
  packer?: Packer
}

export interface ResultProps<T extends BaseProto> {
  uri?: string
  rsp?: T
  traceId?: string | number
}

export interface SvcConfig<T extends BaseProto = BaseProto> extends BaseConfig {
  uris: Record<string, T>
  ip: string
  port: number
}

export interface SendOption {
  extend?: Record<string, any>
  moduleId?: number
}

export abstract class ITran extends IBase<ITran> {
  abstract get valid(): boolean
  abstract send: <T extends BaseProto>(req: T, option?: SendOption) => string | number
  abstract onUri: <T extends BaseProto>(cls: ProtoClass<T>, handler: (rsp: T) => void) => void
  abstract offUri: <T extends BaseProto>(cls: ProtoClass<T>, handler: (rsp: T) => void) => void

  static override get NAME() {
    return 'Tran'
  }

  static get ONREADY(): string {
    return 'Tran.ONREADY'
  }

  static get ONCLOSE(): string {
    return 'Tran.ONCLOSE'
  }

  static get ONSTATUS(): string {
    return 'Tran.ONSTATUS'
  }
}

export abstract class ISvc extends IBase<ISvc> {
  abstract get main(): ITran

  abstract reset: () => void
  abstract fetch: (svcId: string) => ITran
  abstract onUri: <T extends BaseProto>(
    svcId: string,
    cls: ProtoClass<T>,
    handler: (rsp: T) => void
  ) => void
  abstract offUri: <T extends BaseProto>(
    svcId: string,
    cls: ProtoClass<T>,
    handler: (rsp: T) => void
  ) => void
  abstract add: (svcId: string, type: TranType) => boolean
  abstract remove: (svcId: string) => boolean

  static override get NAME() {
    return 'Svc'
  }
}

export type SvcItr = BaseItr<ISvc>
export type SvcMtr = BaseMtr<ISvc>

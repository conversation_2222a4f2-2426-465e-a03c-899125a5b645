let nextUuid = 0

export function uuid() {
  return ++nextUuid
}

export type TypesPick<T, F> = {
  [K in keyof T]: T[K] extends F ? T[K] : never
}[keyof T]

export type TypePick<K, T, F> = {
  [KK in keyof T]: KK extends K ? T[KK] : never
}[keyof T]

export type NamesPick<T, F = any> = {
  [K in keyof T]: T[K] extends F ? K : never
}[keyof T]

export type MethodNames<T> = NamesPick<T, Function>
export type MethodTypes<T> = TypesPick<T, Function>
export type MethodPicks<B, T extends B> = Exclude<MethodNames<T>, MethodNames<B>>
export type FieldPicks<B, T extends B = any> = Exclude<NamesPick<T>, NamesPick<B>>

export type PickField<T extends B, B> = Pick<T, FieldPicks<B, T>>

export const format = (format: string, ...args: any[]) => {
  const exg = /{%(\d+)}/g
  if (exg.test(format)) {
    format = format.replace(exg, (_: any, x: any) => {
      let result = args[x - 1]
      if (result && typeof result !== 'string' && typeof result !== 'number') {
        try {
          result = JSON?.stringify(result)
        } catch (error) {
          result = '{stringify error}'
        }
      }
      return result
    })
  }
  return format
}

export const getArgs = function (): string {
  const src = location?.search || location?.href || ''
  const str = src // src.slice(1)

  const num = str.indexOf('?')
  return (num >= 0 && str.substr(num + 1)) || ''
}

let __args: Record<string, string>
export const urlArgs = function (url: string): Record<string, string> {
  if (!__args) {
    const num = url.indexOf('?')
    const ex = /([^&]+?)=([^#&]+)/g
    const args: Record<string, any> = {}

    url = url.substr(num + 1)
    while (ex.test(url)) {
      args[RegExp.$1] = RegExp.$2
    }
    __args = args
  }

  return __args
}

export const argObject = function (): Record<string, string> {
  const src = location?.search || location?.href || ''
  const str = src.slice(1)
  return urlArgs(str)
}

export const paramFmt = function (url: string) {
  const arg = function (...args: any[]) {
    return format(url, ...args)
  }
  return { url, arg }
}

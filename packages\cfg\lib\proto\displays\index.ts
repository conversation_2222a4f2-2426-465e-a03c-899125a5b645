/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  IncludeFile as IncludeFileModal,
  Displays as DisplaysModal
} from '../modal/displays.mode'

export class IncludeFile extends MapProto<IncludeFile> {
  name = ''
  uri = ''
  depends: Array<string> = []

  constructor(val?: Partial<IncludeFile>) {
    super(IncludeFileModal, 'IncludeFile')
    val && this.assign(val)
  }
}

export class Displays extends RootProto<Displays> {
  includes: Array<IncludeFile> = []

  constructor(val?: Partial<Displays>) {
    super(DisplaysModal, Displays.key)
    val && this.assign(val)
  }

  static get maxType() {
    return DisplaysModal.$MAX
  }

  static get minType() {
    return DisplaysModal.$MIN
  }

  static get uri() {
    return URI(DisplaysModal.$MAX, DisplaysModal.$MIN)
  }

  static get types(): [number, number] {
    return [DisplaysModal.$MAX, DisplaysModal.$MIN]
  }

  static get key() {
    return 'Displays'
  }
}

/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let RangeValue={$keys:["min","max","unit"],min:types.double,max:types.double,unit:types.string},PlcCalcLib={$keys:["type","min","max","data"],type:types.int32,min:types.double,max:types.double,data:types.arrayOf(types.double)},PlcSignal={$keys:["name","signal_range","units_range","type","scale_range","scale_factor","calib_mode","comment","calib","calib_data","comments"],name:types.string,signal_range:RangeValue,units_range:RangeValue,type:types.string,scale_range:RangeValue,scale_factor:types.double,calib_mode:types.int32,comment:types.int32,calib:types.int32,calib_data:PlcCalcLib,comments:types.arrayOf(types.string)},PlcSignals={$keys:["input_float","list"],input_float:types.boolean,list:types.arrayOf(PlcSignal)},PlcDevice={$keys:["type","addr","num","signals"],type:types.int32,addr:types.int32,num:types.int32,signals:PlcSignals},PlcCfg={$keys:["device_name","scan_rate","devices"],device_name:types.string,scan_rate:types.int32,devices:types.arrayOf(PlcDevice)},PlcDriverCfg={$MAX:1e4,$MIN:10,$keys:["scan_rate","plcs"],scan_rate:types.int32,plcs:types.arrayOf(PlcCfg)};export{RangeValue,PlcCalcLib,PlcSignal,PlcSignals,PlcDevice,PlcCfg,PlcDriverCfg};
/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Attrib as AttribModal,
  Attribs as AttribsModal
} from '../modal/attribs.mode'

export class Attrib extends MapProto<Attrib> {
  name = ''
  units = ''
  width = 0
  precision = 0
  long_name = ''

  constructor(val?: Partial<Attrib>) {
    super(AttribModal, 'Attrib')
    val && this.assign(val)
  }
}

export class Attribs extends RootProto<Attribs> {
  attribs: Array<Attrib> = []

  constructor(val?: Partial<Attribs>) {
    super(AttribsModal, Attribs.key)
    val && this.assign(val)
  }

  static get maxType() {
    return AttribsModal.$MAX
  }

  static get minType() {
    return AttribsModal.$MIN
  }

  static get uri() {
    return URI(AttribsModal.$MAX, AttribsModal.$MIN)
  }

  static get types(): [number, number] {
    return [AttribsModal.$MAX, AttribsModal.$MIN]
  }

  static get key() {
    return 'Attribs'
  }
}

/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  EuconvValue as EuconvValueModal,
  PsiEngine as PsiEngineModal,
  PsiSignal as PsiSignalModal,
  Psi9000Cfg as Psi9000CfgModal
} from '../modal/psi9000.mode'

export class EuconvValue extends MapProto<EuconvValue> {
  type = 0
  name = ''

  constructor(val?: Partial<EuconvValue>) {
    super(EuconvValueModal, 'EuconvValue')
    val && this.assign(val)
  }
}

export class PsiEngine extends MapProto<PsiEngine> {
  name = ''
  calib_mode = 0
  alias = ''

  constructor(val?: Partial<PsiEngine>) {
    super(PsiEngineModal, 'PsiEngine')
    val && this.assign(val)
  }
}

export class PsiSignal extends MapProto<PsiSignal> {
  name = ''
  channel = 0
  calib_mode = 0
  engines: Array<PsiEngine> = []

  constructor(val?: Partial<PsiSignal>) {
    super(PsiSignalModal, 'PsiSignal')
    val && this.assign(val)
  }
}

export class Psi9000Cfg extends RootProto<Psi9000Cfg> {
  name = ''
  alia = ''
  over_sample = 0
  trigger = ''
  trig_divisor = 0
  euconv = new EuconvValue()
  zero_relay = ''
  signals: Array<PsiSignal> = []

  constructor(val?: Partial<Psi9000Cfg>) {
    super(Psi9000CfgModal, Psi9000Cfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return Psi9000CfgModal.$MAX
  }

  static get minType() {
    return Psi9000CfgModal.$MIN
  }

  static get uri() {
    return URI(Psi9000CfgModal.$MAX, Psi9000CfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [Psi9000CfgModal.$MAX, Psi9000CfgModal.$MIN]
  }

  static get key() {
    return 'Psi9000Cfg'
  }
}

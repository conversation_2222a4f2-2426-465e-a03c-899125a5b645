import { IBase } from '../interfaces'
import { IMethod, Root } from '../base'
import { contextBridge } from 'electron'
import { createBridge, clearBridge } from '../utils'

export default class Bridge extends Root {
  private _from: string
  private _master: boolean
  private _target?: IBase

  constructor() {
    super('Bridge')

    this._from = ''
    this._master = false
  }

  get from() {
    return this._from
  }

  get target() {
    return this._target
  }

  create(name: string, target: IBase, master = false): this {
    this._from = name
    this._master = master
    this._target = target

    this.initBridge(target)

    return this
  }

  override async stop() {
    await super.stop()
  }

  override async destroy() {
    clearBridge()
    contextBridge?.exposeInMainWorld(this.from, undefined)
    await super.destroy()
  }

  test() {
    return this.unImplementP()
  }

  protected resolve<R>(result: R): Promise<R> {
    return Promise.resolve(result)
  }

  private unImplementP(): Promise<any> {
    return Promise.reject('========api unimplemented error====')
  }

  private initBridge(target: IBase) {
    const proxy = target.proxy
    const expose = createBridge<string>(
      this._from,
      this._master,
      target,
      proxy,
      (key: string, target: object, method: IMethod) => {
        this.setup(key, target, method)
      }
    )

    if (!this._master) {
      contextBridge?.exposeInMainWorld(this.from, expose)
    }
  }
}

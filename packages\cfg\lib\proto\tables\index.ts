/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Table as TableModal,
  TableGroup as TableGroupModal,
  Tables as TablesModal
} from '../modal/tables.mode'

export class Table extends MapProto<Table> {
  name = ''
  type = 0
  save = 0
  dynexp = 0
  exp = ''
  size = 0
  ascii = 0
  format = ''
  xparam_id = ''
  yparam_id = ''
  zparam_id = ''
  constant = 0.0
  interp = 0
  offset = 0
  extrap = 0
  extrap_const = 0.0
  coeff: Array<number> = []
  maxpoly = 0.0
  minpoly = 0.0
  revdate = ''
  mandate = ''
  manrev = ''
  comment = ''

  constructor(val?: Partial<Table>) {
    super(TableModal, 'Table')
    val && this.assign(val)
  }
}

export class TableGroup extends MapProto<TableGroup> {
  name = ''
  format = ''
  tables: Array<Table> = []
  comment = ''

  constructor(val?: Partial<TableGroup>) {
    super(TableGroupModal, 'TableGroup')
    val && this.assign(val)
  }
}

export class Tables extends RootProto<Tables> {
  groups: Array<TableGroup> = []

  constructor(val?: Partial<Tables>) {
    super(TablesModal, Tables.key)
    val && this.assign(val)
  }

  static get maxType() {
    return TablesModal.$MAX
  }

  static get minType() {
    return TablesModal.$MIN
  }

  static get uri() {
    return URI(TablesModal.$MAX, TablesModal.$MIN)
  }

  static get types(): [number, number] {
    return [TablesModal.$MAX, TablesModal.$MIN]
  }

  static get key() {
    return 'Tables'
  }
}

import { Invoker } from '../base'
import { IBaseProxy } from './type'

export abstract class IPage<T extends IBaseProxy = object, K = string> extends Invoker<K> {
  abstract get sdkReady(): boolean
  abstract get pageId(): string
  abstract get pageName(): string

  static get NAME() {
    return 'Page'
  }

  // 启动
  static get ONSDKUP() {
    return 'Page.ONSDKUP'
  }

  // 停止
  static get ONSDKDOWN() {
    return 'Page.ONSDKDOWN'
  }

  // 准备好
  static get ONSDKREADY() {
    return 'Page.ONSDKREADY'
  }
}

/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let UObject={$keys:["start_x","start_y","end_x","end_y","font","flag_id","obj_type"],start_x:types.int32,start_y:types.int32,end_x:types.int32,end_y:types.int32,font:types.float32,flag_id:types.string,obj_type:types.string},UBox={$MAX:3e3,$MIN:1,$keys:["base","style","box_color","shading","line_width","line_color","box_radius"],base:UObject,style:types.int32,box_color:types.string,shading:types.int32,line_width:types.int32,line_color:types.string,box_radius:types.string},ULine={$MAX:3e3,$MIN:2,$keys:["base","line_width","line_color","shading"],base:UObject,line_width:types.int32,line_color:types.string,shading:types.int32},UText={$MAX:3e3,$MIN:3,$keys:["base","direction","alignment","color","value","font_size","font_weight","item_vec"],base:UObject,direction:types.int32,alignment:types.int32,color:types.string,value:types.string,font_size:types.float32,font_weight:types.int32,item_vec:types.arrayOf(types.string)},StringItem={$keys:["color","op","value","text"],color:types.string,op:types.string,value:types.string,text:types.string},UString={$MAX:3e3,$MIN:4,$keys:["base","param_id","label_space","label","label_color","string_color","param_box","param_box_color","shading","string_font_size","string_font_weight","string_radius","label_font_size","label_font_weight","item_vec"],base:UObject,param_id:types.string,label_space:types.int32,label:types.string,label_color:types.string,string_color:types.string,param_box:types.int32,param_box_color:types.string,shading:types.int32,string_font_size:types.float32,string_font_weight:types.int32,string_radius:types.string,label_font_size:types.float32,label_font_weight:types.int32,item_vec:types.arrayOf(StringItem)},DigitalItem={$keys:["param_id","type","width","prec","label","units"],param_id:types.string,type:types.string,width:types.int32,prec:types.int32,label:types.string,units:types.string},UDigital={$MAX:3e3,$MIN:5,$keys:["base","label_space","unit_space","spacing","param_box","param_box_color","label_color","shading","direction","font_size","font_weight","label_font_size","label_font_weight","unit_font_size","unit_font_weight","digit_radius","item_vec"],base:UObject,label_space:types.int32,unit_space:types.int32,spacing:types.int32,param_box:types.int32,param_box_color:types.string,label_color:types.string,shading:types.int32,direction:types.int32,font_size:types.float32,font_weight:types.int32,label_font_size:types.float32,label_font_weight:types.int32,unit_font_size:types.float32,unit_font_weight:types.int32,digit_radius:types.string,item_vec:types.arrayOf(DigitalItem)},BarItem={$keys:["param_id","target","corr","label","units","min","max","tic_inter","label_inter"],param_id:types.string,target:types.string,corr:types.string,label:types.string,units:types.string,min:types.int32,max:types.int32,tic_inter:types.int32,label_inter:types.int32},UBar={$MAX:3e3,$MIN:6,$keys:["base","label_space","unit_space","label_color","length","bar_height","format","tic_pos","dir","shading","spacing","tic_font","param_box","param_box_color","limit_width","unit_font_size","tic_font_size","digit_font_size","digit_font_weight","digit_radius","label_font_size","label_font_weight","unit_font_weight","bar_arc","item_vec"],base:UObject,label_space:types.int32,unit_space:types.int32,label_color:types.string,length:types.int32,bar_height:types.int32,format:types.string,tic_pos:types.string,dir:types.int32,shading:types.int32,spacing:types.int32,tic_font:types.int32,param_box:types.int32,param_box_color:types.string,limit_width:types.int32,unit_font_size:types.float32,tic_font_size:types.float32,digit_font_size:types.float32,digit_font_weight:types.int32,digit_radius:types.string,label_font_size:types.float32,label_font_weight:types.int32,unit_font_weight:types.int32,bar_arc:types.int32,item_vec:types.arrayOf(BarItem)},IndicatorItem={$keys:["color","op","test_value_id","test_string"],color:types.string,op:types.string,test_value_id:types.string,test_string:types.string},UIndicator={$MAX:3e3,$MIN:7,$keys:["base","param_id","text_color","font_size","font_weight","radius","type","label","border_color","box_color","border_width","item_vec"],base:UObject,param_id:types.string,text_color:types.string,font_size:types.float32,font_weight:types.int32,radius:types.string,type:types.string,label:types.string,border_color:types.string,box_color:types.string,border_width:types.int32,item_vec:types.arrayOf(IndicatorItem)},UGauge={$MAX:3e3,$MIN:8,$keys:["base","param_id","gauge_type","peak_param_id","label_color","needle_color","nums_out_side","tic_font","format","max","min","label","units","label_inter","tic_inter","radius","param_box","param_box_color","shading","tic_font_size","axis_line_width","axis_line_space","unit_font_size","digit_font_size","digit_font_weight","digit_radius","label_font_size","label_font_weight","unit_font_weight","gauge_color","track_color","inner_ring_color"],base:UObject,param_id:types.string,gauge_type:types.string,peak_param_id:types.string,label_color:types.string,needle_color:types.string,nums_out_side:types.int32,tic_font:types.int32,format:types.string,max:types.float32,min:types.float32,label:types.string,units:types.string,label_inter:types.int32,tic_inter:types.float32,radius:types.int32,param_box:types.int32,param_box_color:types.string,shading:types.int32,tic_font_size:types.float32,axis_line_width:types.float32,axis_line_space:types.float32,unit_font_size:types.float32,digit_font_size:types.float32,digit_font_weight:types.int32,digit_radius:types.string,label_font_size:types.float32,label_font_weight:types.int32,unit_font_weight:types.int32,gauge_color:types.string,track_color:types.string,inner_ring_color:types.string},CalMsg={$keys:["uuid","line_num","path"],uuid:types.string,line_num:types.int32,path:types.string},USwitch={$MAX:3e3,$MIN:9,$keys:["base","param_id","type","on_label","off_label","off_color","on_color","hide_box_shadow","radius","font_size","font_weight","has_release","release_msg","release_vec","has_push","push_msg","push_vec"],base:UObject,param_id:types.string,type:types.string,on_label:types.string,off_label:types.string,off_color:types.string,on_color:types.string,hide_box_shadow:types.int32,radius:types.string,font_size:types.float32,font_weight:types.int32,has_release:types.boolean,release_msg:CalMsg,release_vec:types.arrayOf(types.string),has_push:types.boolean,push_msg:CalMsg,push_vec:types.arrayOf(types.string)},UButton={$MAX:3e3,$MIN:10,$keys:["base","type","param_id","set_value","on_label","off_label","label_color","on_color","off_color","radius","font_size","font_weight"],base:UObject,type:types.string,param_id:types.string,set_value:types.int32,on_label:types.string,off_label:types.string,label_color:types.string,on_color:types.string,off_color:types.string,radius:types.string,font_size:types.float32,font_weight:types.int32},ECMButtonPanel={$keys:["style","line_width","line_color","box_color","shading","label"],style:types.int32,line_width:types.float32,line_color:types.string,box_color:types.string,shading:types.float32,label:types.string},ECMButtonState={$keys:["state","label","on_color","has_wait","wait_msg","wait_vec","has_lock","lock_msg","lock_vec","has_init","init_msg","init_cal_vec","has_main","main_msg","main_cal_vec","has_final","final_msg","final_cal_vec"],state:types.int32,label:types.string,on_color:types.string,has_wait:types.boolean,wait_msg:CalMsg,wait_vec:types.arrayOf(types.string),has_lock:types.boolean,lock_msg:CalMsg,lock_vec:types.arrayOf(types.string),has_init:types.boolean,init_msg:CalMsg,init_cal_vec:types.arrayOf(types.string),has_main:types.boolean,main_msg:CalMsg,main_cal_vec:types.arrayOf(types.string),has_final:types.boolean,final_msg:CalMsg,final_cal_vec:types.arrayOf(types.string)},UECMButton={$MAX:3e3,$MIN:11,$keys:["base","param_id","wait_param_id","type","label_color","warn_color","off_color","radius","font_size","font_weight","default_state","has_panel","panel","state_vec"],base:UObject,param_id:types.string,wait_param_id:types.string,type:types.string,label_color:types.string,warn_color:types.string,off_color:types.string,radius:types.string,font_size:types.float32,font_weight:types.int32,default_state:types.int32,has_panel:types.boolean,panel:ECMButtonPanel,state_vec:types.arrayOf(ECMButtonState)},XAxis={$keys:["param_id","x_start","x_end","tic_interval","y_intercept","axis_color","label_interval","tic_position","label_position","bottom_to_x_axis","top_to_x_axis","value1","value2"],param_id:types.string,x_start:types.float32,x_end:types.float32,tic_interval:types.float32,y_intercept:types.float32,axis_color:types.string,label_interval:types.float32,tic_position:types.string,label_position:types.string,bottom_to_x_axis:types.int32,top_to_x_axis:types.int32,value1:types.int32,value2:types.int32},ReferenceX={$keys:["y_intercept","tic_position","label_position"],y_intercept:types.string,tic_position:types.string,label_position:types.string},TablePlot={$keys:["color","table_name"],color:types.string,table_name:types.string},Yaxis={$keys:["param_id","y_start","y_end","tic_interval","x_intercept","axis_color","label_interval","grid","tic_position","label_position","bottom_to_x_axis","top_to_x_axis","param_list","color_vec","table_plot_vec","value1"],param_id:types.string,y_start:types.float32,y_end:types.float32,tic_interval:types.float32,x_intercept:types.float32,axis_color:types.string,label_interval:types.float32,grid:types.int32,tic_position:types.string,label_position:types.string,bottom_to_x_axis:types.int32,top_to_x_axis:types.int32,param_list:types.arrayOf(types.string),color_vec:types.arrayOf(types.string),table_plot_vec:types.arrayOf(TablePlot),value1:types.int32},HardCopyPlotItem={$keys:["position_x","position_y","text_string"],position_x:types.float32,position_y:types.float32,text_string:types.string},HardCopyPlot={$keys:["pen_number","direction","plot_item_vec"],pen_number:types.int32,direction:types.int32,plot_item_vec:types.arrayOf(HardCopyPlotItem)},UPlot={$MAX:3e3,$MIN:12,$keys:["base","plot_key","type","points","spot_param_id","line_width","hide_button","has_x_axis","x_axis","has_reference_x","reference_x","yaxis_vec","has_hard_copy_plot","hard_copy_plot"],base:UObject,plot_key:types.string,type:types.string,points:types.arrayOf(types.string),spot_param_id:types.string,line_width:types.int32,hide_button:types.int32,has_x_axis:types.boolean,x_axis:XAxis,has_reference_x:types.boolean,reference_x:ReferenceX,yaxis_vec:types.arrayOf(Yaxis),has_hard_copy_plot:types.boolean,hard_copy_plot:HardCopyPlot},UFuncButton={$MAX:3e3,$MIN:13,$keys:["base","type","label","off_label","background_color","text_color","radius","font_size","font_weight","crt_name","quad_index","display_name"],base:UObject,type:types.string,label:types.string,off_label:types.string,background_color:types.string,text_color:types.string,radius:types.string,font_size:types.float32,font_weight:types.int32,crt_name:types.string,quad_index:types.int32,display_name:types.string},UImage={$MAX:3e3,$MIN:14,$keys:["base","image_name"],base:UObject,image_name:types.string},UInput={$MAX:3e3,$MIN:15,$keys:["base","param_id","label","label_color","max","min","inter_val","input_width","prec","bar","left_label","calc_label","right_label","param_box","param_box_color","shading","delta","digit_font_size","digit_space","label_font_size","label_font_weight","digit_font_weight","digit_radius"],base:UObject,param_id:types.string,label:types.string,label_color:types.string,max:types.float32,min:types.float32,inter_val:types.float32,input_width:types.int32,prec:types.int32,bar:types.int32,left_label:types.string,calc_label:types.string,right_label:types.string,param_box:types.int32,param_box_color:types.string,shading:types.int32,delta:types.string,digit_font_size:types.float32,digit_space:types.float32,label_font_size:types.float32,label_font_weight:types.int32,digit_font_weight:types.int32,digit_radius:types.string},ObjectItem={$keys:["uri","bytes"],uri:types.string,bytes:types.bytes},Display={$MAX:3e3,$MIN:100,$keys:["name","background","editres","move_grid","resize_grid","show_grid","grid_color","obj_vec"],name:types.string,background:types.string,editres:types.string,move_grid:types.int32,resize_grid:types.int32,show_grid:types.int32,grid_color:types.string,obj_vec:types.arrayOf(ObjectItem)};export{UObject,UBox,ULine,UText,StringItem,UString,DigitalItem,UDigital,BarItem,UBar,IndicatorItem,UIndicator,UGauge,CalMsg,USwitch,UButton,ECMButtonPanel,ECMButtonState,UECMButton,XAxis,ReferenceX,TablePlot,Yaxis,HardCopyPlotItem,HardCopyPlot,UPlot,UFuncButton,UImage,UInput,ObjectItem,Display};
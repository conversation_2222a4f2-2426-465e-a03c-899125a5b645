$DISPLAY "Calibration" background=NewBlue editres=MEDRES movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$MSGQ
$STOREDISPLAY Append
$OBJECT Text font=1 startx=10 starty=10 endx=1260 endy=80
Black direction=1 alignment=1 fontweight=500 fontsize=18
"System/Calibration"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=10 starty=50 endx=114 endy=770
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibEgtEng1 type="FLOAT" width=7 prec=2 label="Eng EGT(1)" units="Deg F" 
CalibCIT9 type="FLOAT" width=7 prec=2 label="CIT(9)" units="Deg F" 
CalibCIT3 type="FLOAT" width=7 prec=2 label="EGT(3)" units="Deg F" 
CalibEGT13 type="FLOAT" width=7 prec=2 label="Spare" units="Deg F" 
CalibOriTemp1 type="FLOAT" width=7 prec=2 label="Orifice In Tmp 1" units="Deg F" 
CalibPCell type="FLOAT" width=7 prec=2 label="Cell Pressure" units="PSIA" 
Spare type="FLOAT" width=7 prec=2 label="Spare" units="PSIG" 
CalibGenVA type="FLOAT" width=7 prec=2 label="Gen Volts A" units="VAC"
Spare type="FLOAT" width=7 prec=2 label="Spare 5" units="VAC" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=138 starty=50 endx=238 endy=770
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibEgtEng2 type="FLOAT" width=7 prec=2 label="Eng EGT(2)" units="Deg F" 
CalibCIT10 type="FLOAT" width=7 prec=2 label="CIT(10)" units="Deg F" 
CalibEGT4 type="FLOAT" width=7 prec=2 label="EGT(4)" units="Deg F" 
Space type="FLOAT" width=7 prec=2 label="Spare" units="Deg F"  
CalibOriTemp2 type="FLOAT" width=7 prec=2 label="Orifice In Tmp 2" units="Deg F" 
CalibOilOutPress type="FLOAT" width=7 prec=2 label="Oil Out Press" units="PSIG" 
Space type="FLOAT" width=7 prec=2 label="Spare" units="PSIG" 
CalibGenVB type="FLOAT" width=7 prec=2 label="Gen Volts B" units="VAC" 
Space type="FLOAT" width=7 prec=2 label="Spare 6" units="VAC"  
$END-OBJECT
***********
$OBJECT Digital font=20 startx=266 starty=50 endx=356 endy=770
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibCIT1 type="FLOAT" width=7 prec=2 label="CIT(1)" units="Deg F"  
CalibCIT11 type="FLOAT" width=7 prec=2 label="CIT(11)" units="Deg F"  
CalibEGT5 type="FLOAT" width=7 prec=2 label="EGT(5)" units="Deg F"  
Space type="FLOAT" width=7 prec=2 label="Spare" units="Deg F"  
CalibFMTemp type="FLOAT" width=7 prec=2 label="Fuel Meter Tmp" units="Deg F"  
CalibOilSumpPress type="FLOAT" width=7 prec=2 label="Oil Sump Pres" units="In H2O" 
Space type="FLOAT" width=7 prec=2 label="Spare" units="PSIG" 
CalibGenVC type="FLOAT" width=7 prec=2 label="Gen Volts C" units="VAC"
CalibStartVolA type="FLOAT" width=7 prec=2 label="Starter Vlts A" units="VAC" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=394 starty=50 endx=484 endy=770
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibCIT2 type="FLOAT" width=7 prec=2 label="CIT(2)" units="Deg F"  
CalibCIT12 type="FLOAT" width=7 prec=2 label="CIT(12)" units="Deg F" 
CalibEGT6 type="FLOAT" width=7 prec=2 label="EGT(6)" units="Deg F" 
CalibTCell type="FLOAT" width=7 prec=2 label="Test Cell Tmp" units="Deg F"  
CalibOilTemp type="FLOAT" width=7 prec=2 label="Oil Temp" units="Deg F" 
CalibFPress type="FLOAT" width=7 prec=2 label="Fuel In Press" units="PSIG" 
Spare type="FLOAT" width=7 prec=2 label="Spare" units="PSI" 
CalibGenAmpsA type="FLOAT" width=7 prec=2 label="Gen Amps A" units="Amps"
CalibStartVolB type="FLOAT" width=7 prec=2 label="Starter Vits B" units="VAC" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=512 starty=50 endx=619 endy=770
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibCIT3 type="FLOAT" width=7 prec=2 label="CIT(3)" units="Deg F"   
Spare type="FLOAT" width=7 prec=2 label="Spare" units="Deg F"  
CalibEGT7 type="FLOAT" width=7 prec=2 label="EGT(7)" units="Deg F"  
CalibTB1 type="FLOAT" width=7 prec=2 label="Bleed Air Temp 1" units="Deg F"   
CalibEngFuelTemp type="FLOAT" width=7 prec=2 label="Eng Fuel Temp" units="Deg F"  
CalibPB type="FLOAT" width=7 prec=2 label="Bleed Press" units="PSIA" 
Sparex type="FLOAT" width=7 prec=2 label="Sparex" units="PSI" 
CalibGenAmpsB type="FLOAT" width=7 prec=2 label="Gen Amps B" units="AmpsS"
CalibStartVolC type="FLOAT" width=7 prec=2 label="Starter Vlts c" units="VAC" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=628 starty=50 endx=742 endy=770
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibCIT4 type="FLOAT" width=7 prec=2 label="CIT(4)" units="Deg F"
Spare type="FLOAT" width=7 prec=2 label="Spare" units="Deg F"
CalibEGT8 type="FLOAT" width=7 prec=2 label="EGT(8)" units="Deg F"
CalibTB2 type="FLOAT" width=7 prec=2 label="Bleed Air Temp 2" units="Deg F"
Spare type="FLOAT" width=7 prec=2 label="Spare Temp 1" units="Deg F"
CalibOriInltPress type="FLOAT" width=7 prec=2 label="Orifice Inlt Press" units="PSIA" 
CalibVB_1 type="FLOAT" width=7 prec=2 label="GearBox Vib" units="IPS" 
CalibGenAmpsC type="FLOAT" width=7 prec=2 label="Gen Amps C" units="Amps"
Spare type="FLOAT" width=7 prec=2 label="Spare 7" units="spare" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=764 starty=50 endx=878 endy=770
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibCIT5 type="FLOAT" width=7 prec=2 label="CIT(5)" units="Deg F"
Spare type="FLOAT" width=7 prec=2 label="Spare" units="Deg F"
CalibEGT9 type="FLOAT" width=7 prec=2 label="EGT(9)" units="Deg F"
CalibTB3 type="FLOAT" width=7 prec=2 label="Bleed Air Temp 3" units="Deg F"
N1RPM type="FLOAT" width=7 prec=2 label="Monopole 1" units="RPM"
CalibOriDiffPress type="FLOAT" width=7 prec=2 label="Orifice Diff Press" units="In H2O" 
CalibVB_2 type="FLOAT" width=7 prec=2 label="Turbine Vib" units="IPS" 
Spare type="FLOAT" width=7 prec=2 label="Spare 1" units="Amps"
Spare type="FLOAT" width=7 prec=2 label="Starter Volts" units="Volts DC" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=883 starty=50 endx=1000 endy=770
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibCIT6 type="FLOAT" width=7 prec=2 label="CIT(6)" units="Deg F"
Spare type="FLOAT" width=7 prec=2 label="Spare" units="Deg F"
CalibEGT10 type="FLOAT" width=7 prec=2 label="EGT(10)" units="Deg F"
CalibTB4 type="FLOAT" width=7 prec=2 label="Bleed Air Temp 4" units="Deg F"
N2RPM type="FLOAT" width=7 prec=2 label="Monopole 2" units="RPM"
OilSumpPressPsia type="FLOAT" width=7 prec=2 label="Comp Disch Press" units="PSIA" 
CalibVB_3 type="FLOAT" width=7 prec=2 label="Cooling Fan Vib" units="IPS" 
Spare type="FLOAT" width=7 prec=2 label="Spare 2" units="Amps"
Spare type="FLOAT" width=7 prec=2 label="Starter Amps" units="Amps" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=1012 starty=50 endx=1146 endy=690
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibCIT7 type="FLOAT" width=7 prec=2 label="CIT(7)" units="Deg F"
CalibEGT1 type="FLOAT" width=7 prec=2 label="EGT(1)" units="Deg F"
CalibEGT11 type="FLOAT" width=7 prec=2 label="EGT(11)" units="Deg F" 
CalibTCD1 type="FLOAT" width=7 prec=2 label="Comp Dis Tmp 1" units="Deg F" 
CalibFW1 type="FLOAT" width=7 prec=2 label="Fuel Flow 1" units="Lb/hr"
PS9Psia type="FLOAT" width=7 prec=2 label="Exhaust Static Pres" units="PSIA" 
CalibCBVP type="FLOAT" width=7 prec=2 label="Coarse Bld Valve Pos" units="%" 
Spare type="FLOAT" width=7 prec=2 label="Spare 3" units="Amps"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=1140 starty=50 endx=1255 endy=690
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=20 fontweight=500 labelfontsize=12 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
CalibCIT8 type="FLOAT" width=7 prec=2 label="CIT(8)" units="Deg F" 
CalibEGT2 type="FLOAT" width=7 prec=2 label="EGT(2)" units="Deg F"
CalibEGT12 type="FLOAT" width=7 prec=2 label="EGT(12)" units="Deg F"
CalibTCD2 type="FLOAT" width=7 prec=2 label="Comp Dis Tmp 2" units="Deg F" 
CalibFW2 type="FLOAT" width=7 prec=2 label="Fuel Flow 2" units="Lb/Hr"
Spare type="FLOAT" width=7 prec=2 label="Spare" units="PSIG" 
CalibFBVP type="FLOAT" width=7 prec=2 label="Fine Bld Valve Pos" units="%" 
Spare type="FLOAT" width=7 prec=2 label="Spare 4" units="VAC"
$END-OBJECT
***********
$OBJECT Box font=1 startx=760 starty=365 endx=1265 endy=445
style=0
linewidth=1
linecolor=Black
boxcolor=DarkGray
shading=0
$END-OBJECT
***********
$OBJECT Box font=1 startx=1015 starty=365 endx=1265 endy=445
style=0
linewidth=1
linecolor=Black
boxcolor=DarkGray
shading=0
$END-OBJECT
***********
$OBJECT Line font=1 startx=10 starty=445 endx=760 endy=446
linewidth=1 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Box font=1 startx=640 starty=525 endx=1005 endy=605
style=0
linewidth=1
linecolor=Black
boxcolor=DarkGray
shading=0
$END-OBJECT
***********
$OBJECT Box font=1 startx=640 starty=525 endx=1265 endy=605
style=0
linewidth=1
linecolor=Black
boxcolor=DarkGray
shading=0
$END-OBJECT
***********
$OBJECT Line font=1 startx=10 starty=605 endx=640 endy=605
linewidth=1 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Box font=1 startx=10 starty=780 endx=480 endy=990
style=0
linewidth=2
linecolor=Black
boxcolor=DarkGray
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=40 starty=800 endx=500 endy=870
labelspace=5 unitspace=5 spacing=45 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=16 fontweight=400 labelfontsize=13 \
labelfontweight=500 unitfontsize=10 unitfontweight=400 
Digital1 type="FLOAT" width=10 prec=0 label="Pickup # 1" units=""
Digital2 type="FLOAT" width=10 prec=0 label="Pickup # 2" units=""
Digital3 type="FLOAT" width=10 prec=0 label="Pickup # 3" units=""
$END-OBJECT
***********
$OBJECT FuncButton font=66 startx=1090 starty=850 endx=1190 endy=895
type="CLOSECRT" label="Close" backgroundcolor=DarkGray textcolor=White  fontsize=24 fontweight=400
$END-OBJECT
***********
$END-DISPLAY


import { types } from '@wuk/wkp'

export const Dash = {
  $keys: ['eec_key', 'name'],

  eec_key: types.string,
  name: types.string
}

export const Eec = {
  $keys: ['eec_prompt_string', 'list'],

  eec_prompt_string: types.string,
  list: types.arrayOf(types.string)
}

export const Engine = {
  $MAX: 2000,
  $MIN: 70,
  $keys: ['eec', 'dash'],

  eec: types.mapOf(types.string, Eec),
  dash: types.arrayOf(Dash)
}

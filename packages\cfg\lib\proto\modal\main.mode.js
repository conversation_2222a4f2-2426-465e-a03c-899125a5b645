/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let IncludeFile={$keys:["file","uri","depends"],file:types.string,uri:types.string,depends:types.arrayOf(types.string)},Main={$MAX:2e3,$MIN:80,$keys:["scan_rate","test_mode","pla_rig_param","pla_idle_default","pla_takeoff_default","crs_on_param","run_limits_param","includes"],scan_rate:types.string,test_mode:types.boolean,pla_rig_param:types.string,pla_idle_default:types.string,pla_takeoff_default:types.string,crs_on_param:types.string,run_limits_param:types.string,includes:types.arrayOf(IncludeFile)};export{IncludeFile,Main};
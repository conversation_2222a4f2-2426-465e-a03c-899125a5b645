/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Timer as TimerModal,
  Timers as TimersModal
} from '../modal/timers.mode'

export class Timer extends MapProto<Timer> {
  name = ''
  unit = ''
  param_id = ''
  type = ''

  constructor(val?: Partial<Timer>) {
    super(TimerModal, 'Timer')
    val && this.assign(val)
  }
}

export class Timers extends RootProto<Timers> {
  timers: Array<Timer> = []

  constructor(val?: Partial<Timers>) {
    super(TimersModal, Timers.key)
    val && this.assign(val)
  }

  static get maxType() {
    return TimersModal.$MAX
  }

  static get minType() {
    return TimersModal.$MIN
  }

  static get uri() {
    return URI(TimersModal.$MAX, TimersModal.$MIN)
  }

  static get types(): [number, number] {
    return [TimersModal.$MAX, TimersModal.$MIN]
  }

  static get key() {
    return 'Timers'
  }
}

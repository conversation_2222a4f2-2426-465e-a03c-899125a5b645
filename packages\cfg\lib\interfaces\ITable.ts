import { BaseItr, BaseMtr, IBase } from './IBase'
import { <PERSON>Field } from '../base'
import { MapProto } from '@wuk/wkp'
import { tablecfg, tables } from '../proto'

export type TableLib = PickField<tablecfg.TableLib, MapProto<tablecfg.TableLib>>
export type TableCfg = {
  name: string
  tables: Array<TableLib>
}

export type TableData = PickField<tables.Table, MapProto<tables.Table>>
export type TableGroup = {
  name: string
  tables: Array<TableData>
}
export interface TablesOptions {
  groups: TableGroup[]
}

export abstract class ITable extends IBase<ITable> {
  abstract readOptions(): Promise<TablesOptions>
  abstract removeGroup(index: number): Promise<boolean>
  abstract addGroup(val: TableGroup, index?: number): Promise<boolean>
  abstract modifyGroup(index: number, val: Partial<TableGroup>): Promise<boolean>

  abstract removeTable(groupName: string, index: number): Promise<boolean>
  abstract addTable(groupName: string, val: TableData, index?: number): Promise<boolean>
  abstract modifyTable(groupName: string, index: number, val: Partial<TableData>): Promise<boolean>

  abstract readTableCfg(groupName: string): Promise<TableCfg | undefined>
  abstract modifyTableCfg(groupName: string, val: Partial<TableCfg>): Promise<boolean>

  abstract removeTableLib(groupName: string, tableName: string): Promise<boolean>
  abstract addTableLib(groupName: string, tableName: string, val: TableLib): Promise<boolean>
  abstract modifyTableLib(
    groupName: string,
    tableName: string,
    val: Partial<TableLib>
  ): Promise<boolean>

  static override get NAME() {
    return 'Table'
  }

  static get OnTableCfg() {
    return 'Table.OnTableCfg'
  }

  static get OnTablesOptions() {
    return 'Table.OnTablesOptions'
  }
}

export type TableItr = BaseItr<ITable>
export type TableMtr = BaseMtr<ITable>

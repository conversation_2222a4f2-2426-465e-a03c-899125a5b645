$DISPLAY "Main" background=NewBlue editres=1280:576 movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$STOREDISPLAY Overwrite
***********
$OBJECT Image font=1 startx=0 starty=0 endx=150 endy=65
loongair.png
$END-OBJECT
***********
$OBJECT Image font=1 startx=150 starty=0 endx=300 endy=65
loongair.png
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=0 starty=70 endx=160 endy=100
WFDiffPct textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Orrifice Delta P"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=0 starty=103 endx=160 endy=133
CorrectAdapter_Stat1 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Fuel Temp"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=0 starty=136 endx=160 endy=166
CorrectAdapter_Stat2 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Fuel Press"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=0 starty=169 endx=160 endy=199
CorrectAdapter_Stat3 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="EGT"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=0 starty=202 endx=160 endy=232
CorrectAdapter_Stat4 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="EGT Unit Spread"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=0 starty=235 endx=160 endy=265
CorrectAdapter_Stat5 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="EGT Spread"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=165 starty=70 endx=335 endy=133
CorrectAdapter_Stat6 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="E-STOP"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=165 starty=136 endx=335 endy=166
CorrectAdapter_Stat7 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Gbox Pressure"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=165 starty=169 endx=335 endy=199
CorrectAdapter_Stat8 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Prog Logic Control"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=165 starty=202 endx=335 endy=232
CorrectAdapter_Stat9 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="High Oil Temp"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=165 starty=235 endx=335 endy=265
CorrectAdapter_Stat10 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Oil Pressure"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=338 starty=70 endx=498 endy=100
CorrectAdapter_Stat6 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Gen Over Volt"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=338 starty=103 endx=498 endy=133
CorrectAdapter_Stat6 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Gen Under Volt"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=338 starty=136 endx=498 endy=166
CorrectAdapter_Stat7 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Gearbox vib"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=338 starty=169 endx=498 endy=199
CorrectAdapter_Stat8 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Turbine Vib"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=338 starty=202 endx=498 endy=232
CorrectAdapter_Stat9 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Turb Post Vib"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=338 starty=235 endx=498 endy=265
CorrectAdapter_Stat10 textcolor=White  fontsize=18 fontweight=500 type="LIMIT" label="Overspeed"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=510 starty=110 endx=525 endy=125
CrsCollectDataSwitch textcolor=White  fontsize=18 fontweight=500 radius="20" type="LIGHT"
Green = 1
Red = 0
$END-OBJECT
***********
$OBJECT Buttonobj font=1 startx=510 starty=135 endx=680 endy=250
parameter=ctcurwireda type=RADIO
labelcolor=Black warncolor=Yellow offcolor=DarkGray fontsize=18 fontweight=500
$PANEL
style=2
linewidth=0
linecolor=Black
boxcolor=NewGray
shading=0
label="Speed Reference"
$END-PANEL
defaultstate=0
$STATE 1
label="Monopole 2"
oncolor=Green
$END-STATE
$STATE 0
label="Monopole 1"
oncolor=Red
$END-STATE
$END-OBJECT
***********
$OBJECT Digital font=20 startx=690 starty=90 endx=902 endy=163
labelspace=8 unitspace=0 direction=1 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=18 fontweight=500 labelfontsize=20 labelfontweight=600 unitfontsize=15
TestRunTime type="TIME" width=5 prec=2 label="Run Time" units="Seconds"
Time_secs type="HOURS" width=5 prec=2 label="Clock" units=""
$END-OBJECT
***********
$OBJECT Box font=1 startx=690 starty=170 endx=900 endy=250
style=3
linewidth=0 linecolor=DarkGray
boxcolor=DarkGray
shading=0
$END-OBJECT
***********
$OBJECT Switch font=22 startx=695 starty=190 endx=750 endy=230
TOGGLE ClockSwitch onlabel="Stop" offlabel="Start" offcolor=Blue oncolor=Green labelcolor=White fontsize=15
$RELEASE
#if ClockSwitch
ClockIgnore: clockstart()
#else
ClockIgnore: clockpause()
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT Digital font=20 startx=750 starty=175 endx=840 endy=246
labelspace=6 unitspace=0 direction=1 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=18 fontweight=500 labelfontsize=20 labelfontweight=600 unitfontsize=15
ClockTime type="TIME" width=5 prec=2 label="Timer" units="Seconds"
$END-OBJECT
***********
$OBJECT Switch font=22 startx=840 starty=190 endx=895 endy=230
TOGGLE ClockIgnore onlabel="Reset" offlabel="Reset" offcolor=Blue oncolor=Blue labelcolor=White fontsize=15
$RELEASE
ClockIgnore: clockreset()
ClockSwitch: 0
$END-RELEASE
$END-OBJECT
***********
$OBJECT Box font=1 startx=910 starty=10 endx=1270 endy=260
style=3
linewidth=0 linecolor=DarkGray
boxcolor=DarkGray
shading=0
$END-OBJECT
***********
$OBJECT Text font=22 startx=1036 starty=17 endx=1144 endy=45
Black direction=1 alignment=0 fontsize=20
"Indicators"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=915 starty=50 endx=998 endy=90
CorrectAdapter_Stat15 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Red = 0 "Scv\nNormal"
Green = 1 "Scv\nBold"
Yellow = 2 "Scv\nAdapter"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1003 starty=50 endx=1086 endy=90
CorrectAdapter_Stat16 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
CdBlue = 0 "IGV\nDemand Off"
Green = 1 "IGV\nDemand On"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1091 starty=50 endx=1174 endy=90
CorrectAdapter_Stat17 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Blue = 0 "Starter\nOFF"
White = 1 "Starter\nON"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1179 starty=50 endx=1262 endy=90
CorrectAdapter_Stat18 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Magenta = 0 "Start in\nProgress"
Gray = 1 "Start in\nProgress"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=915 starty=105 endx=998 endy=145
CorrectAdapter_Stat19 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Red = 0 "Scv\nNormal"
Green = 1 "Scv\nBold"
Yellow = 2 "Scv\nAdapter"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1003 starty=105 endx=1086 endy=145
CorrectAdapter_Stat20 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
CdBlue = 0 "LCV\nOFF"
Green = 1 "LCV\nON"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1091 starty=105 endx=1174 endy=145
CorrectAdapter_Stat21 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Blue = 0 "GCU\nPower Off"
White = 1 "GCU\nPower On"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1179 starty=105 endx=1262 endy=145
CorrectAdapter_Stat22 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Magenta = 0 "Scv\nNormal"
Gray = 1 "Scv\nGround"
Orange = 2 "Scv\nAdapter"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=915 starty=160 endx=998 endy=200
CorrectAdapter_Stat23 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Red = 0 "Ac Start"
Green = 1 "Ac End"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1003 starty=160 endx=1086 endy=200
CorrectAdapter_Stat24 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
CdBlue = 0 "Scv\nNormal"
Green = 1 "Scv\nGround"
Yellow = 2 "Scv\nAdapter"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1091 starty=160 endx=1174 endy=200
CorrectAdapter_Stat25 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Blue = 0 "Vari-Speed\nOFF"
White = 1 "Vari-Speed\nON"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1179 starty=160 endx=1262 endy=200
CorrectAdapter_Stat26 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Magenta = 0 "Ready To\nLoad"
Gray = 1 "Ready To\nLoad"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=915 starty=215 endx=998 endy=255
CorrectAdapter_Stat27 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Red = 0 "Scv\nNormal"
Green = 1 "Scv\nBold"
Yellow = 2 "Scv\nAdapter"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1003 starty=215 endx=1086 endy=255
CorrectAdapter_Stat28 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
CdBlue = 0 "MES\nOFF"
Green = 1 "MES\nON"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1091 starty=215 endx=1174 endy=255
CorrectAdapter_Stat29 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Blue = 0 "WOW\nGround"
White = 1 "WOW\nGround"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=1179 starty=215 endx=1262 endy=255
CorrectAdapter_Stat30 textcolor=Black fontsize=13 fontweight=500 radius="40" type="STATUS"
Magenta = 0 "ECU Fire\nSimulate Off"
Gray = 1 "ECU Fire\nSimulate On"
$END-OBJECT
***********
$OBJECT Box font=1 startx=3 starty=290 endx=162 endy=566
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=3 starty=320 endx=163 endy=322
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Text font=22 startx=60 starty=290 endx=162 endy=330
Black direction=1 alignment=0 fontweight=600 fontsize=20
"EGT"
$END-OBJECT
***********
$OBJECT Gauge font=1 startx=3 starty=310 endx=162 endy=500
EGTAvg DIAL_NEEDLE
peakparam=ctcurphaseda labelcolor=White needlecolor=Black numsoutside=0
ticfont=2 format=0.0 max=2000.000000 min=0.000000 ticfontsize=10 axislinewidth=5 axislinespace=3 unitfontsize=15
label="" units="decs" labelinter=2 ticinter=200.000000 radius=79
parambox=2 paramboxcolor=Magenta shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=3 starty=480 endx=162 endy=482
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Buttonobj font=1 startx=7 starty=500 endx=52 endy=542
parameter=ctcurwireda type=LIGHTSWITCH
labelcolor=Black warncolor=Yellow offcolor=DarkGray fontsize=15 fontweight=500
defaultstate=0
$STATE 1
label="Lab"
oncolor=Green
$END-STATE
$STATE 0
label="Eng"
oncolor=Red
$END-STATE
$END-OBJECT
***********
$OBJECT Digital font=20 startx=60 starty=499 endx=145 endy=553
labelspace=6 unitspace=0 direction=1 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=23 fontweight=500 labelfontsize=26 labelfontweight=600 unitfontsize=16
EGTAvg type="FLOAT" width=6 prec=2 label="" units="Degrees F"
$END-OBJECT
***********
$OBJECT Box font=1 startx=165 starty=290 endx=324 endy=566
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=165 starty=320 endx=324 endy=322
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Text font=22 startx=215 starty=290 endx=420 endy=355
Black direction=1 alignment=0 fontweight=600 fontsize=20
"Speed1"
$END-OBJECT
***********
$OBJECT Gauge font=1 startx=165 starty=310 endx=324 endy=500
N1Percent DIAL_NEEDLE
peakparam=ctcurphaseda labelcolor=White needlecolor=Black numsoutside=0
ticfont=2 format=0.0 max=120.000000 min=0.000000 ticfontsize=10 axislinewidth=5 axislinespace=3 unitfontsize=15
label="" units="%" labelinter=1 ticinter=10.000000 radius=79
parambox=2 paramboxcolor=Magenta shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=165 starty=480 endx=324 endy=482
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=174 starty=500 endx=320 endy=551
labelspace=3 unitspace=0 direction=1 spacing=5 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=23 fontweight=500 labelfontsize=20 labelfontweight=600 unitfontsize=14
N1RPM type="FLOAT" width=5 prec=0 label="" units="RPM"
N1Percent type="FLOAT" width=5 prec=2 label="" units="%"
$END-OBJECT
***********
$OBJECT Box font=1 startx=327 starty=290 endx=486 endy=566
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=327 starty=320 endx=486 endy=322
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Text font=22 startx=350 starty=290 endx=486 endy=355
Black direction=1 alignment=0 fontweight=600 fontsize=20
"Oil Pressure"
$END-OBJECT
***********
$OBJECT Gauge font=1 startx=327 starty=310 endx=486 endy=500
CalibOilOutPress DIAL_NEEDLE
peakparam=ctcurphaseda labelcolor=White needlecolor=Black numsoutside=0
ticfont=2 format=0.0 max=100.000000 min=0.000000 ticfontsize=10 axislinewidth=5 axislinespace=3 unitfontsize=15
label="" units="PSI" labelinter=2 ticinter=10.000000 radius=79
parambox=2 paramboxcolor=Magenta shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=327 starty=480 endx=486 endy=482
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=359 starty=500 endx=459 endy=648
labelspace=6 unitspace=0 direction=1 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=23 fontweight=500 labelfontsize=20 labelfontweight=600 unitfontsize=14
CalibOilOutPress type="DEFAULT" width=7 prec=2 label="" units="Psi"
$END-OBJECT
***********
$OBJECT Box font=1 startx=489 starty=290 endx=648 endy=566
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=489 starty=320 endx=648 endy=322
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Text font=22 startx=513 starty=290 endx=648 endy=320
Black direction=1 alignment=0 fontweight=600 fontsize=20
"Shaft Load"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=490 starty=340 endx=505 endy=355
CorrectAdapter_Stat15t textcolor=White  fontsize=18 fontweight=500 radius="20" type="LIGHT"
Green = 1
Red = 0
$END-OBJECT
***********
$OBJECT Gauge font=1 startx=489 starty=310 endx=648 endy=500
ctcurwireda DIAL_NEEDLE
peakparam=ctcurphaseda labelcolor=White needlecolor=Black numsoutside=0
ticfont=2 format=0.0 max=200.000000 min=0.000000 ticfontsize=10 axislinewidth=5 axislinespace=3 unitfontsize=15
label="" units="KW" labelinter=2 ticinter=20.000000 radius=79
parambox=2 paramboxcolor=Magenta shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=489 starty=480 endx=648 endy=482
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Buttonobj font=1 startx=494 starty=500 endx=539 endy=542
parameter=ctcurwireda type=LIGHTSWITCH
labelcolor=Black warncolor=Yellow offcolor=DarkGray fontsize=15 fontweight=500
defaultstate=0
$STATE 1
label="SHPSL"
oncolor=Green
$END-STATE
$STATE 0
label="GENSL"
oncolor=Red
$END-STATE
$END-OBJECT
***********
$OBJECT Digital font=20 startx=539 starty=493 endx=599 endy=545
labelspace=6 unitspace=0 direction=1 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=25 fontweight=500 labelfontsize=26 labelfontweight=600 unitfontsize=18
VSVClose5 type="SECONDS" width=4 prec=2 label="" units="KF"
$END-OBJECT
***********
$OBJECT Buttonobj font=1 startx=599 starty=498 endx=644 endy=540
parameter=ctcurwireda type=LIGHTSWITCH
labelcolor=Black warncolor=Yellow offcolor=DarkGray fontsize=15 fontweight=500
defaultstate=0
$STATE 1
label="HP"
oncolor=Green
$END-STATE
$STATE 0
label="KW"
oncolor=Red
$END-STATE
$END-OBJECT
***********
$OBJECT Box font=1 startx=651 starty=290 endx=810 endy=566
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=651 starty=320 endx=810 endy=322
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Text font=22 startx=660 starty=290 endx=810 endy=330
Black direction=1 alignment=0 fontweight=600 fontsize=20
"Bleed Pressure"
$END-OBJECT
***********
$OBJECT Gauge font=1 startx=651 starty=310 endx=810 endy=500
CalibPB DIAL_NEEDLE
peakparam=ctcurphaseda labelcolor=White needlecolor=Black numsoutside=0
ticfont=2 format=0.0 max=70.000000 min=10.000000 ticfontsize=10 axislinewidth=5 axislinespace=3 unitfontsize=15
label="" units="PSIA" labelinter=2 ticinter=5.000000 radius=79
parambox=2 paramboxcolor=Magenta shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=651 starty=480 endx=810 endy=587
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=679 starty=496 endx=788 endy=546
labelspace=6 unitspace=0 direction=1 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=25 fontweight=500 labelfontsize=26 labelfontweight=600 unitfontsize=18
CalibPB type="FLOAT" width=7 prec=2 label="" units="PSIA"
$END-OBJECT
***********
$OBJECT Box font=1 startx=813 starty=290 endx=972 endy=566
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=813 starty=320 endx=972 endy=322
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Text font=22 startx=830 starty=290 endx=972 endy=330
Black direction=1 alignment=0 fontweight=600 fontsize=20
"Bleed Flow"
$END-OBJECT
***********
$OBJECT Gauge font=1 startx=813 starty=310 endx=972 endy=500
ctcurwireda DIAL_NEEDLE
peakparam=ctcurphaseda labelcolor=White needlecolor=Black numsoutside=0
ticfont=2 format=0.0 max=400.000000 min=0.000000 ticfontsize=10 axislinewidth=5 axislinespace=3 unitfontsize=15
label="" units="LB/Min" labelinter=2 ticinter=50.000000 radius=79
parambox=2 paramboxcolor=Magenta shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=813 starty=480 endx=972 endy=482
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Buttonobj font=1 startx=815 starty=500 endx=860 endy=542
parameter=ctcurwireda type=LIGHTSWITCH
labelcolor=Black warncolor=Yellow offcolor=DarkGray fontsize=15 fontweight=500
defaultstate=0
$STATE 1
label="WHCDAN"
oncolor=Green
$END-STATE
$STATE 0
label="WB"
oncolor=Red
$END-STATE
$END-OBJECT
***********
$OBJECT Digital font=20 startx=872 starty=496 endx=942 endy=548
labelspace=6 unitspace=0 direction=1 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=23 fontweight=500 labelfontsize=26 labelfontweight=600 unitfontsize=18
VSVClose5 type="SECONDS" width=5 prec=2 label="" units="PSIA"
$END-OBJECT
***********
$OBJECT Box font=1 startx=975 starty=290 endx=1134 endy=566
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=975 starty=320 endx=1134 endy=322
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Text font=22 startx=1020 starty=290 endx=1134 endy=355
Black direction=1 alignment=0 fontweight=600 fontsize=20
"Speed2"
$END-OBJECT
***********
$OBJECT Gauge font=1 startx=975 starty=310 endx=1134 endy=500
N2Percent DIAL_NEEDLE
peakparam=ctcurphaseda labelcolor=White needlecolor=Black numsoutside=0
ticfont=2 format=0.0 max=120.000000 min=0.000000 ticfontsize=10 axislinewidth=5 axislinespace=3 unitfontsize=15
label="" units="%" labelinter=2 ticinter=10.000000 radius=79
parambox=2 paramboxcolor=Magenta shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=975 starty=480 endx=1134 endy=482
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=985 starty=496 endx=1132 endy=547
labelspace=3 unitspace=0 direction=1 spacing=5 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=23 fontweight=500 labelfontsize=20 labelfontweight=600 unitfontsize=14
N2RPM type="FLOAT" width=5 prec=0 label="" units="RPM"
N2Percent type="FLOAT" width=5 prec=2 label="" units="%"
$END-OBJECT
***********
$OBJECT Bar font=20 startx=1150 starty=323 endx=1195 endy=569
labelspace=5 unitspace=1 labelcolor=Black length=205 \
height=8 format=3.0 ticpos=RIGHT dir=1 shading=0 spacing=6 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 ticfontsize=14
CalibCBVP target=CalibCBVP label="" units="" min=0 max=100 ticinter=10 labelinter=2
$END-OBJECT
***********
$OBJECT Bar font=20 startx=1220 starty=323 endx=1270 endy=569
labelspace=5 unitspace=1 labelcolor=Black length=205 \
height=8 format=3.0 ticpos=LEFT dir=1 shading=0 spacing=6 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 ticfontsize=14
CalibFBVP target=CalibFBVP label="" units="" min=0 max=100 ticinter=10 labelinter=2
$END-OBJECT
***********
$OBJECT Text font=22 startx=1180 starty=270 endx=1280 endy=320
Black direction=1 alignment=0 fontsize=16 fontweight=600
"Cell\nValve"
$END-OBJECT
$END-DISPLAY

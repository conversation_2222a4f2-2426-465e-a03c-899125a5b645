{"name": "wuksdk", "version": "0.0.1", "description": "wuksdk", "private": true, "scripts": {"dev:base": "yarn workspace @wuk/base run dev", "build:base": "yarn workspace @wuk/base run build", "pub:base": "yarn workspace @wuk/base run pub", "dev:cfg": "yarn workspace @wuk/cfg run dev", "build:cfg": "yarn workspace @wuk/cfg run build", "pub:cfg": "yarn workspace @wuk/cfg run pub", "wkp:cfg": "yarn workspace @wuk/cfg run wkp"}, "repository": {"type": "git", "url": "http://git.100vs.com/webs/libs/wksdk"}, "author": "lovisliao", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.9.0", "@typescript-eslint/parser": "^5.9.0", "typescript": "^4.7.3", "eslint": "^8.45.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "prettier": "^2.1.2"}, "workspaces": {"packages": ["packages/*"]}, "resolutions": {"@wuk/base": "*", "@wuk/cfg": "*"}, "overrides": {"@wuk/base": "*", "@wuk/cfg": "*"}, "eslintIgnore": ["**/build", "**/proto", "**/dist"], "lint-staged": {"packages/**/*.{js,ts}": "eslint --fix"}}
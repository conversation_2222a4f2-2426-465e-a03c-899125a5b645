/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let VibSystem={$keys:["measurement_rate","band_width","fft_lines"],measurement_rate:types.int32,band_width:types.int32,fft_lines:types.int32},VibInput={$keys:["index","sensitivity","gain","input_range","transducer_type","ad_gain","fft_size","sampling_rate"],index:types.int32,sensitivity:types.float32,gain:types.int32,input_range:types.float32,transducer_type:types.int32,ad_gain:types.int32,fft_size:types.int32,sampling_rate:types.int32},VibBandFilter={$keys:["index","vib_channel","output_units","detector_type","time_constant","filter_shape","filter_type","upper_cutoff","lower_cutoff"],index:types.int32,vib_channel:types.int32,output_units:types.int32,detector_type:types.int32,time_constant:types.int32,filter_shape:types.int32,filter_type:types.int32,upper_cutoff:types.int32,lower_cutoff:types.int32},VibTachInput={$keys:["index","tach_processing","rotor_speed","tach_freq","sensitivity"],index:types.int32,tach_processing:types.int32,rotor_speed:types.float32,tach_freq:types.float32,sensitivity:types.float32},VibTFInput={$keys:["index","vib_channel","tach_channel","output_units","detector_type","full_scale_units","full_scale_volts","filter_specification","filter_q","band_width","time_constant","order_tracking"],index:types.int32,vib_channel:types.int32,tach_channel:types.int32,output_units:types.int32,detector_type:types.int32,full_scale_units:types.float32,full_scale_volts:types.float32,filter_specification:types.int32,filter_q:types.int32,band_width:types.int32,time_constant:types.int32,order_tracking:types.int32},VibSpectrum={$keys:["index","output_units","start_frequency","end_frequency","detector_type","full_scale_units"],index:types.int32,output_units:types.int32,start_frequency:types.int32,end_frequency:types.int32,detector_type:types.int32,full_scale_units:types.float32},VibSetupCfg={$MAX:7e3,$MIN:2e3,$keys:["system","vib_inputs","band_filters","tach_inputs","tf_inputs","spectrums"],system:VibSystem,vib_inputs:types.arrayOf(VibInput),band_filters:types.arrayOf(VibBandFilter),tach_inputs:types.arrayOf(VibTachInput),tf_inputs:types.arrayOf(VibTFInput),spectrums:types.arrayOf(VibSpectrum)};export{VibSystem,VibInput,VibBandFilter,VibTachInput,VibTFInput,VibSpectrum,VibSetupCfg};
/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Dash as DashModal,
  Eec as EecModal,
  Engine as EngineModal
} from '../modal/engine.mode'

export class Dash extends MapProto<Dash> {
  eec_key = ''
  name = ''

  constructor(val?: Partial<Dash>) {
    super(DashModal, 'Dash')
    val && this.assign(val)
  }
}

export class Eec extends MapProto<Eec> {
  eec_prompt_string = ''
  list: Array<string> = []

  constructor(val?: Partial<Eec>) {
    super(EecModal, 'Eec')
    val && this.assign(val)
  }
}

export class Engine extends RootProto<Engine> {
  eec: Record<string, Eec> = {}
  dash: Array<Dash> = []

  constructor(val?: Partial<Engine>) {
    super(EngineModal, Engine.key)
    val && this.assign(val)
  }

  static get maxType() {
    return EngineModal.$MAX
  }

  static get minType() {
    return EngineModal.$MIN
  }

  static get uri() {
    return URI(EngineModal.$MAX, EngineModal.$MIN)
  }

  static get types(): [number, number] {
    return [EngineModal.$MAX, EngineModal.$MIN]
  }

  static get key() {
    return 'Engine'
  }
}

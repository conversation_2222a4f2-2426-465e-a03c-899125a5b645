import { limits } from '../proto'
import { PickField } from '../base'
import { BaseItr, BaseMtr, IBase } from './IBase'
import { MapProto } from '@wuk/wkp'

export type CfgLimt = PickField<limits.CfgOfLimit, MapProto<limits.CfgOfLimit>>
export type RateLimt = PickField<limits.RateOfLimit, MapProto<limits.RateOfLimit>>
export type LimitCounter = PickField<limits.LimitCounter, MapProto<limits.LimitCounter>>
export type AlarmRelay = PickField<limits.AlarmRelay, MapProto<limits.AlarmRelay>>
export type LimitCfg = {
  cfg_list: CfgLimt[]
  rate_list: RateLimt[]
}
export type LimitParameter = {
  param_id: string
  start: LimitCfg
  run: LimitCfg
}

export interface LimitOptions {
  alarm_relay: AlarmRelay
  limit_counters: LimitCounter[]
  parameters: LimitParameter[]
}

export abstract class ILimit extends IBase<ILimit> {
  abstract readOptions(): Promise<LimitOptions>
  abstract modifyOptions(val: Partial<LimitOptions>): Promise<boolean>

  abstract modifyAlarmRelay(val: Partial<AlarmRelay>): Promise<boolean>

  abstract removeLimitCounter(index: number): Promise<boolean>
  abstract addLimitCounter(val: LimitCounter, index?: number): Promise<boolean>
  abstract modifyLimitCounter(index: number, val: Partial<LimitCounter>): Promise<boolean>

  abstract removeLimitParameter(index: number): Promise<boolean>
  abstract addLimitParameter(val: LimitParameter, index?: number): Promise<boolean>
  abstract modifyLimitParameter(index: number, val: Partial<LimitParameter>): Promise<boolean>

  static override get NAME() {
    return 'Limit'
  }

  static get OnLimitOptions() {
    return 'Limit.OnLimitOptions'
  }
}

export type LimitItr = BaseItr<ILimit>
export type LimitMtr = BaseMtr<ILimit>

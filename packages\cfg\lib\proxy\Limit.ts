import { <PERSON><PERSON><PERSON>Window } from 'electron'
import {
  AlarmRelay,
  ICfg,
  IEngine,
  ILimit,
  LimitCounter,
  LimitOptions,
  LimitParameter
} from '../interfaces'
import { BaseProxy, method } from './Base'

import { limits } from '../proto'

export class Limit extends BaseProxy<ILimit> implements ILimit {
  private _options: LimitOptions

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(ILimit.NAME, master, win)

    this._options = {
      alarm_relay: {
        input: '',
        clear: '',
        output: ''
      },
      limit_counters: [],
      parameters: []
    }
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readOptions(): Promise<LimitOptions> {
    return this._options
  }

  @method()
  async modifyOptions(val: Partial<LimitOptions>): Promise<boolean> {
    if (val.alarm_relay !== undefined) this._options.alarm_relay = val.alarm_relay
    if (val.limit_counters !== undefined) this._options.limit_counters = val.limit_counters
    if (val.parameters !== undefined) this._options.parameters = val.parameters

    const changed = await this.writeLimitOptions()
    changed && this.emit(ILimit.OnLimitOptions)

    return changed
  }

  @method()
  async modifyAlarmRelay(val: Partial<AlarmRelay>): Promise<boolean> {
    if (val.input !== undefined) this._options.alarm_relay.input = val.input
    if (val.clear !== undefined) this._options.alarm_relay.clear = val.clear
    if (val.output !== undefined) this._options.alarm_relay.output = val.output

    const changed = await this.writeLimitOptions()
    changed && this.emit(ILimit.OnLimitOptions)

    return changed
  }

  @method()
  async removeLimitCounter(index: number): Promise<boolean> {
    this._options.limit_counters.splice(index, 1)

    const changed = await this.writeLimitOptions()
    changed && this.emit(ILimit.OnLimitOptions)

    return changed
  }

  @method()
  async addLimitCounter(val: LimitCounter, index?: number): Promise<boolean> {
    if (index === undefined || index === null) {
      this._options.limit_counters.push(val)
    } else {
      this._options.limit_counters.splice(index, 0, val)
    }

    const changed = await this.writeLimitOptions()
    changed && this.emit(ILimit.OnLimitOptions)

    return changed
  }

  @method()
  async modifyLimitCounter(index: number, val: Partial<LimitCounter>): Promise<boolean> {
    if (this._options.limit_counters.length <= index) {
      return false
    }
    const item = this._options.limit_counters[index]
    if (val.color !== undefined) item.color = val.color
    if (val.param_id !== undefined) item.param_id = val.param_id

    const changed = await this.writeLimitOptions()
    changed && this.emit(ILimit.OnLimitOptions)

    return changed
  }

  @method()
  async removeLimitParameter(index: number): Promise<boolean> {
    this._options.parameters.splice(index, 1)

    const changed = await this.writeLimitOptions()
    changed && this.emit(ILimit.OnLimitOptions)

    return changed
  }

  @method()
  async addLimitParameter(val: LimitParameter, index?: number): Promise<boolean> {
    if (index === undefined || index === null) {
      this._options.parameters.push(val)
    } else {
      this._options.parameters.splice(index, 0, val)
    }

    const changed = await this.writeLimitOptions()
    changed && this.emit(ILimit.OnLimitOptions)

    return changed
  }

  @method()
  async modifyLimitParameter(index: number, val: Partial<LimitParameter>): Promise<boolean> {
    if (this._options.parameters.length <= index) {
      return false
    }
    const item = this._options.parameters[index]
    if (val.param_id !== undefined) item.param_id = val.param_id
    if (val.start !== undefined) item.start = val.start
    if (val.run !== undefined) item.run = val.run

    const changed = await this.writeLimitOptions()
    changed && this.emit(ILimit.OnLimitOptions)

    return changed
  }

  private get engineName() {
    return this.engine?.engineName || 'common'
  }

  private async loadLimits(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    const {
      alarm_relay,
      limit_counters = [],
      parameters = []
    } = (await this.cfg.read(limits.Limits, this.engineName, force)) || {}
    const { input = '', clear = '', output = '' } = alarm_relay || {}
    this._options.alarm_relay = { input, clear, output }
    this._options.limit_counters = limit_counters
    this._options.parameters = parameters
    this.emit(ILimit.OnLimitOptions)
  }

  private async writeLimitOptions() {
    if (this.engine && !this.engine.engineName) {
      return false
    }

    const alarm_relay = new limits.AlarmRelay(this._options.alarm_relay)
    const limit_counters = this._options.limit_counters.map(val => new limits.LimitCounter(val))
    const parameters = this._options.parameters.map(val => {
      const { cfg_list: start_cfg_list, rate_list: start_rate_list } = val.start
      const { cfg_list: run_cfg_list, rate_list: run_rate_list } = val.run
      let cfg_list = start_cfg_list.map(cfg => new limits.CfgOfLimit(cfg)) || []
      let rate_list = start_rate_list.map(cfg => new limits.RateOfLimit(cfg)) || []
      const start = new limits.LimitCfg({ cfg_list, rate_list })

      cfg_list = run_cfg_list.map(cfg => new limits.CfgOfLimit(cfg)) || []
      rate_list = run_rate_list.map(cfg => new limits.RateOfLimit(cfg)) || []
      const run = new limits.LimitCfg({ cfg_list, rate_list })

      return new limits.LimitParameter({
        param_id: val.param_id,
        start,
        run
      })
    })
    let changed = this.cfg.assign(
      limits.Limits,
      { alarm_relay, limit_counters, parameters },
      '',
      this.engineName
    )
    changed = changed && (await this.cfg.write(limits.Limits, this.engineName))

    return changed
  }

  private async loadOptions(force?: boolean) {
    await this.loadLimits(force)
  }

  private async clearOptions() {
    this.cfg.remove(limits.Limits, '', this.engineName)
  }
}

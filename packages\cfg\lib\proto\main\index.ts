/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  IncludeFile as IncludeFileModal,
  Main as MainModal
} from '../modal/main.mode'

export class IncludeFile extends MapProto<IncludeFile> {
  file = ''
  uri = ''
  depends: Array<string> = []

  constructor(val?: Partial<IncludeFile>) {
    super(IncludeFileModal, 'IncludeFile')
    val && this.assign(val)
  }
}

export class Main extends RootProto<Main> {
  scan_rate = ''
  test_mode = false
  pla_rig_param = ''
  pla_idle_default = ''
  pla_takeoff_default = ''
  crs_on_param = ''
  run_limits_param = ''
  includes: Array<IncludeFile> = []

  constructor(val?: Partial<Main>) {
    super(MainModal, Main.key)
    val && this.assign(val)
  }

  static get maxType() {
    return MainModal.$MAX
  }

  static get minType() {
    return MainModal.$MIN
  }

  static get uri() {
    return URI(MainModal.$MAX, MainModal.$MIN)
  }

  static get types(): [number, number] {
    return [MainModal.$MAX, MainModal.$MIN]
  }

  static get key() {
    return 'Main'
  }
}

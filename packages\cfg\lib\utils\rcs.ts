import { exec } from 'child_process'
import path from 'node:path'
import { existed, makeDir } from './file'

export enum RcsRes {
  RCS_OK = 0,
  RCS_ER = 1
}

export interface LogItem {
  version: string
  date: string
  state: string
  author: string
  lines: string
  msg: string
}

export interface FileLog {
  rcs: string
  working: string
  version: string
  logs: LogItem[]
}

export class Rcs {
  public static readonly kRCS = 'RCS'
  private static readonly kAuthor = 'Root'

  static init(root: string) {
    const dir = path.join(root, Rcs.kRCS)

    let result = false
    if (!existed(dir)) {
      result = makeDir(dir)
    }

    return result ? RcsRes.RCS_OK : RcsRes.RCS_ER
  }

  static isInited(root: string) {
    const dir = path.join(root, Rcs.kRCS)
    return existed(dir)
  }

  static async add(path: string, name: string, msg: string) {
    const result = await Rcs.exec(`cd ${path} && rcs -q -I -m"${msg}" ${name}`)
    return result ? RcsRes.RCS_OK : RcsRes.RCS_ER
  }

  static async ci(path: string, name: string, msg: string, locked = true) {
    const result = await Rcs.exec(
      `cd ${path} && rcs ci -q -f${(locked && ' -l') || ''} -w${
        Rcs.kAuthor
      } -t-"111" -m"${msg}" ${name}`
    )
    return result ? RcsRes.RCS_OK : RcsRes.RCS_ER
  }

  static async co(path: string, name: string, ver?: string) {
    const result = await Rcs.exec(
      `cd ${path} && rcs co -q -l -f -w${Rcs.kAuthor} ${(ver && `-r${ver}`) || ''} ${name}`
    )
    return result ? RcsRes.RCS_OK : RcsRes.RCS_ER
  }

  static async clean(path: string, name: string, ver?: string) {
    const result = await Rcs.exec(
      `cd ${path} && rcs clean -q -u ${(ver && `-r${ver}`) || ''} ${name}`
    )
    return result ? RcsRes.RCS_OK : RcsRes.RCS_ER
  }

  static async diff(path: string, name: string, ver1: string, ver2: string) {
    const result = await Rcs.exec(`cd ${path} && rcs diff -q -r${ver1} -r${ver2} ${name}`)
    return result ? RcsRes.RCS_OK : RcsRes.RCS_ER
  }

  static async merge(path: string, name: string, ver1: string, ver2: string) {
    const result = await Rcs.exec(`cd ${path} && rcs merge -p -q -r${ver1} -r${ver2} ${name}`)
    return result ? RcsRes.RCS_OK : RcsRes.RCS_ER
  }

  static async logs(path: string, name: string): Promise<FileLog | undefined> {
    const result = await Rcs.invoke(`cd ${path} && rcs log -q -w${Rcs.kAuthor} ${name}`)
    return Rcs.parse(result)
  }

  private static parse(txt: string): FileLog | undefined {
    txt = txt.replace(/-{3,}/g, '>>>')
    const [head = '', ...vers] = txt.split('>>>') || []
    const texts = head.match(
      /.*RCS file:\s*([\w|\.|\,|\s|\/]+)\n.*Working file:\s*([\w|\.|\,|\s|\/]+)\n.*head:\s*([\w|\.|\,|\s|\/]+)\n/
    )
    if (!texts?.length) {
      return undefined
    }

    const rcs = texts?.[1] || ''
    const working = texts?.[2] || ''
    const version = texts?.[3] || ''
    const logs: LogItem[] = []

    vers?.forEach(ver => {
      const [revision = '', des = '', msg = ''] = (ver.split('\n') || []).filter(it => !!it)
      const matchs = revision.match(/\s*revision\s+(.+)\s*/)
      const version = matchs?.[1] || ''

      let [date = '', author = '', state = '', lines = ''] = des.split(';') || []
      date = date.match(/\s*date:\s+(.+)\s*/)?.[1] || ''
      author = author.match(/\s*author:\s+(.+)\s*/)?.[1] || ''
      state = state.match(/\s*state:\s+(.+)\s*/)?.[1] || ''
      lines = lines.match(/\s*lines:\s+(.+)\s*/)?.[1] || ''

      logs.push({ version, date, author, state, lines, msg })
    })

    return { rcs, working, version, logs }
  }

  private static exec(cmd: string): Promise<boolean> {
    return new Promise(resolve => {
      exec(cmd, (error, stdout, stderr) => {
        if (error || stderr) {
          resolve(false)
        } else {
          resolve(true)
        }
      })
    })
  }

  private static invoke(cmd: string): Promise<string> {
    return new Promise(resolve => {
      exec(cmd, (error, stdout, stderr) => {
        if (error || stderr) {
          resolve('')
        } else {
          resolve(stdout || '')
        }
      })
    })
  }
}

# 类型定义文档
## 相关文档
- [IBase](IBase.md) - 基础接口
- [ISdk](ISdk.md) - SDK 基础接口
- [ICfg](ICfg.md) - 配置接口
- [ISvc](ISvc.md) - 服务接口
- [IWin](IWin.md) - 窗口接口
- [IMenu](IMenu.md) - 菜单接口
- [IPage](IPage.md) - 页面接口
- [IApp](IApp.md) - 应用程序接口
- [IEngine](IEngine.md) - 引擎接口
- [IDisplay](IDisplay.md) - 显示接口

## 概述
本文档定义了系统中使用的各种类型和枚举。这些类型和枚举用于配置管理、显示控制、信号处理、数据格式化等多个方面。

## 基础类型
```typescript
// 通用记录类型
export type RecordType = Record<string, any>

// 基础代理类型
export type IBaseProxy = RecordType
```

## 配置接口
```typescript
// 页面配置
export interface PageConfig extends RecordType {
  id: string    // 页面ID
  name: string  // 页面名称
}

// SDK配置
export interface SdkConfig extends RecordType {
  name: string    // SDK名称
  path?: string   // SDK路径（可选）
}
```

## 枚举定义

### 测试历史类型
```typescript
export enum TestHistoryType {
  /**
   * Standard
   */
  Standard = 1,
  /**
   * No History Data
   */
  NoHistoryData = 2
}
```

### CRT 分辨率类型
```typescript
// CRT分辨率
export enum CrtResolutionType {
  RL_19201200 = 0,
  RL_1920x1080 = 1,
  RL_1600x1200 = 2,
  RL_1280x1024 = 3
}

export const kCrtResolutionVal: Record<string, CrtResolutionType> = {
  WUXGA: CrtResolutionType.RL_19201200,
  FHD: CrtResolutionType.RL_1920x1080,
  HIRES: CrtResolutionType.RL_1600x1200,
  MEDRES: CrtResolutionType.RL_1280x1024
}

export const kCrtValResolution: Record<CrtResolutionType, string> = {
  0: 'WUXGA',
  1: 'FHD',
  2: 'HIRES',
  3: 'MEDRES'
}
```

### CRT 状态类型
```typescript
// CRT状态
export enum CrtStatusType {
  Active = 0,
  InActive = 1
}

export const kCrtStatusVal: Record<string, CrtStatusType> = {
  CONTROL: CrtStatusType.Active,
  ACTIVE: CrtStatusType.Active,
  INACTIVE: CrtStatusType.InActive
}

export const kCrtValStatus: Record<CrtStatusType, string> = {
  0: 'ACTIVE',
  1: 'INACTIVE'
}
```

### CRT 控制类型
```typescript
// CRT控制类型
export enum CrtControlType {
  None = 0,
  Touchscreen = 1,
  Nouse = 2,
  Keyboard = 3
}

export const kCrtControlVal: Record<string, CrtControlType> = {
  NONE: CrtControlType.None,
  TOUCHSCREEN: CrtControlType.Touchscreen,
  MOUSE: CrtControlType.Nouse,
  KEYBOARD: CrtControlType.Keyboard
}

export const kCrtValControl: Record<CrtControlType, string> = {
  0: 'NONE',
  1: 'TOUCHSCREEN',
  2: 'MOUSE',
  3: 'KEYBOARD'
}
```

### 信号滤波器类型
```typescript
// 信号滤波器类型
export enum SignalFilterType {
  Broadband_1 = 0,
  Broadband_2 = 1,
  Broadband_3 = 2,
  Broadband_4 = 3,
  Tracking_1 = 4,
  Tracking_2 = 5,
  Tracking_3 = 6,
  Tracking_4 = 7,
  Tracking_5 = 8,
  Tracking_6 = 9,
  Tracking_7 = 10,
  Tracking_8 = 11,
  Tracking_9 = 12,
  Tracking_10 = 13,
  Tracking_11 = 14,
  Tracking_12 = 15,
  Tracking_13 = 16,
  Tracking_14 = 17,
  Tracking_15 = 18,
  Tracking_16 = 19
}
```

### 信号类型
```typescript
// 信号类型
export enum SignalSigType {
  Amplitude = 0,
  Frequency = 1,
  Phase = 2,
  RPM = 3
}
```

### 信号校准模式
```typescript
// 信号校准模式
export enum SignalCalibMode {
  None = 0,
  VerifyOnly = 1
}
```

### 输入参数顺序类型
```typescript
// 输入参数顺序类型
export enum InputParamOrderType {
  /**
   * As Listed
   */
  AsListed = 0,
  /**
   * Alphabetical
   */
  Alphabetical = 1,
  /**
   * 1st/3rd
   */
  Fst3rd = 2
}

export const kInputParamOrderVal: Record<string, InputParamOrderType> = {
  AsListed: InputParamOrderType.AsListed,
  Alphabetical: InputParamOrderType.Alphabetical,
  '1st/3rd': InputParamOrderType.Fst3rd
}

export const kCrtValInputParamOrder: Record<InputParamOrderType, string> = {
  0: 'AsListed',
  1: 'Alphabetical',
  2: '1st/3rd'
}
```

### 时间格式类型
```typescript
// 时间格式
export enum TimeFormatType {
  /**
   * Standard
   */
  Standard = 0,
  /**
   * Military
   */
  Military = 1
}

export const kTimeFormatVal: Record<string, TimeFormatType> = {
  Standard: TimeFormatType.Standard,
  Military: TimeFormatType.Military
}

export const kValTimeFormat: Record<TimeFormatType, string> = {
  0: 'Standard',
  1: 'Military'
}
```

### 日期格式类型
```typescript
// 日期格式
export enum DateFormatType {
  YYMMDD = 0,
  YYYYMMDD = 1,
  MMDDYY = 2,
  MMDDYYYY = 3,
  DDMMYY = 4,
  DDMMYYYY = 5,
  MM_D_Y = 6
}

export const kDateFormatVal: Record<string, DateFormatType> = {
  YYMMDD: DateFormatType.YYMMDD,
  YYYYMMDD: DateFormatType.YYYYMMDD,
  MMDDYY: DateFormatType.MMDDYY,
  MMDDYYYY: DateFormatType.MMDDYYYY,
  DDMMYY: DateFormatType.DDMMYY,
  DDMMYYYY: DateFormatType.DDMMYYYY,
  MM_D_Y: DateFormatType.MM_D_Y
}

export const kValDateFormat: Record<DateFormatType, string> = {
  0: 'YYMMDD',
  1: 'YYYYMMDD',
  2: 'MMDDYY',
  3: 'MMDDYYYY',
  4: 'DDMMYY',
  5: 'DDMMYYYY',
  6: 'MM_D_Y'
}
```

### 测试模式类型
```typescript
// 测试模式
export enum TestModeType {
  /**
   * Test With Hardware
   */
  TestWithHardware = 0,
  /**
   * No Hardware
   */
  NoHardware = 1
}
```

### 显示模式类型
```typescript
// 显示模式
export enum DisplayModeType {
  None = 0,
  PullDown = 1,
  Increment = 2,
  Menu = 3
}

export const kDisplayModeTypeVal: Record<string, DisplayModeType> = {
  None: DisplayModeType.None,
  PullDown: DisplayModeType.PullDown,
  Increment: DisplayModeType.Increment,
  Menu: DisplayModeType.Menu
}

export const kDisplayModeValType: Record<DisplayModeType, string> = {
  0: 'None',
  1: 'PullDown',
  2: 'Increment',
  3: 'Menu'
}
```

### 测量速率类型
```typescript
// 测量速率
export enum MeasurementRateType {
  Rate_1Hz = 0,
  Rate_2Hz = 1,
  Rate_3Hz = 2,
  Rate_4Hz = 3,
  Rate_5Hz = 4,
  Rate_6Hz = 5,
  Rate_7Hz = 6,
  Rate_8Hz = 7,
  Rate_9Hz = 8,
  Rate_10Hz = 9
}
```

### 带宽类型
```typescript
// 带宽类型
export enum BandwidthType {
  Band_500Hz = 0,
  Band_1000Hz = 1,
  Band_2000Hz = 2,
  Band_5000Hz = 3,
  Band_10000Hz = 4
}
```

### FFT 线数类型
```typescript
// FFT行数
export enum FFTLinesType {
  FFT_200 = 0,
  FFT_400 = 1,
  FFT_800 = 2,
  FFT_1600 = 3
}
```

### 输入类型
```typescript
// 输入类型
export enum InputType {
  Input_1 = 0,
  Input_2 = 1,
  Input_3 = 2,
  Input_4 = 3
}
```

### 传感器类型
```typescript
// 传感器类型
export enum TransducerType {
  Transducer_Disabled = 0,
  Transducer_SingleendedAccel = 1,
  Transducer_DifferentialAccel = 2,
  Transducer_Velocity = 3,
  Transducer_ICPAccel = 4
}
```

### 放大器增益类型
```typescript
// 放大器增益类型
export enum AmplifierGainType {
  Gain_AutoSelect = 0,
  Gain_x1 = 1,
  Gain_x10 = 2
}
```

### A/D 满量程类型
```typescript
// A/D满量程类型
export enum ADFullScaleType {
  Scale_AutoSelect = 0,
  Scale_125Volts = 1,
  Scale_250Volts = 2,
  Scale_500Volts = 3,
  Scale_1000Volts = 4
}
```

### 宽带滤波器类型
```typescript
// 宽带滤波器类型
export enum BroadbanFilterType {
  Broadban_1 = 0,
  Broadban_2 = 1,
  Broadban_3 = 2,
  Broadban_4 = 3
}
```

### 输出单位类型
```typescript
// 输出单位类型
export enum OutputUnitsType {
  /**
   * Disabled
   */
  Disabled = 0,
  /**
   * Acceleration(g's)
   */
  Acceleration_Gs = 1,
  /**
   * Velocity(IPS)
   */
  Velocity_IPS = 2,
  /**
   * Displacement(Mils)
   */
  Displacement_Mils = 3,
  /**
   * Acceleration(m/sec2)
   */
  Acceleration_Msec2 = 4,
  /**
   * Displacement(um)
   */
  Displacement_Um = 5
}
```

### 宽带滤波器类型
```typescript
// 宽带滤波器类型
export enum BroadFilterType {
  /**
   * No Filtering
   */
  NoFiltering = 0,
  /**
   * Low Pass
   */
  LowPass = 1,
  /**
   * High Pass
   */
  HighPass = 2,
  /**
   * Band Pass
   */
  BandPass = 3
}
```

### 滤波器形状类型
```typescript
// 滤波器形状类型
export enum FilterShapeType {
  /**
   * Rectangular
   */
  Rectangular = 0,
  /**
   * 7 Pole Chebyshev
   */
  PoleChebyshev_7 = 1,
  /**
   * 6 Pole Butterworth
   */
  PoleButterworth_6 = 2,
  /**
   * User Defined
   */
  UserDefined = 3
}
```

### 时间常数类型
```typescript
// 时间常数类型
export enum TimeConstantType {
  /**
   * No Smoothing
   */
  No_Smoothing = 0,
  /**
   * 0.25 Sec Smoothing
   */
  Sec025_Smoothing = 1,
  /**
   * 0.50 Sec Smoothing
   */
  Sec050_Smoothing = 2,
  /**
   * 0.75 Sec Smoothing
   */
  Sec075_Smoothing = 3,
  /**
   * 1.00 Sec Smoothing
   */
  Sec100_Smoothing = 4,
  /**
   * 1.25 Sec Smoothing
   */
  Sec125_Smoothing = 5,
  /**
   * 1.50 Sec Smoothing
   */
  Sec150_Smoothing = 6
}
```

### 转速计输入类型
```typescript
// 转速计输入类型
export enum TachometerInputType {
  Input_1 = 0,
  Input_2 = 1,
  Input_3 = 2,
  Input_4 = 3
}
```

### 检测器类型
```typescript
// 检测器类型
export enum DetectorType {
  /**
   * Peak
   */
  Peak = 0,
  /**
   * Peak To Peak
   */
  PeakToPeak = 1,
  /**
   * RMS
   */
  RMS = 2,
  /**
   * Average
   */
  Average = 3
}
```

### 滤波器规格类型
```typescript
// 滤波器规格类型
export enum FilterSpecType {
  /**
   * Constant Bandwidth
   */
  Constant_Bandwidth = 0,
  /**
   * Constant Q
   */
  Constant_Q = 1
}
```

### 滤波器 Q 值类型
```typescript
// 滤波器Q值类型
export enum FilterQType {
  /**
   * Q = 10
   */
  Q_10 = 0,
  /**
   * Q = 20
   */
  Q_20 = 1,
  /**
   * Q = 30
   */
  Q_30 = 2
}
```

### 执行时机类型
```typescript
// 执行时机类型
export enum WhenToExcuteType {
  SelectEngine = 0,
  InitDisplays = 1,
  OpenTest = 2,
  LoadTables = 3,
  AllTimes = 4
}
```

### 计算执行类型
```typescript
// 计算执行类型
export enum CalcsExcuteType {
  Common = 0,
  EngineSpecific = 1
}
```

### 信号计算类型
```typescript
// 信号计算类型
export enum CalcsSignalType {
  Calibrate = 0
}
```

### 定时器类型
```typescript
// 定时器类型
export enum TimerType {
  Reset = 0,
  NoReset = 1
}
```

## 常量映射
系统提供了多个常量映射对象，用于在枚举值和字符串表示之间进行转换：

1. CRT分辨率映射：
```typescript
export const kCrtResolutionVal: Record<string, CrtResolutionType>
export const kCrtValResolution: Record<CrtResolutionType, string>
```

2. CRT状态映射：
```typescript
export const kCrtStatusVal: Record<string, CrtStatusType>
export const kCrtValStatus: Record<CrtStatusType, string>
```

3. CRT控制映射：
```typescript
export const kCrtControlVal: Record<string, CrtControlType>
export const kCrtValControl: Record<CrtControlType, string>
```

4. 输入参数顺序映射：
```typescript
export const kInputParamOrderVal: Record<string, InputParamOrderType>
export const kCrtValInputParamOrder: Record<InputParamOrderType, string>
```

5. 时间格式映射：
```typescript
export const kTimeFormatVal: Record<string, TimeFormatType>
export const kValTimeFormat: Record<TimeFormatType, string>
```

6. 日期格式映射：
```typescript
export const kDateFormatVal: Record<string, DateFormatType>
export const kValDateFormat: Record<DateFormatType, string>
```

7. 显示模式映射：
```typescript
export const kDisplayModeTypeVal: Record<string, DisplayModeType>
export const kDisplayModeValType: Record<DisplayModeType, string>
```

## 注意事项
1. 所有枚举类型都使用数字作为值
2. 提供了完整的类型映射，支持双向转换
3. 部分类型包含详细的注释说明
4. 类型定义遵循TypeScript的类型系统规范
5. 支持泛型和类型继承
6. 提供了完整的类型安全保证 
/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  IncludeFile as IncludeFileModal,
  CalcMain as CalcMainModal
} from '../modal/calcmain.mode'

export class IncludeFile extends MapProto<IncludeFile> {
  file = ''
  uri = ''
  depends: Array<string> = []

  constructor(val?: Partial<IncludeFile>) {
    super(IncludeFileModal, 'IncludeFile')
    val && this.assign(val)
  }
}

export class CalcMain extends RootProto<CalcMain> {
  includes: Array<IncludeFile> = []

  constructor(val?: Partial<CalcMain>) {
    super(CalcMainModal, CalcMain.key)
    val && this.assign(val)
  }

  static get maxType() {
    return CalcMainModal.$MAX
  }

  static get minType() {
    return CalcMainModal.$MIN
  }

  static get uri() {
    return URI(CalcMainModal.$MAX, CalcMainModal.$MIN)
  }

  static get types(): [number, number] {
    return [CalcMainModal.$MAX, CalcMainModal.$MIN]
  }

  static get key() {
    return 'CalcMain'
  }
}

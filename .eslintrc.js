const path = require('path')
module.exports = {
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended",
    "prettier/@typescript-eslint",
  ],
  env: {
    browser: true,
    es6: true,
    node: true,
  },
  globals: {
    Promise: true,
    Atomics: 'readonly',
    SharedArrayBuffer: 'readonly'
  },
  plugins: ["@typescript-eslint"],
  rules: {
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "no-useless-escape": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-unused-vars": "off",
    // "@typescript-eslint/no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
    "@typescript-eslint/no-empty-function": "off",
    "no-irregular-whitespace": "off",
    '@typescript-eslint/no-non-null-assertion': 'off',
    '@typescript-eslint/no-namespace': 'off'
  },
  overrides: [
    {
      files: ["*.ts"],
      rules: {
        'no-unused-vars': 'off',
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/camelcase": [
          "off"
        ],
        '@typescript-eslint/ban-types': [
          'error',
          {
            'types': {
              'Function': false
            }
          }
        ]
      }
    }
  ]
}

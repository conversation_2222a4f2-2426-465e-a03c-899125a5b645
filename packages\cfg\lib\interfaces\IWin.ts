import { BaseItr, BaseMtr, IBase } from './IBase'

export type Platform =
  | 'aix'
  | 'android'
  | 'darwin'
  | 'freebsd'
  | 'haiku'
  | 'linux'
  | 'openbsd'
  | 'sunos'
  | 'win32'
  | 'cygwin'
  | 'netbsd'
export type Architecture =
  | ''
  | 'arm'
  | 'arm64'
  | 'ia32'
  | 'mips'
  | 'mipsel'
  | 'ppc'
  | 'ppc64'
  | 's390'
  | 's390x'
  | 'x64'

export type DialogProperty =
  | 'openFile'
  | 'openDirectory'
  | 'multiSelections'
  | 'showHiddenFiles'
  | 'createDirectory'
  | 'promptToCreate'
  | 'noResolveAliases'
  | 'treatPackageAsDirectory'
  | 'dontAddToRecent'

export enum AppState {
  AP_MAIN = 0, // 主界面
  AP_ENGINE = 1 // 发动机
}

export abstract class IWin extends IBase<IWin> {
  abstract get platform(): Platform
  abstract get arch(): Architecture

  abstract showOpenDialog(title: string, properties: DialogProperty[]): Promise<string>
  abstract getState(): Promise<AppState>
  abstract switchState(state: AppState): Promise<void>

  abstract openWindow(
    url: string,
    icon: string,
    title: string,
    width: number,
    height: number
  ): Promise<number>
  abstract closeWindow(id: number): Promise<boolean>
  abstract closeAll(): Promise<boolean>

  static override get NAME() {
    return 'Win'
  }

  static get ONSTATE() {
    return 'App.ONSTATE'
  }
}

export type WinMtr = BaseMtr<IWin>
export type WinItr = BaseItr<IWin>

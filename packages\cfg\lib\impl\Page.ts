import { Invoker, Root } from '../base'
import { IPage, PageConfig } from '../interfaces'

export default class Page extends Root implements IPage {
  private _pageId: string
  private _pageName: string
  private _invoker: Invoker

  constructor(config: PageConfig, invoker: Invoker) {
    super(IPage.NAME)

    this._pageId = config.id || ''
    this._pageName = config.name || ''

    this._modules = [(this._invoker = invoker)]
  }

  get pageId(): string {
    return this._pageId
  }

  get pageName(): string {
    return this._pageName
  }

  get sdkReady(): boolean {
    return true
  }

  override excute(key: string, ...args: any[]) {
    return this._invoker.excute(key, ...args)
  }
}

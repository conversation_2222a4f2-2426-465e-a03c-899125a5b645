/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  IncludeFile as IncludeFileModal,
  PsiCfg as PsiCfgModal
} from '../modal/psicfg.mode'

export class IncludeFile extends MapProto<IncludeFile> {
  name = ''
  uri = ''
  depends: Array<string> = []

  constructor(val?: Partial<IncludeFile>) {
    super(IncludeFileModal, 'IncludeFile')
    val && this.assign(val)
  }
}

export class PsiCfg extends RootProto<PsiCfg> {
  includes: Array<IncludeFile> = []

  constructor(val?: Partial<PsiCfg>) {
    super(PsiCfgModal, PsiCfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return PsiCfgModal.$MAX
  }

  static get minType() {
    return PsiCfgModal.$MIN
  }

  static get uri() {
    return URI(PsiCfgModal.$MAX, PsiCfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [PsiCfgModal.$MAX, PsiCfgModal.$MIN]
  }

  static get key() {
    return 'PsiCfg'
  }
}

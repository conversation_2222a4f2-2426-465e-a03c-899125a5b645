{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "baseUrl": ".",
    "outDir": "dist",
    // "declarationDir": "dist/types",
		"declaration": true,
		// "noUnusedLocals": true,
		// "noUnusedParameters": true,
    "allowJs": true,
		"esModuleInterop": true,
    "paths": {
      "lib/*": [
        "./lib/*"
      ]
    },
  },
  "include": [
    "lib",
  ],
  "exclude": [
    "node_modules",
    "lib/**/*.js"
  ]
}

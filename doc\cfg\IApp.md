# IApp 接口文档

## 相关文档
- [类型定义](type.md) - 包含 `CrtControlType`、`CrtResolutionType`、`CrtStatusType`、`DateFormatType`、`InputParamOrderType`、`TestHistoryType`、`TimeFormatType` 等类型定义
- [IBase](IBase.md) - 基础接口
- [ICfg](ICfg.md) - 配置接口
- [IEngine](IEngine.md) - 引擎接口
- [IMenu](IMenu.md) - 菜单接口
- [IWin](IWin.md) - 窗口接口

## 概述
`IApp` 是一个抽象类，继承自 `IBase<IApp>`，用于定义应用程序的基本功能和配置管理。它提供了客户管理、版本控制、引擎管理、系统配置等功能。

## 类型定义

### 客户信息
```typescript
export interface AppCustomer {
  name: string
  label: string
  version: string
}
```

### 版本信息
```typescript
export interface CfgVersion {
  name: string
  md5: string
  comments: string
}
```

### 引擎信息
```typescript
export interface AppEngine {
  name: string
  label: string
}
```

### RGB 配置
```typescript
export interface RgbItem {
  r: number
  g: number
  b: number
  name: string
}
```

### 音频配置
```typescript
export interface AudoItem {
  name: string
  file: string
  repeat: number
  priority: number
}
```

### CRT 配置
```typescript
export interface CrtItem {
  name: string
  x_address: string
  status: CrtStatusType
  resolution: CrtResolutionType
  control: CrtControlType
}
```

### 硬件配置
```typescript
export interface Hardware {
  id: string
  name: string
  inter: string
  printer_type: string
  scan_rate: number
  inst_addr: number
}
```

### 系统选项
```typescript
export interface SystemOptions {
  /**
   * Initial Alarm State
   */
  initial_alarm_state: boolean
  /**
   * Input Param Order
   */
  input_param_order: InputParamOrderType
  /**
   * Reference Test
   */
  reference_test: boolean
  /**
   * Automatic Open On Startup
   */
  automatic_open_on_startup: boolean
  /**
   * Store Messages To Database
   */
  store_messages_to_database: boolean
  /**
   * Rcs Control
   */
  rcs_control: boolean
  /**
   * Test History
   */
  test_history: TestHistoryType
  /**
   * Invalid Value
   */
  invalid_value: string
  /**
   * Test ID Prompt
   */
  test_id_prompt: string
  /**
   * System Scan Rate
   */
  system_scan_rate: string
  /**
   * Trigger Control Paramter
   */
  trigger_control_paramter: string
}
```

### 打印模式选项
```typescript
export interface PrintModeOptions {
  /**
   * Print Messages
   */
  print_messages: boolean
  /**
   * Print Stored Scans
   */
  print_stored_scans: boolean
  /**
   * Print Stored Comments
   */
  print_stored_comments: boolean
  /**
   * Print Stored Displays
   */
  print_stored_displays: boolean
}
```

### 数据格式选项
```typescript
export interface DataFormatOptions {
  /**
   * Time Format
   */
  time_format: TimeFormatType
  /**
   * Date Format
   */
  date_format: DateFormatType
  /**
   * Param Color
   */
  param_color: string
}
```

### CRS 记录选项
```typescript
export interface CRSRecordingOptions {
  /**
   * switch
   */
  is_on: boolean
  /**
   * Number of mins
   */
  number_of_mins: number
  /**
   * Division of scan rate
   */
  division_of_scan_rate: number
}
```

### CRS 选项
```typescript
export interface CRSOptions {
  /**
   * Auto Backup
   */
  auto_backup: boolean
  /**
   * Recording Control
   */
  recording_control: boolean
  /**
   * Maximum number of tests
   */
  maximum_number_of_tests: string
  /**
   * Disk space to leave free（Mb）
   */
  disk_space_to_leave_free: string
  /**
   * File System
   */
  file_system: string
  /**
   * Continuous Recording
   */
  continuous_recording: CRSRecordingOptions
  /**
   * Cyclic Recording
   */
  cyclic_recording: CRSRecordingOptions
}
```

### CRT 选项
```typescript
export interface CrtOptions {
  /**
   * Display Update Rate
   */
  rate: number
  /**
   * Crts List
   */
  list: CrtItem[]
}
```

### 音频选项
```typescript
export interface AudioOptions {
  /**
   * Audio Host Address
   */
  audio_host_address: string
  /**
   * Sound List
   */
  list: AudoItem[]
}
```

### 设备选项
```typescript
export interface DeviceOptions {
  list: Hardware[]
}
```

### 颜色选项
```typescript
export interface ColorOptions {
  list: RgbItem[]
}
```

## 接口定义
### IApp 抽象类
```typescript
export abstract class IApp extends IBase<IApp> {
  abstract get menu(): IMenu
  abstract get win(): IWin
  abstract get cfg(): ICfg
  abstract get eng(): IEngine

  abstract test(): Promise<Record<string, any>>

  abstract loadCustomers(): Promise<AppCustomer[]>
  abstract loadVersions(): Promise<CfgVersion[]>
  abstract loadEngines(): Promise<AppEngine[]>

  abstract getCustomer(): Promise<AppCustomer | undefined>
  abstract getVersion(): Promise<CfgVersion | undefined>
  abstract changeCustomer(name: string): Promise<boolean>
  abstract importVerison(name: string): Promise<boolean>
  abstract exportVerison(comments: string): Promise<boolean>

  abstract getEngine(): Promise<AppEngine | undefined>
  abstract openEngine(name: string): Promise<boolean>
  abstract createEngine: (name: string) => Promise<boolean>
  abstract copyEngine(name: string, newName: string): Promise<boolean>
  abstract deleteEngine(name: string): Promise<boolean>

  // System Options
  abstract readSystemOptions(): Promise<SystemOptions>
  abstract writeSystemOptions(val: Partial<SystemOptions>): Promise<boolean>

  // Print Modes
  abstract readPrintModes(): Promise<PrintModeOptions>
  abstract writePrintModes(val: Partial<PrintModeOptions>): Promise<boolean>

  // Data Formats
  abstract readDataFormat(): Promise<DataFormatOptions>
  abstract writeDataFormat(val: Partial<DataFormatOptions>): Promise<boolean>

  // CRS Options
  abstract readCRSOptions(): Promise<CRSOptions>
  abstract writeCRSOptions(val: Partial<CRSOptions>): Promise<boolean>

  // Crts Options
  abstract readCrtOptions(): Promise<CrtOptions>
  abstract setCrtRate(val: number): Promise<boolean>
  abstract removeCrt(index: number): Promise<boolean>
  abstract addCrt(val: CrtItem, index?: number): Promise<boolean>
  abstract modifyCrt(index: number, val: Partial<CrtItem>): Promise<boolean>

  // Audio Options
  abstract readAudioOptions(): Promise<AudioOptions>
  abstract setHostAddress(val: string): Promise<boolean>
  abstract removeAudio(index: number): Promise<boolean>
  abstract addAudio(val: AudoItem, index?: number): Promise<boolean>
  abstract modifyAudio(index: number, val: Partial<AudoItem>): Promise<boolean>

  // Device Options
  abstract readDeviceOptions(): Promise<DeviceOptions>
  abstract removeDevice(index: number): Promise<boolean>
  abstract addDevice(val: Hardware, index?: number): Promise<boolean>
  abstract modifyDevice(index: number, val: Partial<Hardware>): Promise<boolean>

  // Color Options
  abstract readColorOptions(): Promise<ColorOptions>
  abstract removeColor(index: number): Promise<boolean>
  abstract addColor(val: RgbItem, index?: number): Promise<boolean>
  abstract modifyColor(index: number, val: Partial<RgbItem>): Promise<boolean>

  static override get NAME() { return 'App' }
  static get ONTEST() { return 'App.ONTEST' }
  static get ONCHANGED() { return 'App.ONCHANGED' }
  static get OnCustomer() { return 'App.OnCustomer' }
  static get OnCustomers() { return 'App.OnCustomers' }
  static get OnVersion() { return 'App.OnVersion' }
  static get OnVersions() { return 'App.OnVersions' }
  static get OnEngine() { return 'App.OnEngine' }
  static get OnEngines() { return 'App.OnEngines' }
  static get OnSystemOptions() { return 'App.OnSystemOptions' }
  static get OnPrintModes() { return 'App.OnPrintModes' }
  static get OnDataFormat() { return 'App.OnDataFormat' }
  static get OnCRSOptions() { return 'App.OnCRSOptions' }
  static get OnCrtOptions() { return 'App.OnCrtOptions' }
  static get OnAudioOptions() { return 'App.OnAudioOptions' }
  static get OnDeviceOptions() { return 'App.OnDeviceOptions' }
  static get OnColorOptions() { return 'App.OnColorOptions' }
}
```

## 方法说明

### 基础组件访问
- `menu`: 获取菜单管理器
- `win`: 获取窗口管理器
- `cfg`: 获取配置管理器
- `eng`: 获取引擎管理器

### 测试功能
- `test`: 执行测试并返回结果

### 客户管理
- `loadCustomers`: 加载客户列表
- `getCustomer`: 获取当前客户
- `changeCustomer`: 切换客户
- `importVerison`: 导入版本
- `exportVerison`: 导出版本

### 版本管理
- `loadVersions`: 加载版本列表
- `getVersion`: 获取当前版本

### 引擎管理
- `loadEngines`: 加载引擎列表
- `getEngine`: 获取当前引擎
- `openEngine`: 打开引擎
- `createEngine`: 创建引擎
- `copyEngine`: 复制引擎
- `deleteEngine`: 删除引擎

### 系统配置管理
- `readSystemOptions`: 读取系统选项
- `writeSystemOptions`: 写入系统选项

### 打印模式管理
- `readPrintModes`: 读取打印模式
- `writePrintModes`: 写入打印模式

### 数据格式管理
- `readDataFormat`: 读取数据格式
- `writeDataFormat`: 写入数据格式

### CRS 选项管理
- `readCRSOptions`: 读取CRS选项
- `writeCRSOptions`: 写入CRS选项

### CRT 管理
- `readCrtOptions`: 读取CRT选项
- `setCrtRate`: 设置CRT刷新率
- `removeCrt`: 移除CRT
- `addCrt`: 添加CRT
- `modifyCrt`: 修改CRT

### 音频管理
- `readAudioOptions`: 读取音频选项
- `setHostAddress`: 设置主机地址
- `removeAudio`: 移除音频
- `addAudio`: 添加音频
- `modifyAudio`: 修改音频

### 设备管理
- `readDeviceOptions`: 读取设备选项
- `removeDevice`: 移除设备
- `addDevice`: 添加设备
- `modifyDevice`: 修改设备

### 颜色管理
- `readColorOptions`: 读取颜色选项
- `removeColor`: 移除颜色
- `addColor`: 添加颜色
- `modifyColor`: 修改颜色

## 静态属性及事件定义
- `NAME`: 返回应用程序名称
- `ONTEST`: 测试事件
- `ONCHANGED`: 变更事件
- `OnCustomer`: 客户变更事件
- `OnCustomers`: 客户列表变更事件
- `OnVersion`: 版本变更事件
- `OnVersions`: 版本列表变更事件
- `OnEngine`: 引擎变更事件
- `OnEngines`: 引擎列表变更事件
- `OnSystemOptions`: 系统选项变更事件
- `OnPrintModes`: 打印模式变更事件
- `OnDataFormat`: 数据格式变更事件
- `OnCRSOptions`: CRS选项变更事件
- `OnCrtOptions`: CRT选项变更事件
- `OnAudioOptions`: 音频选项变更事件
- `OnDeviceOptions`: 设备选项变更事件
- `OnColorOptions`: 颜色选项变更事件

$PSI9000 psi101 PSI9016
$OVERSAMPLE 1
$TRIGGER INTERNAL
$TRIGDIVISOR 1
$EUCONV 1 PSI
$ZERO-RELAY None
$SIGNAL-LIST
*
PCellF CHANNEL=0
$ENGINE "LEAP1B"   CALIBMODE=2 
*
PCellR CHANNEL=1
$ENGINE "LEAP1B"   CALIBMODE=2 
*
psi101_002 CHANNEL=2 CALIBMODE=2
*
psi101_003 CHANNEL=3 CALIBMODE=2
*
SAP CHANNEL=4
$ENGINE "LEAP1B"   CALIBMODE=2 
*
psi101_005 CHANNEL=5
$ENGINE "CFM563" ALIAS=Pt495  CALIBMODE=2 
$ENGINE "CFM565" ALIAS=Pt495  CALIBMODE=2 
$ENGINE "CFM567" ALIAS=Pt495  CALIBMODE=2 
$ENGINE "LEAP1B" ALIAS=Pt495  CALIBMODE=2 
*
psi101_006 CHANNEL=6
$ENGINE "CFM565" ALIAS=PS13Sig  CALIBMODE=2 
$ENGINE "CFM567" ALIAS=psi101_106  CALIBMODE=2 
$ENGINE "LEAP1B"   CALIBMODE=2 
*
psi101_007 CHANNEL=7
$ENGINE "CFM567" ALIAS=PS13Sig  CALIBMODE=2 
$ENGINE "LEAP1B"   CALIBMODE=2 
*
PStartAir CHANNEL=8
$ENGINE "LEAP1B"   CALIBMODE=2 
*
PStoredAir CHANNEL=9
$ENGINE "LEAP1B"   CALIBMODE=2 
*
psi101_010 CHANNEL=10 CALIBMODE=2
*
psi101_011 CHANNEL=11 CALIBMODE=2
*
psi101_012 CHANNEL=12 CALIBMODE=2
*
psi101_013 CHANNEL=13 CALIBMODE=2
*
psi101_014 CHANNEL=14
$ENGINE "CFM563" ALIAS=PS3  CALIBMODE=2 
$ENGINE "CFM565" ALIAS=PS3  CALIBMODE=2 
$ENGINE "CFM567" ALIAS=PS3  CALIBMODE=2 
$ENGINE "LEAP1B"   CALIBMODE=2 
*
psi101_015 CHANNEL=15 CALIBMODE=2
$ENGINE "V2500" ALIAS=PsTOBI  
*
$END-SIGNAL-LIST
$END-PSI

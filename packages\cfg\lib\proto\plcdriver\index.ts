/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  RangeValue as RangeValueModal,
  PlcCalcLib as PlcCalcLibModal,
  PlcSignal as PlcSignalModal,
  PlcSignals as PlcSignalsModal,
  PlcDevice as PlcDeviceModal,
  PlcCfg as PlcCfgModal,
  PlcDriverCfg as PlcDriverCfgModal
} from '../modal/plcdriver.mode'

export class RangeValue extends MapProto<RangeValue> {
  min = 0.0
  max = 0.0
  unit = ''

  constructor(val?: Partial<RangeValue>) {
    super(RangeValueModal, 'RangeValue')
    val && this.assign(val)
  }
}

export class PlcCalcLib extends MapProto<PlcCalcLib> {
  type = 0
  min = 0.0
  max = 0.0
  data: Array<number> = []

  constructor(val?: Partial<PlcCalcLib>) {
    super(PlcCalcLibModal, 'PlcCalcLib')
    val && this.assign(val)
  }
}

export class PlcSignal extends MapProto<PlcSignal> {
  name = ''
  signal_range = new RangeValue()
  units_range = new RangeValue()
  type = ''
  scale_range = new RangeValue()
  scale_factor = 0.0
  calib_mode = 0
  comment = 0
  calib = 0
  calib_data = new PlcCalcLib()
  comments: Array<string> = []

  constructor(val?: Partial<PlcSignal>) {
    super(PlcSignalModal, 'PlcSignal')
    val && this.assign(val)
  }
}

export class PlcSignals extends MapProto<PlcSignals> {
  input_float = false
  list: Array<PlcSignal> = []

  constructor(val?: Partial<PlcSignals>) {
    super(PlcSignalsModal, 'PlcSignals')
    val && this.assign(val)
  }
}

export class PlcDevice extends MapProto<PlcDevice> {
  type = 0
  addr = 0
  num = 0
  signals = new PlcSignals()

  constructor(val?: Partial<PlcDevice>) {
    super(PlcDeviceModal, 'PlcDevice')
    val && this.assign(val)
  }
}

export class PlcCfg extends MapProto<PlcCfg> {
  device_name = ''
  scan_rate = 0
  devices: Array<PlcDevice> = []

  constructor(val?: Partial<PlcCfg>) {
    super(PlcCfgModal, 'PlcCfg')
    val && this.assign(val)
  }
}

export class PlcDriverCfg extends RootProto<PlcDriverCfg> {
  scan_rate = 0
  plcs: Array<PlcCfg> = []

  constructor(val?: Partial<PlcDriverCfg>) {
    super(PlcDriverCfgModal, PlcDriverCfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return PlcDriverCfgModal.$MAX
  }

  static get minType() {
    return PlcDriverCfgModal.$MIN
  }

  static get uri() {
    return URI(PlcDriverCfgModal.$MAX, PlcDriverCfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [PlcDriverCfgModal.$MAX, PlcDriverCfgModal.$MIN]
  }

  static get key() {
    return 'PlcDriverCfg'
  }
}

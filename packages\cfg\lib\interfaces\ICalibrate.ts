import { BaseItr, BaseMtr, IBase } from './IBase'

export interface VirSignalItem {
  file: string
  /**
   * Group Name
   */
  group_name: string
  /**
   * Type
   */
  type: number
}

export abstract class ICalibrate extends IBase<ICalibrate> {
  /**
   * Calcs Virtual Signal
   */
  abstract readVirSignals(): Promise<Array<VirSignalItem>>
  abstract removeVirSignal(index: number): Promise<boolean>
  abstract addVirSignal(val: VirSignalItem, index?: number): Promise<boolean>
  abstract modifyVirSignal(index: number, val: Partial<VirSignalItem>): Promise<boolean>

  static override get NAME() {
    return 'Calibrate'
  }

  static get OnVirSignals() {
    return 'Calibrate.OnVirSignals'
  }
}

export type CalibrateItr = BaseItr<ICalibrate>
export type CalibrateMtr = BaseMtr<ICalibrate>

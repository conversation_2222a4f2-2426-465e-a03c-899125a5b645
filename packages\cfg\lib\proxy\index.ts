import { Sdk } from '../impl'
import { App } from './App'
import { PageConfig, IPage, ISdk, SdkConfig, IApp, AppItr } from '../interfaces'
import { BrowserWindow } from 'electron'

export { IApp }
export const createSdk = (config: SdkConfig, win: BrowserWindow): ISdk<IApp, AppItr> => {
  const proxy = new App(win, true, config.path || '')
  proxy.init()
  proxy.start()
  return Sdk.createSdk<IApp, AppItr>(config, proxy)
}

export const createPage = (config: PageConfig): IPage<IApp, AppItr> => {
  const proxy = new App()
  return Sdk.createPage<IApp, AppItr>(config, proxy)
}

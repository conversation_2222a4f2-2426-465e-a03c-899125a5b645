/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  QuestionItem as QuestionItemModal,
  Questions as QuestionsModal
} from '../modal/questions.mode'

export class QuestionItem extends MapProto<QuestionItem> {
  type = ''
  id = ''
  prompt = ''
  answer = ''
  prev_param = ''
  operator = ''
  answer_set = ''
  enum = ''
  format = ''

  constructor(val?: Partial<QuestionItem>) {
    super(QuestionItemModal, 'QuestionItem')
    val && this.assign(val)
  }
}

export class Questions extends RootProto<Questions> {
  type = ''
  name = ''
  question_vec: Array<QuestionItem> = []

  constructor(val?: Partial<Questions>) {
    super(QuestionsModal, Questions.key)
    val && this.assign(val)
  }

  static get maxType() {
    return QuestionsModal.$MAX
  }

  static get minType() {
    return QuestionsModal.$MIN
  }

  static get uri() {
    return URI(QuestionsModal.$MAX, QuestionsModal.$MIN)
  }

  static get types(): [number, number] {
    return [QuestionsModal.$MAX, QuestionsModal.$MIN]
  }

  static get key() {
    return 'Questions'
  }
}

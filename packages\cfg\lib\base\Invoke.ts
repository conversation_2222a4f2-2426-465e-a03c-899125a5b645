import { Invoker, IMethod } from './Invoker'
import { Dispatch } from './Dispatch'

export class Invoke extends Dispatch implements Invoker {
  private _name: string
  private _outputs: Map<string, [object, IMethod]>

  constructor(name: string) {
    super()

    this._name = name
    this._outputs = new Map<string, [object, IMethod]>()
  }

  get valid() {
    return false
  }

  override async init(...args: any[]) {
    await super.init()
    this.log('init', 'output: {%1}', [...this._outputs.keys()].join(','))
  }

  override async stop() {
    await super.stop()
    this._outputs.clear()
  }

  override async destroy() {
    this._outputs.clear()
    await super.destroy()

    this.log('destroy')
  }

  get name(): string {
    return this._name
  }

  excute(key: string, ...args: any[]) {
    const [invoker = null, method = null] = this._outputs.get(key) || []
    return method?.apply(invoker, args) || null
  }

  protected setup(name: string, invoker: object, method: IMethod) {
    this._outputs.set(name, [invoker, method])
  }

  protected log(title: string, centent = '', ...args: any[]) {}
}

$DISPLAY "Critical" background=NewGreen editres=934:448 movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$MSGQ
$STOREDISPLAY Append
$OBJECT Digital font=20 startx=40 starty=20 endx=160 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=6 prec=1 label="Inlet\nTemperature" units="Degrees F" 
Digital2 type="FLOAT" width=6 prec=1 label="Gearbox\nVibration" units="In/S"
Digital3 type="FLOAT" width=6 prec=1 label=" \nIGV Position" units="Deg" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=190 starty=20 endx=310 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=6 prec=1 label="Cell\nTemperature" units="Degrees F" 
Digital2 type="FLOAT" width=6 prec=1 label="Turbine\nVibration" units="In/S"
Digital3 type="FLOAT" width=6 prec=1 label="Generator\nVolts" units="Volts" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=340 starty=20 endx=460 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=6 prec=1 label="Bleed Air\nTemperature" units="Degrees F" 
Digital2 type="FLOAT" width=6 prec=1 label="Cooling Fan\nVib" units="In/S"
Digital3 type="FLOAT" width=6 prec=1 label="Exhaust Static\nPressure" units="In H2O" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=490 starty=20 endx=610 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=6 prec=1 label="Bleed Orifice\nTemp" units="Degrees F" 
Digital2 type="FLOAT" width=6 prec=1 label=" \nBarometer" units="PSIA"
Digital3 type="FLOAT" width=6 prec=1 label=" \nFuel Flow" units="Lb/Hr" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=640 starty=20 endx=760 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=6 prec=1 label="Compressor\nDis Temp" units="Degrees F" 
Digital2 type="FLOAT" width=6 prec=1 label="Compressor\nDis Press" units="PSIG"
Digital3 type="FLOAT" width=6 prec=1 label="Fuel Boost\nPressure" units="PSI" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=790 starty=20 endx=910 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=6 prec=1 label="Oil\nTemp" units="Degrees F" 
Digital2 type="FLOAT" width=6 prec=1 label=" Sump\nPressure" units="inH2O"
Digital3 type="FLOAT" width=6 prec=1 label=" \nPWGEN" units="KW" 
$END-OBJECT
***********
$END-DISPLAY


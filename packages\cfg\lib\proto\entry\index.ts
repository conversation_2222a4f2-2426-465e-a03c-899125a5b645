/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Customer as CustomerModal,
  Entry as EntryModal
} from '../modal/entry.mode'

export class Customer extends MapProto<Customer> {
  name = ''
  label = ''
  version = ''

  constructor(val?: Partial<Customer>) {
    super(CustomerModal, 'Customer')
    val && this.assign(val)
  }
}

export class Entry extends RootProto<Entry> {
  current = ''
  customers: Array<Customer> = []

  constructor(val?: Partial<Entry>) {
    super(EntryModal, Entry.key)
    val && this.assign(val)
  }

  static get maxType() {
    return EntryModal.$MAX
  }

  static get minType() {
    return EntryModal.$MIN
  }

  static get uri() {
    return URI(EntryModal.$MAX, EntryModal.$MIN)
  }

  static get types(): [number, number] {
    return [EntryModal.$MAX, EntryModal.$MIN]
  }

  static get key() {
    return 'Entry'
  }
}

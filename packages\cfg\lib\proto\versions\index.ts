/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Version as VersionModal,
  CustVersion as CustVersionModal,
  Versions as VersionsModal
} from '../modal/versions.mode'

export class Version extends MapProto<Version> {
  name = ''
  md5 = ''
  comments = ''

  constructor(val?: Partial<Version>) {
    super(VersionModal, 'Version')
    val && this.assign(val)
  }
}

export class CustVersion extends MapProto<CustVersion> {
  name = ''
  versions: Array<Version> = []

  constructor(val?: Partial<CustVersion>) {
    super(CustVersionModal, 'CustVersion')
    val && this.assign(val)
  }
}

export class Versions extends RootProto<Versions> {
  customers: Array<CustVersion> = []

  constructor(val?: Partial<Versions>) {
    super(VersionsModal, Versions.key)
    val && this.assign(val)
  }

  static get maxType() {
    return VersionsModal.$MAX
  }

  static get minType() {
    return VersionsModal.$MIN
  }

  static get uri() {
    return URI(VersionsModal.$MAX, VersionsModal.$MIN)
  }

  static get types(): [number, number] {
    return [VersionsModal.$MAX, VersionsModal.$MIN]
  }

  static get key() {
    return 'Versions'
  }
}

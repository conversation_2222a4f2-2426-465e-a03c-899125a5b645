
******************************************************
********** CENC-VIB4 CONFIGURATION FILE  *************
******************************************************

******************************************************
**************  SYSTEM CONFIGURATION  ****************
******************************************************

$SYSTEM
   Measurement_Rate = 10  /*  Measurement rate, 0 - 10 Hz  */
*
   Bandwidth = 0         /*  0 = 500 Hz    */
*                         /*  1 = 1000 Hz   */
*                         /*  2 = 2000 Hz   */
*                         /*  3 = 5000 Hz   */
*                         /*  4 = 10000 Hz  */
*
   FFTLines = 2          /*  0 = 200 lines   */
*                         /*  1 = 400 lines   */
*                         /*  2 = 800 lines   */
*                         /*  3 = 1600 lines  */
$END-SYSTEM

******************************************************
*************  VIBRATION INPUT SETUP  ****************
******************************************************

$VibInput 1
   Sensitivity = 50.0     /*  Transducer sensitivity, 1.0 to 2000.0  */
   Gain = 0               /*  Gain of amplifier, 0, 1, or 10  */
*                         /*  0 = Vib. System will select gain  */
*                         /*  1 = Gain of 1  */
*                         /*  10 = Gain of 10  */
   Input_Range = 25.0     /*  Expected input range, 0.1 to 150.0 units  */
   Transducer_Type = 2    /*  Transducer type, 0 = Disabled  */
*                         /*                   1 = Single Ended Accel  */
*                         /*                   2 = Differential Accel  */
*                         /*                   3 = Velocity  */
*                         /*                   4 = ICP Accel  */
   AD_Gain = 0            /*  A/D gain, 0 = Auto Select  */
*                         /*            1 = 10.00 volts  */
*                         /*            2 = 5.00 volts  */
*                         /*            4 = 2.50 volts  */
*                         /*            8 = 1.25 volts  */
$END-VIBINPUT

$VibInput 2
   Sensitivity = 50.0     /*  Transducer sensitivity, 1.0 to 2000.0  */
   Gain = 0               /*  Gain of amplifier, 0, 1, or 10  */
*                         /*  0 = Vib. System will select gain  */
*                         /*  1 = Gain of 1  */
*                         /*  10 = Gain of 10  */
   Input_Range = 25.0     /*  Expected input range, 0.1 to 150.0 units  */
   Transducer_Type = 2    /*  Transducer type, 0 = Disabled  */
*                         /*                   1 = Single Ended Accel  */
*                         /*                   2 = Differential Accel  */
*                         /*                   3 = Velocity  */
*                         /*                   4 = ICP Accel  */
   AD_Gain = 0            /*  A/D gain, 0 = Auto Select  */
*                         /*            1 = 10.00 volts  */
*                         /*            2 = 5.00 volts  */
*                         /*            4 = 2.50 volts  */
*                         /*            8 = 1.25 volts  */
$END-VIBINPUT

$VibInput 3
   Sensitivity = 50.0     /*  Transducer sensitivity, 1.0 to 2000.0  */
   Gain = 0               /*  Gain of amplifier, 0, 1, or 10  */
*                         /*  0 = Vib. System will select gain  */
*                         /*  1 = Gain of 1  */
*                         /*  10 = Gain of 10  */
   Input_Range = 25.0     /*  Expected input range, 0.1 to 150.0 units  */
   Transducer_Type = 2    /*  Transducer type, 0 = Disabled  */
*                         /*                   1 = Single Ended Accel  */
*                         /*                   2 = Differential Accel  */
*                         /*                   3 = Velocity  */
*                         /*                   4 = ICP Accel  */
   AD_Gain = 0            /*  A/D gain, 0 = Auto Select  */
*                         /*            1 = 10.00 volts  */
*                         /*            2 = 5.00 volts  */
*                         /*            4 = 2.50 volts  */
*                         /*            8 = 1.25 volts  */
$END-VIBINPUT

$VibInput 4
   Sensitivity = 50.0     /*  Transducer sensitivity, 1.0 to 2000.0  */
   Gain = 0               /*  Gain of amplifier, 0, 1, or 10  */
*                         /*  0 = Vib. System will select gain  */
*                         /*  1 = Gain of 1  */
*                         /*  10 = Gain of 10  */
   Input_Range = 25.0     /*  Expected input range, 0.1 to 150.0 units  */
   Transducer_Type = 0    /*  Transducer type, 0 = Disabled  */
*                         /*                   1 = Single Ended Accel  */
*                         /*                   2 = Differential Accel  */
*                         /*                   3 = Velocity  */
*                         /*                   4 = ICP Accel  */
   AD_Gain = 0            /*  A/D gain, 0 = Auto Select  */
*                         /*            1 = 10.00 volts  */
*                         /*            2 = 5.00 volts  */
*                         /*            4 = 2.50 volts  */
*                         /*            8 = 1.25 volts  */
$END-VIBINPUT

******************************************************
*************  BROAD BAND FILTER SETUP  **************
******************************************************

$BBFILTER 1
   Vib_Channel = 1           /*  1 to 4, Selects input channel to filter  */ 
   Output_Units = 2          /*  0 to 3, 0 = Disables Filter */
*                            /*          1 = Acceleration (g's) Output  */
*                            /*          2 = Velocity (IPS) Output  */
*                            /*          3 = Displacement (Mils) Output  */
*                            /*          4 = Acceleration (m/sec2) Output  */
*                            /*          5 = Velocity (mm/sec) Output  */
*                            /*          6 = Displacement (um) Output  */
   Detector_Type = 0         /*  0 to 3, 0 = Peak Detector  */
*                            /*          1 = Peak to Peak Detector  */
*                            /*          2 = RMS Detector  */
*                            /*          3 = Average Detector  */
   Time_Constant = 4         /*  0 to 6, 0 = No Smoothing  */
*                            /*          1 = 0.25 Sec Smoothing  */
*                            /*          2 = 0.50 Sec Smoothing  */
*                            /*          3 = 0.75 Sec Smoothing  */
*                            /*          4 = 1.00 Sec Smoothing  */
*                            /*          5 = 1.25 Sec Smoothing  */
*                            /*          6 = 1.50 Sec Smoothing  */
   Filter_Shape = 0          /*  0 to 3, 0 = Rectangular  */
*                            /*          1 = 7 Pole Chebyshev  */
*                            /*          2 = 6 Pole Butterworth  */
*                            /*          3 = User Defined  */
   Filter_Type = 3           /*  0 to 3, 0 = No Filtering  */
*                            /*          1 = Low Pass Filter  */
*                            /*          2 = High Pass Filter  */
*                            /*          3 = Band Pass Filter  */
   Upper_Cutoff = 400         /*  2 to 10000, Upper Cutoff of filter  */
   Lower_Cutoff = 27         /*  1 to 999, Lower Cutoff of filter  */
$END-BBFILTER

$BBFILTER 2
   Vib_Channel = 2           /*  1 to 4, Selects input channel to filter  */ 
   Output_Units = 2          /*  0 to 3, 0 = Disables Filter */
*                            /*          1 = Acceleration (g's) Output  */
*                            /*          2 = Velocity (IPS) Output  */
*                            /*          3 = Displacement (Mils) Output  */
*                            /*          4 = Acceleration (m/sec2) Output  */
*                            /*          5 = Velocity (mm/sec) Output  */
*                            /*          6 = Displacement (um) Output  */
   Detector_Type = 0         /*  0 to 3, 0 = Peak Detector  */
*                            /*          1 = Peak to Peak Detector  */
*                            /*          2 = RMS Detector  */
*                            /*          3 = Average Detector  */
   Time_Constant = 4         /*  0 to 6, 0 = No Smoothing  */
*                            /*          1 = 0.25 Sec Smoothing  */
*                            /*          2 = 0.50 Sec Smoothing  */
*                            /*          3 = 0.75 Sec Smoothing  */
*                            /*          4 = 1.00 Sec Smoothing  */
*                            /*          5 = 1.25 Sec Smoothing  */
*                            /*          6 = 1.50 Sec Smoothing  */
   Filter_Shape = 0          /*  0 to 3, 0 = Rectangular  */
*                            /*          1 = 7 Pole Chebyshev  */
*                            /*          2 = 6 Pole Butterworth  */
*                            /*          3 = User Defined  */
   Filter_Type = 3           /*  0 to 3, 0 = No Filtering  */
*                            /*          1 = Low Pass Filter  */
*                            /*          2 = High Pass Filter  */
*                            /*          3 = Band Pass Filter  */
   Upper_Cutoff = 400         /*  2 to 10000, Upper Cutoff of filter  */
   Lower_Cutoff = 27         /*  1 to 999, Lower Cutoff of filter  */
$END-BBFILTER

$BBFILTER 3
   Vib_Channel = 3           /*  1 to 4, Selects input channel to filter  */ 
   Output_Units = 2          /*  0 to 3, 0 = Disables Filter */
*                            /*          1 = Acceleration (g's) Output  */
*                            /*          2 = Velocity (IPS) Output  */
*                            /*          3 = Displacement (Mils) Output  */
*                            /*          4 = Acceleration (m/sec2) Output  */
*                            /*          5 = Velocity (mm/sec) Output  */
*                            /*          6 = Displacement (um) Output  */
   Detector_Type = 0         /*  0 to 3, 0 = Peak Detector  */
*                            /*          1 = Peak to Peak Detector  */
*                            /*          2 = RMS Detector  */
*                            /*          3 = Average Detector  */
   Time_Constant = 4         /*  0 to 6, 0 = No Smoothing  */
*                            /*          1 = 0.25 Sec Smoothing  */
*                            /*          2 = 0.50 Sec Smoothing  */
*                            /*          3 = 0.75 Sec Smoothing  */
*                            /*          4 = 1.00 Sec Smoothing  */
*                            /*          5 = 1.25 Sec Smoothing  */
*                            /*          6 = 1.50 Sec Smoothing  */
   Filter_Shape = 0          /*  0 to 3, 0 = Rectangular  */
*                            /*          1 = 7 Pole Chebyshev  */
*                            /*          2 = 6 Pole Butterworth  */
*                            /*          3 = User Defined  */
   Filter_Type = 3           /*  0 to 3, 0 = No Filtering  */
*                            /*          1 = Low Pass Filter  */
*                            /*          2 = High Pass Filter  */
*                            /*          3 = Band Pass Filter  */
   Upper_Cutoff = 400         /*  2 to 10000, Upper Cutoff of filter  */
   Lower_Cutoff = 27         /*  1 to 999, Lower Cutoff of filter  */
$END-BBFILTER

$BBFILTER 4
   Vib_Channel = 4           /*  1 to 4, Selects input channel to filter  */ 
   Output_Units = 2          /*  0 to 3, 0 = Disables Filter */
*                            /*          1 = Acceleration (g's) Output  */
*                            /*          2 = Velocity (IPS) Output  */
*                            /*          3 = Displacement (Mils) Output  */
*                            /*          4 = Acceleration (m/sec2) Output  */
*                            /*          5 = Velocity (mm/sec) Output  */
*                            /*          6 = Displacement (um) Output  */
   Detector_Type = 0         /*  0 to 3, 0 = Peak Detector  */
*                            /*          1 = Peak to Peak Detector  */
*                            /*          2 = RMS Detector  */
*                            /*          3 = Average Detector  */
   Time_Constant = 4         /*  0 to 6, 0 = No Smoothing  */
*                            /*          1 = 0.25 Sec Smoothing  */
*                            /*          2 = 0.50 Sec Smoothing  */
*                            /*          3 = 0.75 Sec Smoothing  */
*                            /*          4 = 1.00 Sec Smoothing  */
*                            /*          5 = 1.25 Sec Smoothing  */
*                            /*          6 = 1.50 Sec Smoothing  */
   Filter_Shape = 0          /*  0 to 3, 0 = Rectangular  */
*                            /*          1 = 7 Pole Chebyshev  */
*                            /*          2 = 6 Pole Butterworth  */
*                            /*          3 = User Defined  */
   Filter_Type = 3           /*  0 to 3, 0 = No Filtering  */
*                            /*          1 = Low Pass Filter  */
*                            /*          2 = High Pass Filter  */
*                            /*          3 = Band Pass Filter  */
   Upper_Cutoff = 400         /*  2 to 10000, Upper Cutoff of filter  */
   Lower_Cutoff = 27         /*  1 to 999, Lower Cutoff of filter  */
$END-BBFILTER

******************************************************
*************  TACHOMETER INPUT SETUP  ***************
******************************************************

$TACHINPUT 1
   Rotor_Speed = 5650.0      /*  0 to 100,000, 100% Rotor Speed in RPM  */
*                            /*                0 disables tach processing  */
   Tach_Freq = 5650.0        /*  1 to 100,000, 100% Tach. Frequency in Hz  */
*
   Sensitivity = 0.500       /*  Low amplitude shutdown (Vp-p), 0.0 to 1.1 */
$END-TACHINPUT

$TACHINPUT 2
   Rotor_Speed = 14950.0      /*  0 to 100,000, 100% Rotor Speed in RPM  */
*                            /*                0 disables tach processing  */
   Tach_Freq = 3552.0        /*  1 to 100,000, 100% Tach. Frequency in Hz  */
*
   Sensitivity = 1.000       /*  Low amplitude shutdown (Vp-p), 0.0 to 1.1 */
$END-TACHINPUT

$TACHINPUT 3
   Rotor_Speed = 5650.0      /*  0 to 100,000, 100% Rotor Speed in RPM  */
*                            /*                0 disables tach processing  */
   Tach_Freq = 5650.0        /*  1 to 100,000, 100% Tach. Frequency in Hz  */
*
   Sensitivity = 0.500       /*  Low amplitude shutdown (Vp-p), 0.0 to 1.1 */
$END-TACHINPUT

$TACHINPUT 4
   Tach_Processing = 3       /*  0 to 3, Selects 1/Rev tach. processing  */
*                            /*          0 = Disabled  */
*                            /*          1 = Encode High Pulse  */
*                            /*          2 = Encode Low Pulse  */
*                            /*          3 = Single Pulse or Strobe  */
*
   Sensitivity = 0.500       /*  Low amplitude shutdown (Vp-p), 0.0 to 1.1 */
$END-TACHINPUT

******************************************************
***********  TRACKING FILTER INPUT SETUP  ************
******************************************************

$TFINPUT 1
   Vib_Channel = 1              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 1             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 2
   Vib_Channel = 1              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 2             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 3
   Vib_Channel = 1              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 3             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 0             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 4
   Vib_Channel = 1              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 4             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 5
   Vib_Channel = 2              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 1             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 6
   Vib_Channel = 2              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 2             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 7
   Vib_Channel = 2              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 3             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 0             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 8
   Vib_Channel = 2              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 4             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 9
   Vib_Channel = 3              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 1             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 10
   Vib_Channel = 3              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 2             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 11
   Vib_Channel = 3              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 3             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 0             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 0            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 12
   Vib_Channel = 3              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 4             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 13
   Vib_Channel = 4              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 1             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 14
   Vib_Channel = 4              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 2             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 15
   Vib_Channel = 4              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 3             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 0             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 1            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 0            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

$TFINPUT 16
   Vib_Channel = 4              /*  1 to 4, Selects Input channel to filter  */
   Tach_Channel = 4             /*  1 to 4, Selects Tach. input to track  */
   Output_Units = 2             /*  0 to 3, 0 = Disables Filter  */
*                               /*          1 = Acceleration (g's) Output  */
*                               /*          2 = Velocity (IPS) Output  */
*                               /*          3 = Displacement (Mils) Output  */
*                               /*          4 = Acceleration (m/sec2) Output  */
*                               /*          5 = Velocity (mm/sec) Output  */
*                               /*          6 = Displacement (um) Output  */
   Detector_Type = 0            /*  0 to 3, 0 = Peak Detector  */
*                               /*          1 = Peak to Peak Detector  */
*                               /*          2 = RMS Detector  */
*                               /*          3 = Average Detector  */
   Full_Scale_Units = 15.0      /*  0.1 to 150.0, Full Scale Output  */
*                               /*                in detector units  */
   Full_Scale_Volts = 10.0      /*  1.0 to 10.0, Full Scale Output  */
*                               /*                in Volts  */
   Filter_Specification = 1     /*  0 to 1, 0 = Constant Bandwidth  */
*                               /*          1 = Constant Q  */
   Filter_Q = 30                /*  10, 20, or 30, 10 = Q of 10  */
*                               /*                 20 = Q of 20  */
*                               /*                 30 = Q of 20  */
   Band_Width = 11              /*  5 or 11, 5 = 5 Hz Bandwidth  */
*                               /*           11 = 11 Hz Bandwidth  */
   Time_Constant = 4            /* 0 to 6, 0 = No Smoothing  */
*                               /*         1 = 0.25 Sec Smoothing  */
*                               /*         2 = 0.50 Sec Smoothing  */
*                               /*         3 = 0.75 Sec Smoothing  */
*                               /*         4 = 1.00 Sec Smoothing  */
*                               /*         5 = 1.25 Sec Smoothing  */
*                               /*         6 = 1.50 Sec Smoothing  */
   Order_Tracking = 1           /*  0.1 to 2000.0, Order of tach   */
*                               /*         signal to track against */
$END-TFINPUT

******************************************************
***********  SPECTRUM ANALYSIS SETUP  ****************
******************************************************

$SPECTRUM 1
   Output_Units = 2           /*  0 to 3, 0 = Disables Filter  */
*                              /*          1 = Acceleration (g's) Output  */
*                              /*          2 = Velocity (IPS) Output  */
*                              /*          3 = Displacement (Mils) Output  */
*                              /*          4 = Acceleration (m/sec2) Output  */
*                              /*          5 = Velocity (mm/sec) Output  */
*                              /*          6 = Displacement (um) Output  */
   Start_Frequency = 5         /* 0 to 9999, Spectrum range starting freq, must be less than End_Frequency.  */  
   End_Frequency = 500         /*  1 to 10000, Spectrum range ending freq, cannot exceed system bandwidth */
   Detector_Type = 0           /*  0 to 3, 0 = Peak Detector  */
*                              /*          1 = Peak to Peak Detector  */
*                              /*          2 = RMS Detector  */
*                              /*          3 = Average Detector  */
   Full_Scale_Units = 3.0     /*  0.1 to 150.0, Full Scale Output  */
*                              /*                in detector units  */
$END-SPECTRUM
$SPECTRUM 2
   Output_Units = 2           /*  0 to 3, 0 = Disables Filter  */
*                              /*          1 = Acceleration (g's) Output  */
*                              /*          2 = Velocity (IPS) Output  */
*                              /*          3 = Displacement (Mils) Output  */
*                              /*          4 = Acceleration (m/sec2) Output  */
*                              /*          5 = Velocity (mm/sec) Output  */
*                              /*          6 = Displacement (um) Output  */
   Start_Frequency = 5         /* 0 to 9999, Spectrum range starting freq, must be less than End_Frequency.  */  
   End_Frequency = 500         /*  1 to 10000, Spectrum range ending freq, cannot exceed system bandwidth */
   Detector_Type = 0           /*  0 to 3, 0 = Peak Detector  */
*                              /*          1 = Peak to Peak Detector  */
*                              /*          2 = RMS Detector  */
*                              /*          3 = Average Detector  */
   Full_Scale_Units = 3.0     /*  0.1 to 150.0, Full Scale Output  */
*                              /*                in detector units  */
$END-SPECTRUM
$SPECTRUM 3
   Output_Units = 2           /*  0 to 3, 0 = Disables Filter  */
*                              /*          1 = Acceleration (g's) Output  */
*                              /*          2 = Velocity (IPS) Output  */
*                              /*          3 = Displacement (Mils) Output  */
*                              /*          4 = Acceleration (m/sec2) Output  */
*                              /*          5 = Velocity (mm/sec) Output  */
*                              /*          6 = Displacement (um) Output  */
   Start_Frequency = 0         /* 0 to 9999, Spectrum range starting freq, must be less than End_Frequency.  */  
   End_Frequency = 500         /*  1 to 10000, Spectrum range ending freq, cannot exceed system bandwidth */
   Detector_Type = 0           /*  0 to 3, 0 = Peak Detector  */
*                              /*          1 = Peak to Peak Detector  */
*                              /*          2 = RMS Detector  */
*                              /*          3 = Average Detector  */
   Full_Scale_Units = 3.0     /*  0.1 to 150.0, Full Scale Output  */
*                              /*                in detector units  */
$END-SPECTRUM
$SPECTRUM 4
   Output_Units = 2           /*  0 to 3, 0 = Disables Filter  */
*                              /*          1 = Acceleration (g's) Output  */
*                              /*          2 = Velocity (IPS) Output  */
*                              /*          3 = Displacement (Mils) Output  */
*                              /*          4 = Acceleration (m/sec2) Output  */
*                              /*          5 = Velocity (mm/sec) Output  */
*                              /*          6 = Displacement (um) Output  */
   Start_Frequency = 0         /* 0 to 9999, Spectrum range starting freq, must be less than End_Frequency.  */  
   End_Frequency = 500         /*  1 to 10000, Spectrum range ending freq, cannot exceed system bandwidth */
   Detector_Type = 0           /*  0 to 3, 0 = Peak Detector  */
*                              /*          1 = Peak to Peak Detector  */
*                              /*          2 = RMS Detector  */
*                              /*          3 = Average Detector  */
   Full_Scale_Units = 3.0     /*  0.1 to 150.0, Full Scale Output  */
*                              /*                in detector units  */
$END-SPECTRUM


******************************************************
***********  END OF VIBRATION SETUP FILE  ************
******************************************************

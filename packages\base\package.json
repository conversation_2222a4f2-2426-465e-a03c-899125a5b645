{"name": "@wuk/base", "version": "0.0.7", "description": "base", "author": "lovisliao", "repository": {"type": "git", "url": "http://git.100vs.com/webs/libs/wksdk"}, "keywords": ["base"], "license": "MIT", "main": "./dist/index.js", "types": "./dist/index.d.ts", "directories": {"lib": "dist", "doc": "doc"}, "files": ["dist", "doc"], "engines": {"node": ">=6"}, "maintainers": ["lovisliao"], "publishConfig": {"access": "public", "registry": "https://npm-registry.100vs.com"}, "scripts": {"dev": "yarn clean && ttsc -w", "build": "yarn clean && ttsc", "clean": "rimraf ./dist", "declare": "tsc -p tsconfig.d.json", "pub": "yarn build && npm publish --registry=https://npm-registry.100vs.com", "build:gulp": "gulp --gulpfile build/gulp.js"}, "dependencies": {"crypto": "^1.0.1", "yauzl": "^3.0.0", "yazl": "^2.4.3"}, "devDependencies": {"@types/node": "^16.11.9", "@types/yauzl": "^2.10.0", "@types/yazl": "^2.4.2", "rimraf": "^3.0.2", "ttypescript": "^1.5.13", "typescript-transform-paths": "^3.3.1"}}
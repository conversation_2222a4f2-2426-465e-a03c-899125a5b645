import { BrowserWindow, <PERSON>u as App<PERSON>enu, MenuItemConstructorOptions } from 'electron'
import { ActionKey, IMenu, AppMenuItem } from '../interfaces'
import { BaseProxy, method } from './Base'

type BizMenuItem = AppMenuItem & MenuItemConstructorOptions

const kAppMenu: BizMenuItem = {
  // label: 'System',
  // submenu: [
  //   { key: kBizMenuKey.Create_New_Engine, label: 'Create New Engine' },
  //   { type: 'separator' },
  //   { label: 'System Setup' },
  //   { label: 'Change Engine Icon' },
  //   { label: 'Save System Setup' },
  //   { type: 'separator' },
  //   { role: 'quit', label: 'Exit System' }
  // ]
}
// const kEngineMenu: BizMenuItem = {
//   label: 'Engine',
//   submenu: [
//     { key: kBizMenuKey.Save_Engine, label: 'Save' },
//     { key: kBizMenuKey.SaveAs_Engine, label: 'Save As' },
//     { type: 'separator' },
//     { key: kBizMenuKey.Copy_Engine, label: 'Copy' },
//     { key: kBizMenuKey.Delete_Engine, label: 'Delete' }
//   ]
// }

export class Menu extends BaseProxy<IMenu> implements IMenu {
  constructor(win?: BrowserWindow, master = false) {
    super(IMenu.NAME, master, win)
    this.handleMenuClicked = this.handleMenuClicked.bind(this)
  }

  override async init() {
    this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async showRightMenu(actionKey: number, items: AppMenuItem[], x: number, y: number) {
    this.popupMenu(actionKey, items, x, y)
    return true
  }

  private initAppMenu() {
    // const items = this.readyItems(this._items)
    // const menu = AppMenu.buildFromTemplate(items)
    // AppMenu.setApplicationMenu(menu)
  }

  private readyItems(items: BizMenuItem[], actionKey: ActionKey = '') {
    items.forEach(item => {
      const submenu = item.submenu
      if (submenu instanceof Array) {
        this.readyItems(submenu, actionKey)
      }

      const key = item.key || ''
      key &&
        (item.click = () => {
          this.handleMenuClicked(actionKey, key)
        })
    })
    return items
  }

  private popupMenu(actionKey: ActionKey, items: AppMenuItem[], x: number, y: number) {
    const template = this.readyItems(items, actionKey)
    const menu = AppMenu.buildFromTemplate(template)

    menu.popup({ window: this.win_, x, y })
  }

  private handleMenuClicked(actonKey: ActionKey, key: ActionKey) {
    this.emit(IMenu.MenuClicked, actonKey, key)
  }

  private async loadOptions(force?: boolean) {
    this.initAppMenu()
  }

  private async clearOptions() {}
}

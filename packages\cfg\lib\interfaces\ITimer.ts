import { BaseItr, BaseMtr, IBase } from './IBase'
import { TimerType } from './type'

export interface TimerItem {
  /**
   * Name
   */
  name: string
  /**
   * Equation
   */
  equation: string
  /**
   * Type
   */
  type: TimerType
}

export interface TimersOptions {
  list: TimerItem[]
}

export abstract class ITimer extends IBase<ITimer> {
  abstract readOptions(): Promise<TimersOptions>
  abstract removeTimer(index: number): Promise<boolean>
  abstract addTimer(val: TimerItem, index?: number): Promise<boolean>
  abstract modifyTimer(index: number, val: Partial<TimerItem>): Promise<boolean>

  static override get NAME() {
    return 'Timer'
  }

  static get OnOptions() {
    return 'Timer.OnOptions'
  }
}

export type TimerItr = BaseItr<ITimer>
export type TimerMtr = BaseMtr<ITimer>

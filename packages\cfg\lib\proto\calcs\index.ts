/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  IncludeFile as IncludeFileModal,
  Calcs as CalcsModal
} from '../modal/calcs.mode'

export class IncludeFile extends MapProto<IncludeFile> {
  name = ''
  uri = ''
  depends: Array<string> = []

  constructor(val?: Partial<IncludeFile>) {
    super(IncludeFileModal, 'IncludeFile')
    val && this.assign(val)
  }
}

export class Calcs extends RootProto<Calcs> {
  includes: Array<IncludeFile> = []

  constructor(val?: Partial<Calcs>) {
    super(CalcsModal, Calcs.key)
    val && this.assign(val)
  }

  static get maxType() {
    return CalcsModal.$MAX
  }

  static get minType() {
    return CalcsModal.$MIN
  }

  static get uri() {
    return URI(CalcsModal.$MAX, CalcsModal.$MIN)
  }

  static get types(): [number, number] {
    return [CalcsModal.$MAX, CalcsModal.$MIN]
  }

  static get key() {
    return 'Calcs'
  }
}

/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Lineable as LineableModal,
  Groupable as GroupableModal,
  Calc as CalcModal
} from '../modal/calc.mode'

export class Lineable extends MapProto<Lineable> {
  comments: Array<string> = []
  type = 0
  name = ''
  unit = ''
  value = ''

  constructor(val?: Partial<Lineable>) {
    super(LineableModal, 'Lineable')
    val && this.assign(val)
  }
}

export class Groupable extends MapProto<Groupable> {
  comments: Array<string> = []
  name = ''
  excute = 0
  lines: Array<Lineable> = []

  constructor(val?: Partial<Groupable>) {
    super(GroupableModal, 'Groupable')
    val && this.assign(val)
  }
}

export class Calc extends RootProto<Calc> {
  group = new Groupable()

  constructor(val?: Partial<Calc>) {
    super(CalcModal, Calc.key)
    val && this.assign(val)
  }

  static get maxType() {
    return CalcModal.$MAX
  }

  static get minType() {
    return CalcModal.$MIN
  }

  static get uri() {
    return URI(CalcModal.$MAX, CalcModal.$MIN)
  }

  static get types(): [number, number] {
    return [CalcModal.$MAX, CalcModal.$MIN]
  }

  static get key() {
    return 'Calc'
  }
}

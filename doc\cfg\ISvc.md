# ISvc 接口文档
## 相关文档
- [类型定义](type.md) - 包含 `SvcConfig` 等类型定义
- [IBase](IBase.md) - 基础接口
- [ISdk](ISdk.md) - SDK 基础接口
- [ICfg](ICfg.md) - 配置接口
- [IWin](IWin.md) - 窗口接口
- [IMenu](IMenu.md) - 菜单接口
- [IPage](IPage.md) - 页面接口
- [IApp](IApp.md) - 应用程序接口
- [IEngine](IEngine.md) - 引擎接口
- [IDisplay](IDisplay.md) - 显示接口

## 概述
`ISvc` 是一个抽象类，继承自 `IBase<ISvc>`，用于定义服务管理的基本功能。它提供了服务的添加、删除、获取和事件处理等功能。

## 类型定义

### 传输类型
```typescript
export enum [TranType](type.md#传输类型) {
  WKP = 0,  // WKP 传输类型
  PB        // Protocol Buffers 传输类型
}
```

### 打包器
```typescript
export abstract class [Packer](type.md#打包器) {
  abstract get valid(): boolean
  abstract marshal: <T extends BaseProto>(
    req: T,
    option?: [SendOption](type.md#发送选项)
  ) => [Uint8Array | null, string | number]
  abstract unmarshal: <T extends BaseProto>(data: Uint8Array) => [ResultProps](type.md#结果属性)<T>[] | null
}
```

### 传输配置
```typescript
export interface [TranConfig](type.md#传输配置) {
  svcId: string      // 服务ID
  type: [TranType](type.md#传输类型)     // 传输类型
  packer?: [Packer](type.md#打包器)    // 打包器
}
```

### 结果属性
```typescript
export interface [ResultProps](type.md#结果属性)<T extends BaseProto> {
  uri?: string           // 资源标识符
  rsp?: T               // 响应数据
  traceId?: string | number  // 追踪ID
}
```

### 服务配置
```typescript
export interface [SvcConfig](type.md#服务配置)<T extends BaseProto = BaseProto> extends BaseConfig {
  uris: Record<string, T>  // URI映射
  ip: string              // IP地址
  port: number            // 端口号
}
```

### 发送选项
```typescript
export interface [SendOption](type.md#发送选项) {
  extend?: Record<string, any>  // 扩展选项
  moduleId?: number             // 模块ID
}
```

## 接口定义

### ITran 抽象类
```typescript
export abstract class ITran extends IBase<ITran> {
  abstract get valid(): boolean
  abstract send: <T extends BaseProto>(req: T, option?: SendOption) => string | number
  abstract onUri: <T extends BaseProto>(cls: ProtoClass<T>, handler: (rsp: T) => void) => void
  abstract offUri: <T extends BaseProto>(cls: ProtoClass<T>, handler: (rsp: T) => void) => void

  static override get NAME() { return 'Tran' }
  static get ONREADY(): string { return 'Tran.ONREADY' }
  static get ONCLOSE(): string { return 'Tran.ONCLOSE' }
  static get ONSTATUS(): string { return 'Tran.ONSTATUS' }
}
```

### ISvc 抽象类
```typescript
export abstract class ISvc extends IBase<ISvc> {
  abstract get main(): ITran
  abstract reset: () => void
  abstract fetch: (svcId: string) => ITran
  abstract onUri: <T extends BaseProto>(
    svcId: string,
    cls: ProtoClass<T>,
    handler: (rsp: T) => void
  ) => void
  abstract offUri: <T extends BaseProto>(
    svcId: string,
    cls: ProtoClass<T>,
    handler: (rsp: T) => void
  ) => void
  abstract add: (svcId: string, type: TranType) => boolean
  abstract remove: (svcId: string) => boolean

  static override get NAME() { return 'Svc' }
}
```

## 方法说明

### ITran 方法
- `valid`: 获取传输器是否有效
- `send`: 发送请求
- `onUri`: 注册URI事件处理器
- `offUri`: 移除URI事件处理器

### ISvc 方法
- `main`: 获取主传输器
- `reset`: 重置服务
- `fetch`: 获取指定服务ID的传输器
- `onUri`: 注册服务URI事件处理器
- `offUri`: 移除服务URI事件处理器
- `add`: 添加服务
- `remove`: 移除服务

## 类型定义
```typescript
export type SvcItr = BaseItr<ISvc>
export type SvcMtr = BaseMtr<ISvc>
```

## 使用示例
```typescript
class MySvc extends ISvc {
  get main(): ITran {
    // 实现主传输器获取逻辑
    return new MyTran();
  }

  reset(): void {
    // 实现重置逻辑
  }

  fetch(svcId: string): ITran {
    // 实现传输器获取逻辑
    return new MyTran();
  }

  // ... 实现其他抽象方法
}
```

## 注意事项
1. 服务管理支持多种传输类型（WKP和PB）
2. 所有服务操作都是异步的
3. 事件处理支持URI级别的注册和移除
4. 服务配置包含IP和端口信息
5. 支持自定义打包器
6. 所有抽象方法都需要在具体实现类中实现
7. 传输器状态通过事件通知机制管理 
/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  MapProto
} from '@wuk/wkp'
import {
  CfgReadTextRsp as CfgReadTextRspModal,
  CfgVerifyRsp as CfgVerifyRspModal
} from '../modal/base.mode'

export class CfgReadTextRsp extends MapProto<CfgReadTextRsp> {
  data = ''

  constructor(val?: Partial<CfgReadTextRsp>) {
    super(CfgReadTextRspModal, 'CfgReadTextRsp')
    val && this.assign(val)
  }
}

export class CfgVerifyRsp extends MapProto<CfgVerifyRsp> {
  code = 0

  constructor(val?: Partial<CfgVerifyRsp>) {
    super(CfgVerifyRspModal, 'CfgVerifyRsp')
    val && this.assign(val)
  }
}

/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  IncludeFile as IncludeFileModal,
  CalcInit as CalcInitModal
} from '../modal/calcinit.mode'

export class IncludeFile extends MapProto<IncludeFile> {
  file = ''
  uri = ''
  depends: Array<string> = []

  constructor(val?: Partial<IncludeFile>) {
    super(IncludeFileModal, 'IncludeFile')
    val && this.assign(val)
  }
}

export class CalcInit extends RootProto<CalcInit> {
  includes: Array<IncludeFile> = []

  constructor(val?: Partial<CalcInit>) {
    super(CalcInitModal, CalcInit.key)
    val && this.assign(val)
  }

  static get maxType() {
    return CalcInitModal.$MAX
  }

  static get minType() {
    return CalcInitModal.$MIN
  }

  static get uri() {
    return URI(CalcInitModal.$MAX, CalcInitModal.$MIN)
  }

  static get types(): [number, number] {
    return [CalcInitModal.$MAX, CalcInitModal.$MIN]
  }

  static get key() {
    return 'CalcInit'
  }
}

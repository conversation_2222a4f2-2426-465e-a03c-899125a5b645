# ICfg 接口文档

## 相关文档
- [类型定义](type.md) - 包含 `CfgConfig` 等类型定义
- [IBase](IBase.md) - 基础接口
- [ISdk](ISdk.md) - SDK 基础接口
- [ISvc](ISvc.md) - 服务接口
- [IWin](IWin.md) - 窗口接口
- [IMenu](IMenu.md) - 菜单接口
- [IPage](IPage.md) - 页面接口
- [IApp](IApp.md) - 应用程序接口
- [IEngine](IEngine.md) - 引擎接口
- [IDisplay](IDisplay.md) - 显示接口

## 概述
`ICfg` 是一个抽象类，继承自 `IBase<ICfg>`，用于定义配置管理的基本功能。它提供了配置的读取、写入、删除等操作，以及显示相关的管理功能。

## 错误码定义
```typescript
export enum [ErrorCode](type.md#错误码定义) {
  OK = 0,           // 成功
  ParamErr = -1,    // 参数错误（暂未支持）
  UnSupported = -2, // 不支持（暂未支持）
  FileLoadErr = -3, // 文件加载错误
  FileReadErr = -4, // 文件读取错误
  FileWriteErr = -5 // 文件写入错误
}
```

## 事件接口
```typescript
export interface [CfgEvent](type.md#事件接口)<T = any> {
  uri: string;           // 资源标识符
  code?: [ErrorCode](type.md#错误码定义);      // 错误码
  message?: string;      // 错误信息
  readonly data?: T;     // 事件数据
}
```

## 接口定义
### ICfg 抽象类
```typescript
export abstract class ICfg extends IBase<ICfg> {
  abstract read<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    engineName?: string,
    force?: boolean
  ): Promise<T | undefined>
  abstract write<T extends BaseProto<T>>(cls: ProtoClass<T>, engineName?: string): Promise<boolean>
  abstract remove<T extends BaseProto<T>>(cls: ProtoClass<T>): Promise<void>
  abstract assign<T extends BaseProto<T>>(cls: ProtoClass<T>, val: Record<string, any>): boolean
  abstract createDisplay(engine: string, name: string): Promise<string>
  abstract removeDisplay(engine: string, name: string): Promise<boolean>
  abstract renameDisplay(engine: string, oldName: string, newName: string): Promise<string>
}
```

### 方法说明

#### read
```typescript
abstract read<T extends BaseProto<T>>(
  cls: ProtoClass<T>,
  engineName?: string,
  force?: boolean
): Promise<T | undefined>
```
读取配置数据：
- 参数：
  - `cls`: 协议类
  - `engineName`: 引擎名称（可选）
  - `force`: 是否强制读取（可选）
- 返回：Promise<T | undefined>
- 说明：异步读取配置数据

#### write
```typescript
abstract write<T extends BaseProto<T>>(cls: ProtoClass<T>, engineName?: string): Promise<boolean>
```
写入配置数据：
- 参数：
  - `cls`: 协议类
  - `engineName`: 引擎名称（可选）
- 返回：Promise<boolean>
- 说明：异步写入配置数据

#### remove
```typescript
abstract remove<T extends BaseProto<T>>(cls: ProtoClass<T>): Promise<void>
```
删除配置数据：
- 参数：
  - `cls`: 协议类
- 返回：Promise<void>
- 说明：异步删除配置数据

#### assign
```typescript
abstract assign<T extends BaseProto<T>>(cls: ProtoClass<T>, val: Record<string, any>): boolean
```
分配配置数据：
- 参数：
  - `cls`: 协议类
  - `val`: 配置值
- 返回：boolean
- 说明：同步分配配置数据

#### createDisplay
```typescript
abstract createDisplay(engine: string, name: string): Promise<string>
```
创建显示：
- 参数：
  - `engine`: 引擎名称
  - `name`: 显示名称
- 返回：Promise<string>
- 说明：异步创建显示

#### removeDisplay
```typescript
abstract removeDisplay(engine: string, name: string): Promise<boolean>
```
删除显示：
- 参数：
  - `engine`: 引擎名称
  - `name`: 显示名称
- 返回：Promise<boolean>
- 说明：异步删除显示

#### renameDisplay
```typescript
abstract renameDisplay(engine: string, oldName: string, newName: string): Promise<string>
```
重命名显示：
- 参数：
  - `engine`: 引擎名称
  - `oldName`: 原显示名称
  - `newName`: 新显示名称
- 返回：Promise<string>
- 说明：异步重命名显示

### 静态属性
```typescript
static override get NAME() { return 'Cfg' }
static get CHANGED() { return 'Cfg.CHANGED' }
static get OnCustomer() { return 'Cfg.OnCustomer' }
static get OnCustomers() { return 'Cfg.OnCustomers' }
static get OnVersion() { return 'Cfg.OnVersion' }
static get OnVersions() { return 'Cfg.OnVersions' }
```

## 类型定义
```typescript
export type CfgMtr = BaseMtr<ICfg>
export type CfgItr = BaseItr<ICfg>
```

## 使用示例
```typescript
class MyCfg extends ICfg {
  async read<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    engineName?: string,
    force?: boolean
  ): Promise<T | undefined> {
    // 实现读取逻辑
  }

  async write<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    engineName?: string
  ): Promise<boolean> {
    // 实现写入逻辑
    return true;
  }

  // ... 实现其他抽象方法
}
```

## 注意事项
1. 所有配置操作都是异步的，返回 Promise
2. 配置类需要继承 BaseProto
3. 错误处理使用 ErrorCode 枚举
4. 事件通知使用 CfgEvent 接口
5. 显示操作与引擎关联
6. 所有抽象方法都需要在具体实现类中实现 
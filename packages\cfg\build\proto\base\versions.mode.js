import { types } from '@wuk/wkp'

export const Version = {
  $keys: ['name', 'md5', 'comments'],

  name: types.string,
  md5: types.string,
  comments: types.string
}

export const CustVersion = {
  $keys: ['name', 'versions'],

  name: types.string,
  versions: types.arrayOf(Version)
}

export const Versions = {
  $MAX: 1000,
  $MIN: 50,
  $keys: ['customers'],

  customers: types.arrayOf(CustVersion)
}

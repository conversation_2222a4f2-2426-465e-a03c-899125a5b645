$DISPLAY "Button" background=NewBlue editres=346:448 movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$STOREDISPLAY Append
$OBJECT FuncButton font=66 startx=5 starty=5 endx=117 endy=45
type="EXIT" label="ARUNC" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=122 starty=5 endx=234 endy=45
type="EXIT" label="Strip Charts" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=239 starty=5 endx=341 endy=45
type="EXIT" label="Tare" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=5 starty=50 endx=117 endy=90
type="EXIT" label="Flow Sensor" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=122 starty=50 endx=234 endy=90
type="CHANGEDSP" label="Averaging" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
crtname="SECOND CRT" quadindx=0 displayname="Temperature"
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=239 starty=50 endx=341 endy=90
type="EXIT" label="Start Screen" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=5 starty=95 endx=117 endy=135
type="EXIT" label="Surge Check" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=122 starty=95 endx=234 endy=135
type="EXIT" label="Spectrum" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=239 starty=95 endx=341 endy=135
type="EXIT" label="Performance" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=5 starty=140 endx=117 endy=180
type="EXIT" label="IGV Offset" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=122 starty=140 endx=234 endy=180
type="EXIT" label="Log Data" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=122 starty=185 endx=234 endy=225
type="CHANGEDSP" label="Fuel" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
crtname="SECOND CRT" quadindx=0 displayname="Fuel"
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=239 starty=185 endx=341 endy=225
type="EXIT" label="Limits" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=122 starty=230 endx=234 endy=270
type="EXIT" label="Generator" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=239 starty=230 endx=341 endy=270
type="EXIT" label="Copyright" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=122 starty=275 endx=234 endy=315
type="EXIT" label="Logger" backgroundcolor=BrightBlue textcolor=Black fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=122 starty=320 endx=234 endy=360
type="EXIT" label="About" backgroundcolor=BrightBlue textcolor=Black fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=239 starty=320 endx=341 endy=360
type="EXIT" label="Exit" backgroundcolor=Gold textcolor=Black fontsize=16 fontweight=500
$END-OBJECT
*******
$END-DISPLAY
/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  RangeValue as RangeValueModal,
  VxiCalcLib as VxiCalcLibModal,
  VxiEngine as VxiEngineModal,
  VxiSignal as VxiSignalModal,
  VixCfg as VixCfgModal
} from '../modal/vixcfg.mode'

export class RangeValue extends MapProto<RangeValue> {
  min = 0.0
  max = 0.0
  unit = ''

  constructor(val?: Partial<RangeValue>) {
    super(RangeValueModal, 'RangeValue')
    val && this.assign(val)
  }
}

export class VxiCalcLib extends MapProto<VxiCalcLib> {
  type = 0
  min = 0.0
  max = 0.0
  data: Array<number> = []

  constructor(val?: Partial<VxiCalcLib>) {
    super(VxiCalcLibModal, 'VxiCalcLib')
    val && this.assign(val)
  }
}

export class VxiEngine extends MapProto<VxiEngine> {
  name = ''
  rate_group = ''
  signal_range = new RangeValue()
  scale_range = new RangeValue()
  progfilter = 0
  sensitivity = 0
  measwindow = 0.0
  calib_mode = 0
  calib = 0
  calib_data = new VxiCalcLib()
  alias = ''
  dfilter = 0

  constructor(val?: Partial<VxiEngine>) {
    super(VxiEngineModal, 'VxiEngine')
    val && this.assign(val)
  }
}

export class VxiSignal extends MapProto<VxiSignal> {
  name = ''
  channel = 0
  type = 0
  rate_group = ''
  signal_range = new RangeValue()
  scale_range = new RangeValue()
  progfilter = 0
  sensitivity = 0
  measwindow = 0.0
  dfilter = 0
  calib_mode = 0
  calib = 0
  calib_data = new VxiCalcLib()
  engines: Array<VxiEngine> = []

  constructor(val?: Partial<VxiSignal>) {
    super(VxiSignalModal, 'VxiSignal')
    val && this.assign(val)
  }
}

export class VixCfg extends RootProto<VixCfg> {
  name = ''
  rate_groups: Array<number> = []
  scp: Array<number> = []
  board_calcs_on = false
  value1 = 0
  value2 = 0
  value3 = 0

  constructor(val?: Partial<VixCfg>) {
    super(VixCfgModal, VixCfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return VixCfgModal.$MAX
  }

  static get minType() {
    return VixCfgModal.$MIN
  }

  static get uri() {
    return URI(VixCfgModal.$MAX, VixCfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [VixCfgModal.$MAX, VixCfgModal.$MIN]
  }

  static get key() {
    return 'VixCfg'
  }
}

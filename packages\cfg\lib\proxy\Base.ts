import { <PERSON><PERSON><PERSON><PERSON>indow, ipc<PERSON>ain } from 'electron'
import { Dispatch, IMethod } from '../base'
import { IBase, IBaseProxy } from '../interfaces'
import { createBridge } from '../utils'

const objectPrototype = Object.prototype
const defineProperty = Object.defineProperty
export function addHiddenProp<T = object>(object: T, key: PropertyKey, value: any) {
  defineProperty(object, key, {
    enumerable: false,
    writable: true,
    configurable: true,
    value
  })
}

export function hasProp<T = object>(target: T, key: PropertyKey) {
  return objectPrototype.hasOwnProperty.call(target, key)
}

export const createSymbol = (key: string) => (typeof Symbol !== undefined ? Symbol(key) : key)

const kStoredMethodKey = createSymbol('module-out-method-key')
export function method<T extends BaseProxy>(name?: string) {
  return (prototype: T, propertyKey: string, descriptor: PropertyDescriptor) => {
    if (!hasProp(prototype, kStoredMethodKey)) {
      addHiddenProp<T>(prototype, kStoredMethodKey, {})
    }
    if (typeof descriptor.value === 'function') {
      propertyKey = name || propertyKey
      // const func = (descriptor.value as Function).bind(prototype)
      ;(prototype as any)[kStoredMethodKey][propertyKey] = descriptor.value
    }
    return descriptor
  }
}

const storedPropertyKey = createSymbol('module-out-property-key')
export function property<T extends BaseProxy>(name?: string) {
  return (prototype: T, propertyKey: string) => {
    if (!hasProp(prototype, storedPropertyKey)) {
      addHiddenProp<T>(prototype, storedPropertyKey, {})
    }
    name = name || propertyKey
    defineProperty((prototype as any)[storedPropertyKey], name, {
      get() {
        return (prototype as any)[propertyKey]
      },
      configurable: false
    })
  }
}

export class BaseProxy<T extends IBaseProxy = object, K = string>
  extends Dispatch
  implements IBase<T, K>
{
  private _outputs: Record<string, any>
  protected _modules: BaseProxy[]

  constructor(
    protected readonly name: string,
    protected readonly master = false,
    protected readonly win_?: BrowserWindow
  ) {
    super()

    this._outputs = {}
    this._modules = []
  }

  get proxy(): T {
    const methods = (this as any)[kStoredMethodKey]
    const propertys = (this as any)[storedPropertyKey]
    const proxy = {
      ...methods,
      ...propertys,
      ...this._outputs
    }
    return proxy as T
  }

  method(key: K, target: object, value: IMethod) {
    this._outputs[key as string] = value
  }

  property(key: K, value: any): void {
    this._outputs[key as string] = value
  }

  override async init() {
    this.log('init')
    if (this.master) {
      for (const module of this._modules) {
        await module.init()
      }
    }
    await super.init()
  }

  async load(force = false) {
    this.master &&
      this._modules.forEach(async module => {
        await module.load(force)
      })
  }
  async clear() {
    this.master &&
      this._modules.forEach(async module => {
        await module.clear()
      })
  }

  override async destroy() {
    this.master &&
      this._modules.forEach(async module => {
        await module.destroy()
      })
    await super.destroy()

    this.log('destroy')
  }

  override async start() {
    this.log('start')
    this._modules.forEach(async module => {
      await module.start()
    })
    await super.start()
  }

  override async stop() {
    this.master &&
      this._modules.forEach(async module => {
        await module.stop()
      })
    await super.stop()
    this.master && ipcMain?.removeAllListeners()

    this.log('stop')
  }

  log(fmt = '', ...args: any[]) {
    const centent = this.format(fmt, ...args)
    console.info(this.name, centent)
  }

  expose() {
    return this.createExpose()
  }

  protected unImplementP(key = ''): Promise<any> {
    return Promise.reject(`========${this.name}.${key} unimplemented error====`)
  }

  protected createExpose(): Record<string, IMethod> {
    this._outputs = {}
    const result: Record<string, IMethod> = createBridge<K>(
      this.name,
      this.master,
      this,
      this.proxy,
      (key: K, target: object, value: IMethod) => {
        this.method(key, target, value)
      }
    )

    return result
  }

  private format(fmt: string, ...args: any[]): string {
    const exg = /{%(\d+)}/g
    if (exg.test(fmt)) {
      fmt = fmt.replace(exg, (_: any, x: any) => {
        let result = args[x - 1]
        if (result && typeof result !== 'string' && typeof result !== 'number') {
          try {
            result = JSON.stringify(result)
          } catch (error) {
            result = '{stringify error}'
          }
        }
        return result
      })
    }
    return fmt
  }
}

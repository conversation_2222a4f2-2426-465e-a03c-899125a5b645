import { BaseItr, BaseMtr, IBase } from './IBase'

export type ActionKey = number | string

export interface AppMenuItem {
  key?: ActionKey
  label?: string
  type?: 'normal' | 'separator' | 'submenu' | 'checkbox' | 'radio'
  role?:
    | 'undo'
    | 'redo'
    | 'cut'
    | 'copy'
    | 'paste'
    | 'pasteAndMatchStyle'
    | 'delete'
    | 'selectAll'
    | 'reload'
    | 'forceReload'
    | 'toggleDevTools'
    | 'resetZoom'
    | 'zoomIn'
    | 'zoomOut'
    | 'toggleSpellChecker'
    | 'togglefullscreen'
    | 'window'
    | 'minimize'
    | 'close'
    | 'help'
    | 'about'
    | 'services'
    | 'hide'
    | 'hideOthers'
    | 'unhide'
    | 'quit'
    | 'showSubstitutions'
    | 'toggleSmartQuotes'
    | 'toggleSmartDashes'
    | 'toggleTextReplacement'
    | 'startSpeaking'
    | 'stopSpeaking'
    | 'zoom'
    | 'front'
    | 'appMenu'
    | 'fileMenu'
    | 'editMenu'
    | 'viewMenu'
    | 'shareMenu'
    | 'recentDocuments'
    | 'toggleTabBar'
    | 'selectNextTab'
    | 'selectPreviousTab'
    | 'showAllTabs'
    | 'mergeAllWindows'
    | 'clearRecentDocuments'
    | 'moveTabToNewWindow'
    | 'windowMenu'

  submenu?: AppMenuItem[]
}

export abstract class IMenu extends IBase<IMenu> {
  abstract showRightMenu(
    actionKey: ActionKey,
    items: AppMenuItem[],
    x: number,
    y: number
  ): Promise<boolean>

  static override get NAME() {
    return 'Menu'
  }

  static get MenuClicked() {
    return 'Menu.MenuClicked'
  }
}

export type MenuMtr = BaseMtr<IMenu>
export type MenuItr = BaseItr<IMenu>

/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Quad as QuadModal,
  Crt as CrtModal,
  Crts as CrtsModal
} from '../modal/crts.mode'

export class Quad extends MapProto<Quad> {
  position = 0
  change_display_mode = ''
  list: Array<string> = []

  constructor(val?: Partial<Quad>) {
    super(QuadModal, 'Quad')
    val && this.assign(val)
  }
}

export class Crt extends MapProto<Crt> {
  name = ''
  quads: Array<Quad> = []

  constructor(val?: Partial<Crt>) {
    super(CrtModal, 'Crt')
    val && this.assign(val)
  }
}

export class Crts extends RootProto<Crts> {
  crts: Array<Crt> = []

  constructor(val?: Partial<Crts>) {
    super(CrtsModal, Crts.key)
    val && this.assign(val)
  }

  static get maxType() {
    return CrtsModal.$MAX
  }

  static get minType() {
    return CrtsModal.$MIN
  }

  static get uri() {
    return URI(CrtsModal.$MAX, CrtsModal.$MIN)
  }

  static get types(): [number, number] {
    return [CrtsModal.$MAX, CrtsModal.$MIN]
  }

  static get key() {
    return 'Crts'
  }
}

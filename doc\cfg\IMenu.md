# IMenu 接口文档
## 相关文档
- [类型定义](type.md) - 包含 `MenuConfig` 等类型定义
- [IBase](IBase.md) - 基础接口
- [ISdk](ISdk.md) - SDK 基础接口
- [ICfg](ICfg.md) - 配置接口
- [ISvc](ISvc.md) - 服务接口
- [IWin](IWin.md) - 窗口接口
- [IPage](IPage.md) - 页面接口
- [IApp](IApp.md) - 应用程序接口
- [IEngine](IEngine.md) - 引擎接口
- [IDisplay](IDisplay.md) - 显示接口
## 概述
`IMenu` 是一个抽象类，继承自 `IBase<IMenu>`，用于定义菜单管理的基本功能。它提供了右键菜单显示和菜单事件处理等功能。
## 类型定义
### 动作键
```typescript
export type ActionKey = number | string
```
### 菜单项
```typescript
export interface AppMenuItem {
  key?: ActionKey
  label?: string
  type?: 'normal' | 'separator' | 'submenu' | 'checkbox' | 'radio'
  role?:
    | 'undo'
    | 'redo'
    | 'cut'
    | 'copy'
    | 'paste'
    | 'pasteAndMatchStyle'
    | 'delete'
    | 'selectAll'
    | 'reload'
    | 'forceReload'
    | 'toggleDevTools'
    | 'resetZoom'
    | 'zoomIn'
    | 'zoomOut'
    | 'toggleSpellChecker'
    | 'togglefullscreen'
    | 'window'
    | 'minimize'
    | 'close'
    | 'help'
    | 'about'
    | 'services'
    | 'hide'
    | 'hideOthers'
    | 'unhide'
    | 'quit'
    | 'showSubstitutions'
    | 'toggleSmartQuotes'
    | 'toggleSmartDashes'
    | 'toggleTextReplacement'
    | 'startSpeaking'
    | 'stopSpeaking'
    | 'zoom'
    | 'front'
    | 'appMenu'
    | 'fileMenu'
    | 'editMenu'
    | 'viewMenu'
    | 'shareMenu'
    | 'recentDocuments'
    | 'toggleTabBar'
    | 'selectNextTab'
    | 'selectPreviousTab'
    | 'showAllTabs'
    | 'mergeAllWindows'
    | 'clearRecentDocuments'
    | 'moveTabToNewWindow'
    | 'windowMenu'
  submenu?: AppMenuItem[]
}
```
## 接口定义
### IMenu 抽象类
```typescript
export abstract class IMenu extends IBase<IMenu> {
  abstract showRightMenu(
    actionKey: ActionKey,
    items: AppMenuItem[],
    x: number,
    y: number
  ): Promise<boolean>

  static override get NAME() { return 'Menu' }
  static get MenuClicked() { return 'Menu.MenuClicked' }
}
```
## 方法说明
### showRightMenu
显示右键菜单：
- 参数：
  - `actionKey`: 动作键
  - `items`: 菜单项数组
  - `x`: 菜单显示位置的X坐标
  - `y`: 菜单显示位置的Y坐标
- 返回：Promise<boolean>
## 静态属性
- `NAME`: 返回菜单管理器名称
- `MenuClicked`: 返回菜单点击事件名称
## 类型定义
```typescript
export type MenuMtr = BaseMtr<IMenu>
export type MenuItr = BaseItr<IMenu>
```
## 使用示例
```typescript
class MyMenu extends IMenu {
  async showRightMenu(
    actionKey: ActionKey,
    items: AppMenuItem[],
    x: number,
    y: number
  ): Promise<boolean> {
    // 实现右键菜单显示逻辑
    return true;
  }
}
```
## 注意事项
1. 菜单操作是异步的，返回 Promise
2. 菜单项支持多种类型（normal、separator、submenu、checkbox、radio）
3. 菜单项支持多种角色（undo、redo、cut、copy等）
4. 支持子菜单嵌套
5. 菜单点击通过事件通知机制管理
6. 所有抽象方法都需要在具体实现类中实现
7. 菜单位置通过坐标指定 
import { types } from '@wuk/wkp'

export const QuestionItem = {
  $keys: ['type', 'id', 'prompt', 'answer', 'prev_param', 'operator', 'answer_set', 'enum', 'format'],

  type: types.string,
  id: types.string,
  prompt: types.string,
  answer: types.string,
  prev_param: types.string,
  operator: types.string,
  answer_set: types.string,
  enum: types.string,
  format: types.string
}

export const Questions = {
  $MAX: 2000,
  $MIN: 90,
  $keys: ['type', 'name', 'question_vec'],

  type: types.string,
  name: types.string,
  question_vec: types.arrayOf(QuestionItem)
}

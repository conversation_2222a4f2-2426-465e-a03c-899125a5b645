/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  CfgOfLimit as CfgOfLimitModal,
  RateOfLimit as RateOfLimitModal,
  LimitCfg as LimitCfgModal,
  LimitParameter as LimitParameterModal,
  AlarmRelay as AlarmRelayModal,
  LimitCounter as LimitCounterModal,
  Limits as LimitsModal
} from '../modal/limits.mode'

export class CfgOfLimit extends MapProto<CfgOfLimit> {
  limit = ''
  deadband = 0.0
  wait_sec = 0.0
  below_message = ''
  above_message = ''
  phase = ''
  color = ''
  alarm = ''
  normality = 0
  store_event = false

  constructor(val?: Partial<CfgOfLimit>) {
    super(CfgOfLimitModal, 'CfgOfLimit')
    val && this.assign(val)
  }
}

export class RateOfLimit extends MapProto<RateOfLimit> {
  eng_units = 0.0
  time_sec = 0.0
  exceeded_message = ''
  back_message = ''
  phase = ''
  color = ''
  alarm = ''
  store_event = false

  constructor(val?: Partial<RateOfLimit>) {
    super(RateOfLimitModal, 'RateOfLimit')
    val && this.assign(val)
  }
}

export class LimitCfg extends MapProto<LimitCfg> {
  cfg_list: Array<CfgOfLimit> = []
  rate_list: Array<RateOfLimit> = []

  constructor(val?: Partial<LimitCfg>) {
    super(LimitCfgModal, 'LimitCfg')
    val && this.assign(val)
  }
}

export class LimitParameter extends MapProto<LimitParameter> {
  param_id = ''
  start = new LimitCfg()
  run = new LimitCfg()

  constructor(val?: Partial<LimitParameter>) {
    super(LimitParameterModal, 'LimitParameter')
    val && this.assign(val)
  }
}

export class AlarmRelay extends MapProto<AlarmRelay> {
  input = ''
  clear = ''
  output = ''

  constructor(val?: Partial<AlarmRelay>) {
    super(AlarmRelayModal, 'AlarmRelay')
    val && this.assign(val)
  }
}

export class LimitCounter extends MapProto<LimitCounter> {
  color = ''
  param_id = ''

  constructor(val?: Partial<LimitCounter>) {
    super(LimitCounterModal, 'LimitCounter')
    val && this.assign(val)
  }
}

export class Limits extends RootProto<Limits> {
  alarm_relay = new AlarmRelay()
  limit_counters: Array<LimitCounter> = []
  parameters: Array<LimitParameter> = []

  constructor(val?: Partial<Limits>) {
    super(LimitsModal, Limits.key)
    val && this.assign(val)
  }

  static get maxType() {
    return LimitsModal.$MAX
  }

  static get minType() {
    return LimitsModal.$MIN
  }

  static get uri() {
    return URI(LimitsModal.$MAX, LimitsModal.$MIN)
  }

  static get types(): [number, number] {
    return [LimitsModal.$MAX, LimitsModal.$MIN]
  }

  static get key() {
    return 'Limits'
  }
}

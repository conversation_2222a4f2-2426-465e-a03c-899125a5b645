$DISPLAY "Fuel" background=NewBlue editres=MEDRES movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$OBJECT Text font=1 startx=350 starty=15 endx=550 endy=105
Black direction=1 alignment=1 fontweight=400 fontsize=36
"Fuel Page"
$END-OBJECT
***********
$OBJECT Line font=1 startx=365 starty=60 endx=535 endy=62
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=53 starty=80 endx=324 endy=448
labelspace=8 unitspace=6 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=28 fontweight=500 labelfontsize=22 \
labelfontweight=500 unitfontsize=16 unitfontweight=400 
Digital1 type="FLOAT" width=10 prec=1 label="Fuel Inlet Pressure" units="Degrees F" 
Digital2 type="FLOAT" width=10 prec=1 label="Fuel Press Pump" units="In/S"
Digital3 type="FLOAT" width=10 prec=1 label="Flowmeter Temperature" units="Deg" 
$END-OBJECT
***********
$OBJECT Input font=1 startx=51 starty=450 endx=300 endy=560
UTR32_40 label="Fuel Sample Temp" labelcolor=Black max=100.00   min=0.00     \
interval=0    width=10 prec=2 bar=0 \
leftlabel="<" calclabel="cal" rightlabel=">" \
parambox=2 paramboxcolor=White shading=1 delta=1.00    
$END-OBJECT
***********
$OBJECT Input font=1 startx=51 starty=570 endx=300 endy=680
UTR32_40 label="Fuel Sample SG" labelcolor=Black max=100.00   min=0.00     \
interval=0    width=10 prec=2 bar=0 \
leftlabel="<" calclabel="cal" rightlabel=">" \
parambox=2 paramboxcolor=White shading=1 delta=1.00    
$END-OBJECT
***********
$OBJECT Input font=1 startx=52 starty=690 endx=300 endy=794
UTR32_40 label="Lower Heating Value" labelcolor=Black max=100.00   min=0.00     \
interval=0    width=10 prec=2 bar=0 \
leftlabel="<" calclabel="cal" rightlabel=">" \
parambox=2 paramboxcolor=White shading=1 delta=1.00    
$END-OBJECT
***********
$OBJECT Digital font=20 startx=60 starty=800 endx=300 endy=880
labelspace=8 unitspace=6 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=28 fontweight=500 labelfontsize=22 \
labelfontweight=500 unitfontsize=16 unitfontweight=400 
Digital1 type="FLOAT" width=10 prec=1 label="Calculated SG" units="Degrees F" 
$END-OBJECT
***********
$OBJECT Text font=1 startx=400 starty=90 endx=680 endy=120
Black direction=1 alignment=2 fontweight=400 fontsize=24
"Fuel Meter Serial #"
$END-OBJECT
***********
$OBJECT Text font=1 startx=400 starty=160 endx=680 endy=190
Black direction=1 alignment=2 fontweight=400 fontsize=24
"Fuel Meter Frequency"
$END-OBJECT
***********
$OBJECT Text font=1 startx=400 starty=254 endx=680 endy=284
Black direction=1 alignment=2 fontweight=400 fontsize=24
"Observed Flow"
$END-OBJECT
***********
$OBJECT Text font=1 startx=400 starty=344 endx=680 endy=374
Black direction=1 alignment=2 fontweight=400 fontsize=24
"Observed Flow"
$END-OBJECT
***********
$OBJECT Text font=1 startx=400 starty=434 endx=680 endy=464
Black direction=1 alignment=2 fontweight=400 fontsize=24
"LHV Corrected Flow"
$END-OBJECT
***********
$OBJECT Box font=1 startx=700 starty=15 endx=1230 endy=515
style=1
linewidth=4
linecolor=ShallowBlue
boxcolor=DarkGray
shading=26
$END-OBJECT
***********
$OBJECT Text font=1 startx=750 starty=33 endx=897 endy=78
Black direction=1 alignment=1 fontweight=400 fontsize=28
"Meter # 1"
$END-OBJECT
***********
$OBJECT Line font=1 startx=750 starty=67 endx=880 endy=67
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=740 starty=90 endx=930 endy=520
labelspace=2 unitspace=5 spacing=28 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 labelfontweight=400 \
labelfontweight=500 unitfontsize=16 unitfontweight=400 
Digital1 type="FLOAT" width=12 prec=0 label="" units="" 
Digital2 type="FLOAT" width=12 prec=0 label="" units="Hz"
Digital3 type="FLOAT" width=12 prec=0 label="" units="GPM" 
Digital1 type="FLOAT" width=12 prec=0 label="" units="PPH" 
Digital2 type="FLOAT" width=12 prec=0 label="" units="PPH"
$END-OBJECT
***********
$OBJECT Text font=1 startx=1031 starty=35 endx=1188 endy=80
Black direction=1 alignment=1 fontweight=400 fontsize=28
"Meter # 2"
$END-OBJECT
***********
$OBJECT Line font=1 startx=1050 starty=67 endx=1180 endy=67
linewidth=2 linecolor=Black
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=1031 starty=87 endx=1195 endy=486
labelspace=2 unitspace=5 spacing=28 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=22 fontweight=500 labelfontsize=15 labelfontweight=400 \
labelfontweight=500 unitfontsize=16 unitfontweight=400 
Digital1 type="FLOAT" width=12 prec=0 label="" units="" 
Digital2 type="FLOAT" width=12 prec=0 label="" units="Hz"
Digital3 type="FLOAT" width=12 prec=0 label="" units="GPM" 
Digital1 type="FLOAT" width=12 prec=0 label="" units="PPH" 
Digital2 type="FLOAT" width=12 prec=0 label="" units="PPH"
$END-OBJECT
***********
$OBJECT Box font=1 startx=531 starty=552 endx=971 endy=882
style=1
linewidth=4
linecolor=ShallowBlue
boxcolor=DarkGray
shading=26
$END-OBJECT
***********
$OBJECT Input font=1 startx=579 starty=604 endx=771 endy=710
UTR32_40 label="Fuel In Tank" labelcolor=Black max=100.00   min=0.00     \
interval=0    width=10 prec=2 bar=0 \
leftlabel="<" calclabel="cal" rightlabel=">" \
parambox=2 paramboxcolor=White shading=1 delta=1.00    
$END-OBJECT
***********
$OBJECT Input font=1 startx=580 starty=730 endx=774 endy=835
UTR32_40 label="Gallons This Run" labelcolor=Black max=100.00   min=0.00     \
interval=0    width=10 prec=2 bar=0 \
leftlabel="<" calclabel="cal" rightlabel=">" \
parambox=2 paramboxcolor=White shading=1 delta=1.00    
$END-OBJECT
***********
$OBJECT Button font=1 startx=800 starty=630 endx=930 endy=675
TOGGLE oilleveltoggle setvalue=1 onlabel="Fill Tank" offlabel="Fill Tank" \
oncolor=DarkGray offcolor=DarkGray labelcolor=Black
$END-OBJECT
***********
$OBJECT Button font=1 startx=800 starty=750 endx=930 endy=795
TOGGLE oilleveltoggle setvalue=1 onlabel="Reset" offlabel="Reset" \
oncolor=DarkGray offcolor=DarkGray labelcolor=Black
$END-OBJECT
***********
$OBJECT FuncButton font=66 startx=1090 starty=860 endx=1210 endy=910
type="CLOSECRT" label="Close" backgroundcolor=DarkGray textcolor=Black  fontsize=24 fontweight=400
$END-OBJECT
***********
$END-DISPLAY


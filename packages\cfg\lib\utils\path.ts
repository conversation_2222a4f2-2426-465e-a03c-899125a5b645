import { app } from 'electron'
import { existed, makeDir } from './file'
import path, { join } from 'node:path'

export class Path {
  private static _appDir = ''
  private static _homeDir = ''
  private static _appdataDir = ''

  static get appDir(): string {
    if (app.isPackaged) {
      return process.resourcesPath
    }

    return Path._appDir || (Path._appDir = app.getAppPath())
  }

  static set appDir(val: string) {
    this._appDir = val
  }

  static get homeDir(): string {
    return Path._homeDir || (Path._homeDir = app.getPath('home'))
  }

  static get appdataDir(): string {
    return Path._appdataDir || (Path._appdataDir = app.getPath('appData'))
  }

  static get dataDir(): string {
    return Path.join(Path.appDir, 'cfg')
  }

  static get toolsDir(): string {
    return Path.join(Path.appDir, 'bin')
  }

  static get backDir(): string {
    return Path.join(Path.appDir, 'back')
  }

  static get versionDir(): string {
    return Path.join(Path.appdataDir, 'wuk/data')
  }

  static imageDir(customer: string): string {
    return Path.join(Path.dataDir, customer, 'images')
  }

  static imageURL(fileName: string) {
    return `images/${path.basename(fileName)}`
  }

  static customerURL(customer: string, fileName: string) {
    if (fileName.includes('http:') || fileName.includes('https:')) {
      return fileName
    }

    return `app://${customer}/${fileName}`
  }

  static join(...args: any[]) {
    return join(...args)
  }

  static ensureBackDir(): string {
    let result = Path.backDir
    if (!existed(result) && makeDir(result)) {
      result = ''
    }

    return result
  }

  static ensureDataDir(): string {
    let result = Path.dataDir
    if (!existed(result) && makeDir(result)) {
      result = ''
    }

    return result
  }

  static ensureImageDir(customer: string): string {
    let result = Path.imageDir(customer)
    if (!existed(result) && makeDir(result)) {
      result = ''
    }

    return result
  }

  static ensureVersionDir(): string {
    let result = Path.versionDir
    if (!existed(result) && makeDir(result)) {
      result = ''
    }
    return result
  }
}

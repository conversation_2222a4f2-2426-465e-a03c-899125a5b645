import { BrowserWindow } from 'electron'
import {
  ICfg,
  I<PERSON>ngine,
  IDevice,
  VibSystemOption,
  VibInputOption,
  VibBandFilterOption,
  VibTachInputOption,
  VibTFInputOption,
  VibSpectrumOption,
  VibSignalOption,
  PlcDriverOptions,
  PlcCfgOption,
  SetupOptions,
  PlcSignals,
  PlcSignal
} from '../interfaces'
import { BaseProxy, method } from './Base'

import { plcdriver, tables, vibcfg, vibsetup, vibsigs } from '../proto'

const kSystemDefault = {
  measurement_rate: 0,
  band_width: 0,
  fft_lines: 0
}

const kInputDefault = {
  sensitivity: 0.0,
  gain: 0,
  input_range: 0.0,
  transducer_type: 0,
  ad_gain: 0,
  fft_size: 0,
  sampling_rate: 0
}

const kBandFilterDefault = {
  vib_channel: 0,
  output_units: 0,
  detector_type: 0,
  time_constant: 0,
  filter_shape: 0,
  filter_type: 0,
  upper_cutoff: 0,
  lower_cutoff: 0
}

const kTachInputDefault = {
  tach_processing: 0,
  rotor_speed: 0.0,
  tach_freq: 0.0,
  sensitivity: 0.0
}

const kTFInputDefault = {
  vib_channel: 0,
  tach_channel: 0,
  output_units: 0,
  detector_type: 0,
  full_scale_units: 0.0,
  full_scale_volts: 0.0,
  filter_specification: 0,
  filter_q: 0,
  band_width: 0,
  time_constant: 0,
  order_tracking: 0
}

const kSpectrumDefault = {
  output_units: 0,
  start_frequency: 0,
  end_frequency: 0,
  detector_type: 0,
  full_scale_units: 0.0
}

export class Device extends BaseProxy<IDevice> implements IDevice {
  private static readonly kVipSetupCfgFile = 'vibsetup.cfg'
  private static readonly kVipSignalFile = 'vibsigs.cfg'

  private _vibcfg: SetupOptions[]
  private _caches: Record<
    string,
    {
      system: VibSystemOption
      vib_inputs: VibInputOption[]
      band_filters: VibBandFilterOption[]
      tach_inputs: VibTachInputOption[]
      tf_inputs: VibTFInputOption[]
      spectrums: VibSpectrumOption[]
    }
  >

  private _vib_signals: VibSignalOption[]
  private _plc_setups: PlcDriverOptions

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(IDevice.NAME, master, win)

    this._vibcfg = []
    this._caches = {}

    this._vib_signals = []
    this._plc_setups = { scan_rate: 0, plcs: [] }
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readSetups(): Promise<Array<SetupOptions>> {
    return this._vibcfg
  }

  @method()
  async removeSetup(index: number): Promise<boolean> {
    if (this._vibcfg.length <= index) {
      return false
    }
    const fileName = this._vibcfg[index].file || ''
    this._vibcfg.splice(index, 1)

    await this.cfg.removeFile(fileName, this.engineName)
    const changed = await this.writeVibCfg()
    changed && this.emit(IDevice.OnSetupOptions)

    return changed
  }

  @method()
  async addSetup(val: SetupOptions, index?: number): Promise<boolean> {
    const fileName = `vibsetup.${val.name}.cfg`
    const fromName = this._vibcfg[0].file || ''
    val.file = fileName
    if (index === undefined || index === null) {
      this._vibcfg.push(val)
    } else {
      this._vibcfg.splice(index, 0, val)
    }
    if (fromName) {
      this.cfg.copyFile(this.engineName, fromName, fileName)
    } else {
      this.cfg.createFile(fileName, this.engineName)
    }
    await this.loadVibSetupCfg()
    const changed = await this.writeVibCfg()
    changed && this.emit(IDevice.OnSetupOptions)

    return changed
  }

  @method()
  async modifySetup(index: number, val: Partial<SetupOptions>): Promise<boolean> {
    if (this._vibcfg.length <= index) {
      return false
    }
    const item = this._vibcfg[index]
    if (val.name !== undefined) item.name = val.name
    if (val.exp !== undefined) item.exp = val.exp
    if (val.file !== undefined) item.file = val.file

    const changed = await this.writeVibCfg()
    changed && this.emit(IDevice.OnSetupOptions)

    return changed
  }

  private async writeVibCfg() {
    if (this.engine && !this.engine.engineName) {
      return false
    }

    const setups = this._vibcfg.map(item => new vibcfg.VibSetup(item))
    let changed = this.cfg.assign(
      vibcfg.VibCfg,
      { setups },
      Device.kVipSetupCfgFile,
      this.engineName
    )
    changed =
      changed && (await this.cfg.write(vibcfg.VibCfg, this.engineName, Device.kVipSetupCfgFile))

    return changed
  }

  @method()
  async readVibSystem(index: number) {
    const key = this._vibcfg[index].file || ''
    const cache = this.getCache(key)
    return cache.system
  }

  @method()
  async writeVibSystem(index: number, val: Partial<VibSystemOption>) {
    const key = this._vibcfg[index].file || ''
    const cache = this._caches[key]
    if (!cache) {
      return false
    }

    const item = cache.system
    if (val.measurement_rate !== undefined) item.measurement_rate = val.measurement_rate
    if (val.band_width !== undefined) item.band_width = val.band_width
    if (val.fft_lines !== undefined) item.fft_lines = val.fft_lines

    const changed = await this.writeVibSetupCfg(index)
    changed && this.emit(IDevice.OnVibSystemOption)

    return changed
  }

  @method()
  async readVibInput(index: number) {
    const key = this._vibcfg[index].file || ''
    const cache = this.getCache(key)
    return cache.vib_inputs
  }

  @method()
  async writeVibInput(index: number, idx: number, val: Partial<VibInputOption>) {
    const key = this._vibcfg[index].file || ''
    const cache = this._caches[key]
    if (!cache || cache.vib_inputs.length <= idx) {
      return false
    }

    const item = cache.vib_inputs[idx]
    if (val.sensitivity !== undefined) item.sensitivity = val.sensitivity
    if (val.gain !== undefined) item.gain = val.gain
    if (val.input_range !== undefined) item.input_range = val.input_range
    if (val.transducer_type !== undefined) item.transducer_type = val.transducer_type
    if (val.ad_gain !== undefined) item.ad_gain = val.ad_gain
    if (val.fft_size !== undefined) item.fft_size = val.fft_size
    if (val.sampling_rate !== undefined) item.sampling_rate = val.sampling_rate

    const changed = await this.writeVibSetupCfg(index)
    changed && this.emit(IDevice.OnVibInputOption)

    return changed
  }

  @method()
  async readVibBandFilter(index: number) {
    const key = this._vibcfg[index].file || ''
    const cache = this.getCache(key)
    return cache.band_filters
  }

  @method()
  async writeVibBandFilter(index: number, idx: number, val: Partial<VibBandFilterOption>) {
    const key = this._vibcfg[index].file || ''
    const cache = this._caches[key]
    if (!cache || cache.band_filters.length <= idx) {
      return false
    }

    const item = cache.band_filters[idx]
    if (val.vib_channel !== undefined) item.vib_channel = val.vib_channel
    if (val.output_units !== undefined) item.output_units = val.output_units
    if (val.detector_type !== undefined) item.detector_type = val.detector_type
    if (val.time_constant !== undefined) item.time_constant = val.time_constant
    if (val.filter_shape !== undefined) item.filter_shape = val.filter_shape
    if (val.filter_type !== undefined) item.filter_type = val.filter_type
    if (val.upper_cutoff !== undefined) item.upper_cutoff = val.upper_cutoff
    if (val.lower_cutoff !== undefined) item.lower_cutoff = val.lower_cutoff

    const changed = await this.writeVibSetupCfg(index)
    changed && this.emit(IDevice.OnVibBandFilterOption)

    return changed
  }

  @method()
  async readVibTachInput(index: number) {
    const key = this._vibcfg[index].file || ''
    const cache = this.getCache(key)
    return cache.tach_inputs
  }

  @method()
  async writeVibTachInput(index: number, idx: number, val: Partial<VibTachInputOption>) {
    const key = this._vibcfg[index].file || ''
    const cache = this._caches[key]
    if (!cache || cache.tach_inputs.length <= idx) {
      return false
    }

    const item = cache.tach_inputs[idx]
    if (val.tach_processing !== undefined) item.tach_processing = val.tach_processing
    if (val.rotor_speed !== undefined) item.rotor_speed = val.rotor_speed
    if (val.tach_freq !== undefined) item.tach_freq = val.tach_freq
    if (val.sensitivity !== undefined) item.sensitivity = val.sensitivity

    const changed = await this.writeVibSetupCfg(index)
    changed && this.emit(IDevice.OnVibTachInputOption)

    return changed
  }

  @method()
  async readVibTFInput(index: number) {
    const key = this._vibcfg[index].file || ''
    const cache = this.getCache(key)
    return cache.tf_inputs
  }

  @method()
  async writeVibTFInput(index: number, idx: number, val: Partial<VibTFInputOption>) {
    const key = this._vibcfg[index].file || ''
    const cache = this._caches[key]
    if (!cache || cache.tf_inputs.length <= idx) {
      return false
    }

    const item = cache.tf_inputs[idx]
    if (val.vib_channel !== undefined) item.vib_channel = val.vib_channel
    if (val.tach_channel !== undefined) item.tach_channel = val.tach_channel
    if (val.output_units !== undefined) item.output_units = val.output_units
    if (val.detector_type !== undefined) item.detector_type = val.detector_type
    if (val.full_scale_units !== undefined) item.full_scale_units = val.full_scale_units
    if (val.full_scale_volts !== undefined) item.full_scale_volts = val.full_scale_volts
    if (val.filter_specification !== undefined) item.filter_specification = val.filter_specification
    if (val.filter_q !== undefined) item.filter_q = val.filter_q
    if (val.band_width !== undefined) item.band_width = val.band_width
    if (val.time_constant !== undefined) item.time_constant = val.time_constant
    if (val.order_tracking !== undefined) item.order_tracking = val.order_tracking

    const changed = await this.writeVibSetupCfg(index)
    changed && this.emit(IDevice.OnVibTFInputOption)

    return changed
  }

  @method()
  async readVibSpectrum(index: number) {
    const key = this._vibcfg[index].file || ''
    const cache = this.getCache(key)
    return cache.spectrums
  }

  @method()
  async writeVibSpectrum(index: number, idx: number, val: Partial<VibSpectrumOption>) {
    const key = this._vibcfg[index].file || ''
    const cache = this._caches[key]
    if (!cache || cache.spectrums.length <= idx) {
      return false
    }

    const item = cache.spectrums[idx]
    if (val.output_units !== undefined) item.output_units = val.output_units
    if (val.start_frequency !== undefined) item.start_frequency = val.start_frequency
    if (val.end_frequency !== undefined) item.end_frequency = val.end_frequency
    if (val.detector_type !== undefined) item.detector_type = val.detector_type
    if (val.full_scale_units !== undefined) item.full_scale_units = val.full_scale_units

    const changed = await this.writeVibSetupCfg(index)
    changed && this.emit(IDevice.OnVibSpectrumOption)

    return changed
  }

  @method()
  async readVibSignals() {
    return this._vib_signals
  }

  @method()
  async removeVibSignal(index: number) {
    this._vib_signals.splice(index, 1)

    const changed = await this.writeVibSigsCfg()
    changed && this.emit(IDevice.OnVibSignalOption)

    return changed
  }

  @method()
  async addVibSignal(val: VibSignalOption, index?: number) {
    if (index === undefined || index === null) {
      this._vib_signals.push(val)
    } else {
      this._vib_signals.splice(index, 0, val)
    }

    const changed = await this.writeVibSigsCfg()
    changed && this.emit(IDevice.OnVibSignalOption)

    return changed
  }

  @method()
  async modifyVibSignal(index: number, val: Partial<VibSignalOption>) {
    if (this._vib_signals.length <= index) {
      return false
    }
    const item = this._vib_signals[index]
    if (val.name !== undefined) item.name = val.name
    if (val.filter_type !== undefined) item.filter_type = val.filter_type
    if (val.sig_type !== undefined) item.sig_type = val.sig_type
    if (val.calib_mode !== undefined) item.calib_mode = val.calib_mode

    const changed = await this.writeVibSigsCfg()
    changed && this.emit(IDevice.OnVibSignalOption)

    return changed
  }

  @method()
  async readPLCSetups() {
    return this._plc_setups
  }

  @method()
  async modifyPLCSetup(index: number, val: Partial<PlcCfgOption>) {
    if (this._plc_setups.plcs.length <= index) {
      return false
    }
    const item = this._plc_setups.plcs[index]
    if (val.device_name !== undefined) item.device_name = val.device_name
    if (val.scan_rate !== undefined) item.scan_rate = val.scan_rate
    if (val.devices !== undefined) item.devices = val.devices

    const changed = await this.writePLCSetups()
    changed && this.emit(IDevice.OnPLCSetupOption)

    return changed
  }

  private get engineName() {
    return this.engine?.engineName || ''
  }

  private async loadVibCfg(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    const { setups = [] } =
      (await this.cfg.read(vibcfg.VibCfg, this.engineName, force, Device.kVipSetupCfgFile)) || {}

    this._vibcfg = setups.map(({ name, exp, file }) => ({ name, exp, file }))
    this.emit(IDevice.OnSetupOptions)
  }

  private getCache(name: string) {
    const cache =
      this._caches[name] ||
      (this._caches[name] = {
        system: { ...kSystemDefault },
        vib_inputs: Array.from({ length: 4 }, () => ({ ...kInputDefault })),
        band_filters: Array.from({ length: 4 }, () => ({ ...kBandFilterDefault })),
        tach_inputs: Array.from({ length: 4 }, () => ({ ...kTachInputDefault })),
        tf_inputs: Array.from({ length: 16 }, () => ({ ...kTFInputDefault })),
        spectrums: Array.from({ length: 4 }, () => ({ ...kSpectrumDefault }))
      })
    return cache
  }

  private async loadVibSetupCfg(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    this._vibcfg.forEach(async item => {
      const fileName = item.file || ''
      const {
        system,
        vib_inputs = [],
        band_filters = [],
        tach_inputs = [],
        tf_inputs = [],
        spectrums = []
      } = (await this.cfg.read(vibsetup.VibSetupCfg, this.engineName, force, fileName)) || {}

      const cache = this.getCache(fileName)
      if (system) {
        cache.system.band_width = system.band_width
        cache.system.fft_lines = system.fft_lines
        cache.system.measurement_rate = system.measurement_rate

        this.emit(IDevice.OnVibSystemOption)
      }

      if (vib_inputs.length > 0) {
        vib_inputs.forEach((item, index) => {
          const val = cache.vib_inputs[index]
          val.ad_gain = item.ad_gain
          val.fft_size = item.fft_size
          val.gain = item.gain
          val.input_range = item.input_range
          val.sampling_rate = item.sampling_rate
          val.sensitivity = item.sensitivity
          val.transducer_type = item.transducer_type
        })
        this.emit(IDevice.OnVibInputOption)
      }

      if (band_filters.length > 0) {
        band_filters.forEach((item, index) => {
          const val = cache.band_filters[index]
          val.detector_type = item.detector_type
          val.filter_shape = item.filter_shape
          val.filter_type = item.filter_type
          val.lower_cutoff = item.lower_cutoff
          val.output_units = item.output_units
          val.time_constant = item.time_constant
          val.upper_cutoff = item.upper_cutoff
          val.vib_channel = item.vib_channel
        })
        this.emit(IDevice.OnVibBandFilterOption)
      }

      if (tach_inputs.length > 0) {
        tach_inputs.forEach((item, index) => {
          const val = cache.tach_inputs[index]
          val.rotor_speed = item.rotor_speed
          val.sensitivity = item.sensitivity
          val.tach_freq = item.tach_freq
          val.tach_processing = item.tach_processing
        })
        this.emit(IDevice.OnVibTachInputOption)
      }

      if (tf_inputs.length > 0) {
        tf_inputs.forEach((item, index) => {
          const val = cache.tf_inputs[index]
          val.band_width = item.band_width
          val.detector_type = item.detector_type
          val.filter_q = item.filter_q
          val.filter_specification = item.filter_specification
          val.full_scale_units = item.full_scale_units
          val.full_scale_volts = item.full_scale_volts
          val.order_tracking = item.order_tracking
          val.output_units = item.output_units
          val.tach_channel = item.tach_channel
          val.time_constant = item.time_constant
          val.vib_channel = item.vib_channel
        })
        this.emit(IDevice.OnVibTFInputOption)
      }

      if (spectrums.length > 0) {
        spectrums.forEach((item, index) => {
          const val = cache.spectrums[index]
          val.detector_type = item.detector_type
          val.end_frequency = item.end_frequency
          val.full_scale_units = item.full_scale_units
          val.output_units = item.output_units
          val.start_frequency = item.start_frequency
        })
        this.emit(IDevice.OnVibSpectrumOption)
      }
    })
  }

  private async loadVibSigsCfg(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    const { signals = [] } =
      (await this.cfg.read(vibsigs.VibSigsCfg, this.engineName, force, Device.kVipSignalFile)) || {}
    this._vib_signals = signals.map(({ name, filter_type, sig_type, calib_mode }) => {
      return { name, filter_type, sig_type, calib_mode }
    })
    this.emit(IDevice.OnVibSignalOption)
  }

  private async loadPlcSetupCfg(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    const { scan_rate = 0, plcs: list = [] } =
      (await this.cfg.read(plcdriver.PlcDriverCfg, this.engineName, force)) || {}
    const plcs = list.map(({ device_name, scan_rate, devices: list }) => {
      const devices = list.map(({ type, addr, num, signals: sgs }) => {
        const signals: PlcSignals = {
          input_float: sgs.input_float,
          list: sgs.list.map((info: plcdriver.PlcSignal) => {
            const signal_range = { ...info.signal_range }
            const units_range = { ...info.units_range }
            const scale_range = { ...info.scale_range }
            const calib_data = { ...info.calib_data }
            return {
              ...info,
              signal_range,
              units_range,
              scale_range,
              calib_data
            }
          })
        }
        return { type, addr, num, signals }
      })
      return { device_name, scan_rate, devices }
    })
    this._plc_setups = { scan_rate, plcs }
    this.emit(IDevice.OnPLCSetupOption)
  }

  private async writeVibSigsCfg() {
    if (this.engine && !this.engine.engineName) {
      return false
    }
    const signals = this._vib_signals.map(item => new vibsigs.VibSignal(item))
    let changed = this.cfg.assign(
      vibsigs.VibSigsCfg,
      { signals },
      Device.kVipSignalFile,
      this.engineName
    )
    changed =
      changed && (await this.cfg.write(vibsigs.VibSigsCfg, this.engineName, Device.kVipSignalFile))

    return changed
  }

  private async writePLCSetups() {
    if (this.engine && !this.engine.engineName) {
      return false
    }
    const { scan_rate, plcs: list = [] } = this._plc_setups
    const plcs = list.map(item => {
      const devices = item.devices.map(item => {
        const list = item.signals.list.map(item => {
          const signal_range = new plcdriver.RangeValue(item.signal_range)
          const units_range = new plcdriver.RangeValue(item.units_range)
          const scale_range = new plcdriver.RangeValue(item.scale_range)
          const calib_data = new plcdriver.PlcCalcLib(item.calib_data)
          return new plcdriver.PlcSignal({
            ...item,
            signal_range,
            units_range,
            scale_range,
            calib_data
          })
        })
        const signals = new plcdriver.PlcSignals({ input_float: item.signals.input_float, list })
        return new plcdriver.PlcDevice({ ...item, signals })
      })
      return new plcdriver.PlcCfg({ ...item, devices })
    })

    let changed = this.cfg.assign(plcdriver.PlcDriverCfg, { scan_rate, plcs }, '', this.engineName)
    changed = changed && (await this.cfg.write(plcdriver.PlcDriverCfg, this.engineName))

    return changed
  }

  private async writeVibSetupCfg(index: number) {
    if (this.engine && !this.engine.engineName) {
      return false
    }

    const fileName = this._vibcfg[index].file || ''
    const cache = this._caches[fileName]
    if (!cache) {
      return false
    }

    const system = new vibsetup.VibSystem(cache.system)
    const vib_inputs = cache.vib_inputs.map(item => new vibsetup.VibInput(item))
    const band_filters = cache.band_filters.map(item => new vibsetup.VibBandFilter(item))
    const tach_inputs = cache.tach_inputs.map(item => new vibsetup.VibTachInput(item))
    const tf_inputs = cache.tf_inputs.map(item => new vibsetup.VibTFInput(item))
    const spectrums = cache.spectrums.map(item => new vibsetup.VibSpectrum(item))

    let changed = this.cfg.assign(
      vibsetup.VibSetupCfg,
      { system, vib_inputs, band_filters, tach_inputs, tf_inputs, spectrums },
      fileName,
      this.engineName
    )
    changed = changed && (await this.cfg.write(vibsetup.VibSetupCfg, this.engineName, fileName))

    return changed
  }

  private async loadOptions(force?: boolean) {
    await this.loadVibCfg(force)
    await this.loadVibSetupCfg(force)
    await this.loadVibSigsCfg(force)
    await this.loadPlcSetupCfg(force)
  }

  private async clearOptions() {
    this.cfg.remove(tables.Tables, '', this.engineName)
  }
}

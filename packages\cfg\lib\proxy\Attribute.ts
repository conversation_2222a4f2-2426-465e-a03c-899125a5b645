import { <PERSON><PERSON><PERSON><PERSON>indow } from 'electron'
import { ICfg, IEngine, IAttribute, AttributeItem, AttributeOptions } from '../interfaces'
import { BaseProxy, method } from './Base'

import { attribs } from '../proto'

export class Attribute extends BaseProxy<IAttribute> implements IAttribute {
  private _options: AttributeOptions

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(IAttribute.NAME, master, win)

    this._options = {
      list: []
    }
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readOptions(): Promise<AttributeOptions> {
    return this._options
  }

  @method()
  async removeAttribute(index: number) {
    this._options.list.splice(index, 1)

    const changed = await this.writeCache()
    changed && this.emit(IAttribute.OnOptions)

    return changed
  }

  @method()
  async addAttribute(val: AttributeItem, index?: number) {
    if (index === undefined || index === null) {
      this._options.list.push(val)
    } else {
      this._options.list.splice(index, 0, val)
    }

    const changed = await this.writeCache()
    changed && this.emit(IAttribute.OnOptions)

    return changed
  }

  @method()
  async modifyAttribute(index: number, val: Partial<AttributeItem>) {
    if (this._options.list.length <= index) {
      return false
    }
    const item = this._options.list[index]
    if (val.name !== undefined) item.name = val.name
    if (val.units !== undefined) item.units = val.units
    if (val.long_name !== undefined) item.long_name = val.long_name
    if (val.width !== undefined) item.width = val.width
    if (val.precision !== undefined) item.precision = val.precision

    const changed = await this.writeCache()
    changed && this.emit(IAttribute.OnOptions)

    return changed
  }

  private get engineName() {
    return this.engine?.engineName || 'common'
  }

  private async writeCache() {
    if (this.engine && !this.engine.engineName) {
      return false
    }

    const list = this._options.list.map(val => new attribs.Attrib(val))
    const changed = this.cfg.assign(attribs.Attribs, { attribs: list }, '', this.engineName)
    changed && (await this.cfg.write(attribs.Attribs, this.engineName))

    return changed
  }

  private async loadAttributes(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    this._options.list = []
    const { attribs: list = [] } = (await this.cfg.read(attribs.Attribs, this.engineName)) || {}
    list.forEach(({ name, units, width, precision, long_name }) =>
      this._options.list.push({ name, units, width, precision, long_name })
    )
    this.emit(IAttribute.OnOptions)
  }

  private async loadOptions(force?: boolean) {
    await this.loadAttributes(force)
  }

  private async clearOptions() {
    this.cfg.remove(attribs.Attribs, '', this.engineName)
  }
}

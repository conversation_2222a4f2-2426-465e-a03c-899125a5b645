import { Emitter, IConsole } from './Emitter'
import { UBase } from './UBase'

export class Dispatch extends UBase implements Emitter {
  protected _stoped: boolean

  constructor(protected console?: IConsole) {
    super()
    this._stoped = false
  }

  override emit(event: string, ...args: any[]): boolean {
    if (this._stoped) return false
    return super.emit(event, ...args)
  }

  async init(...args: any[]) {
    this._stoped = false
    this.emit(Dispatch.ONINIT)
  }

  async start() {
    this._stoped = false
    this.emit(Dispatch.ONSTART)
  }

  async stop() {
    this.emit(Dispatch.ONSTOP)
    super.removeAllListeners()
    this._stoped = true
  }

  async destroy() {
    this.emit(Dispatch.ONDESTROY)
    this._stoped = true
    super.removeAllListeners()
  }

  static get ONINIT(): string {
    return 'Dispatch.ONINIT'
  }

  static get ONSTART(): string {
    return 'Dispatch.ONSTART'
  }

  static get ONSTOP(): string {
    return 'Dispatch.ONSTOP'
  }

  static get ONDESTROY(): string {
    return 'Dispatch.ONDESTROY'
  }
}

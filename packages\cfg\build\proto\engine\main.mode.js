import { types } from '@wuk/wkp'

export const IncludeFile = {
  $keys: ['file', 'uri', 'depends'],

  file: types.string,
  uri: types.string,
  depends: types.arrayOf(types.string)
}

export const Main = {
  $MAX: 2000,
  $MIN: 80,
  $keys: ['scan_rate', 'test_mode', 'pla_rig_param', 'pla_idle_default', 'pla_takeoff_default', 'crs_on_param', 'run_limits_param', 'includes'],

  scan_rate: types.string,
  test_mode: types.boolean,
  pla_rig_param: types.string,
  pla_idle_default: types.string,
  pla_takeoff_default: types.string,
  crs_on_param: types.string,
  run_limits_param: types.string,
  includes: types.arrayOf(IncludeFile)
}

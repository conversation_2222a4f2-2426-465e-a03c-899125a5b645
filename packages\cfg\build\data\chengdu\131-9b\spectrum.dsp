$DISPLAY "Spectrum" background=NewGray editres=1280:448 movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$STOREDISPLAY Overwrite
***********
$OBJECT Plot font=1 startx=10 starty=5 endx=1260 endy=440
$TYPE SPECTRUMPLOT
$SPOT-PARAM Cycle
$LINE_WIDTH 2
$HIDE_BUTTON 1
$XAXIS currentoilqty1 533 40 902 100 3 BELOW BELOW 0 100 White 0.000000
$YAXIS CITF1 40 0 533 1 1 LEFT LEFT 0 120 White 1 0.000000
$COLOR
White
Yellow
$END-COLOR
$TABLE-PLOT Red
InvestMOP_T
$END-TABLE-PLOT
$END-YAXIS
$HARDCOPYPLOT 1 0 QUANTITY=1
$END-HARDCOPYPLOT
$END-OBJECT
***********
$OBJECT FuncButton font=22 startx=1210 starty=230 endx=1270 endy=280
type="CHANGEDSP" label="Close" backgroundcolor=DarkGray textcolor=Red fontsize=18 fontweight=400
crtname="Test CRT" quadindx=1 displayname="Main Second"
$END-OBJECT
***********
$END-DISPLAY

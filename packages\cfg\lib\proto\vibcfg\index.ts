/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  VibSetup as VibSetupModal,
  VibCfg as VibCfgModal
} from '../modal/vibcfg.mode'

export class VibSetup extends MapProto<VibSetup> {
  name = ''
  exp = ''
  file = ''

  constructor(val?: Partial<VibSetup>) {
    super(VibSetupModal, 'VibSetup')
    val && this.assign(val)
  }
}

export class VibCfg extends RootProto<VibCfg> {
  setups: Array<VibSetup> = []

  constructor(val?: Partial<VibCfg>) {
    super(VibCfgModal, VibCfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return VibCfgModal.$MAX
  }

  static get minType() {
    return VibCfgModal.$MIN
  }

  static get uri() {
    return URI(VibCfgModal.$MAX, VibCfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [VibCfgModal.$MAX, VibCfgModal.$MIN]
  }

  static get key() {
    return 'VibCfg'
  }
}

import { <PERSON><PERSON>erWindow } from 'electron'
import {
  HardwareInfo,
  hardwareTypes,
  ICfg,
  IEngine,
  IHardwares,
  PrinterInfo,
  VxiInfo,
  HardwareType,
  ArincCardCfg,
  VxiCard,
  vxiCardType,
  VxiCardType
} from '../interfaces'
import { BaseProxy, method } from './Base'
import { cust, vixcards, arinc429 } from '../proto'

export class Hardwares extends BaseProxy<IHardwares> implements IHardwares {
  private _hardwares: HardwareInfo[]

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(IHardwares.NAME, master, win)
    this._hardwares = []
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readHardwares() {
    return this._hardwares
  }

  @method()
  async removeHardware(index: number) {
    this._hardwares.splice(index, 1)

    const changed = await this.writeHardwareCache()
    if (changed) {
      this.emit(IHardwares.OnHardwareList)
    }

    return changed
  }

  @method()
  async addHardware(val: HardwareInfo, index?: number) {
    if (index === undefined || index === null) {
      this._hardwares.push(val)
    } else {
      this._hardwares.splice(index, 0, val)
    }

    const changed = await this.writeHardwareCache()
    if (changed) {
      this.emit(IHardwares.OnHardwareList)
    }

    return changed
  }

  @method()
  async modifyHardware(index: number, val: Partial<HardwareInfo>) {
    if (this._hardwares.length <= index) {
      return false
    }
    const item = this._hardwares[index]
    if (val.name !== undefined) item.name = val.name
    if (val.inter !== undefined) item.inter = val.inter
    if (val.scan_rate !== undefined) item.scan_rate = val.scan_rate
    if (val.inst_addr !== undefined) item.inst_addr = val.inst_addr

    const changed = await this.writeHardwareCache()
    if (changed) {
      this.emit(IHardwares.OnHardwareList)
    }

    return changed
  }

  @method()
  async loadHardware<T>(name: string): Promise<T | undefined> {
    const hardware = this._hardwares.find(hw => hw.name === name) as HardwareInfo<T>
    if (!hardware) {
      return undefined
    }
    const cfg = await this.loadCfg<T>(hardware)
    hardware.info = cfg as T
    return cfg as T
  }

  @method()
  async saveHardware<T extends Record<string, any>>(name: string, info: T): Promise<boolean> {
    const hardware = this._hardwares.find(hw => hw.name === name)
    if (!hardware) {
      return false
    }
    const changed = await this.saveCfg(hardware, info)
    if (changed) {
      this.emit(IHardwares.OnHardwareChanged)
    }
    return changed
  }

  @method()
  async loadCardCfg<T>(name: string, card_file: string): Promise<T | undefined> {
    const hardware = this._hardwares.find(hw => hw.name === name)

    if (!hardware?.info) {
      return undefined
    }

    const cards = hardware.info?.cards || []
    const card = cards.find((info: any) => info.file_name === card_file)

    if (!card) {
      return undefined
    }

    let result = card.cfg as T
    if (!result) {
      const card_type = card.card_type as VxiCardType
      switch (card_type) {
        case vxiCardType.ARINC_429:
          result = (await this.loadArinc429Cfg(card, card_file)) as T
          break
        default:
          return undefined
      }
    }
    return result
  }

  @method()
  async saveCardCfg<T>(name: string, card_file: string, info: T): Promise<boolean> {
    const hardware = this._hardwares.find(hw => hw.name === name)
    if (!hardware?.info) {
      return false
    }
    const cards = hardware.info?.cards
    const card = cards?.find((info: any) => info.file_name === card_file)
    if (!card) {
      return false
    }
    let changed = false
    const card_type = card.card_type.toUpperCase() as VxiCardType
    switch (card_type) {
      case vxiCardType.ARINC_429:
        changed = await this.saveArinc429Cfg(card_file, info)
        break
      default:
        break
    }
    if (changed) {
      this.emit(IHardwares.OnHardwareChanged)
    }
    return changed
  }

  private get engineName() {
    return this.engine?.engineName || 'vxi'
  }

  private async loadHardwares(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }
    const { hardware_list: list = [] } = (await this.cfg.read(cust.Cust, undefined, force)) || {}

    this._hardwares = list.map(val => {
      const { name, id, inter, printer_type, scan_rate, inst_addr } = val
      const item = {
        name,
        type: id as HardwareType,
        inter,
        scan_rate,
        inst_addr,
        info: undefined
      } as HardwareInfo

      if (id === hardwareTypes.PRINTER) {
        item.info = {
          name,
          printer_type
        }
      }
      return item
    })
    this.emit(IHardwares.OnHardwareList)
  }

  private async writeHardwareCache() {
    const hardware_list: cust.Hardware[] = this._hardwares.map(val => {
      let printer_type: string | undefined = undefined
      if (val.type === hardwareTypes.PRINTER) {
        const data = val.info as PrinterInfo
        printer_type = data?.printer_type
      }
      return new cust.Hardware({
        ...val,
        id: val.type,
        printer_type
      })
    })
    let changed = this.cfg.assign(cust.Cust, { hardware_list })
    changed = changed && (await this.cfg.write(cust.Cust))

    return changed
  }

  private async loadCfg<T>(hardware: HardwareInfo<T>) {
    switch (hardware.type) {
      case 'VXI':
        return await this.loadVxiCard(hardware.name)
      case 'DSA3000':
      case 'DTS3000':
      case 'PSI9000':
      case 'MGCPlus':
      case 'QUANTUMX':
      case 'OPC':
      case 'NAIU':
      case 'PRINTER':
        return hardware.info as T
      case 'PLOTTER':
      case 'TRUTEMP':
      case 'EX1048':
      case 'PBS':
      case 'PI7200':
      case 'iServer':
      case 'CUSTOM':
      case 'MPI322':
      case 'MPI320':
      case 'ASCIIDriver':
      default:
        return undefined
    }
  }

  private async saveCfg<T>(hardware: HardwareInfo<T>, info: T): Promise<boolean> {
    switch (hardware.type) {
      case 'VXI':
        return await this.saveVxiCard(hardware as HardwareInfo<VxiInfo>, info as VxiInfo)
      case 'PRINTER':
        ;(hardware as HardwareInfo<PrinterInfo>).info = info as PrinterInfo
        return await this.writeHardwareCache()
      case 'DSA3000':
      case 'DTS3000':
      case 'PSI9000':
      case 'MGCPlus':
      case 'QUANTUMX':
      case 'OPC':
      case 'NAIU':
      case 'PLOTTER':
      case 'TRUTEMP':
      case 'EX1048':
      case 'PBS':
      case 'PI7200':
      case 'iServer':
      case 'CUSTOM':
      case 'MPI322':
      case 'MPI320':
      case 'ASCIIDriver':
      default:
        return false
    }
  }

  private async loadVxiCard(fileName: string) {
    const { cards = [] } =
      (await this.cfg.read(vixcards.VxiCards, this.engineName, false, `/${fileName}.hw.vxi`)) || {}
    const result: VxiCard[] = []
    for (const card of cards) {
      const card_type = card.card_type.toUpperCase() as VxiCardType
      switch (card_type) {
        case vxiCardType.ARINC_429:
          await this.loadArinc429Cfg(card, card.file_name)
          result.push(card)
          break
        default:
          break
      }
    }
    return { cards: result }
  }

  private async saveVxiCard(hardware: HardwareInfo<VxiInfo>, info: VxiInfo): Promise<boolean> {
    hardware.info = { ...info }

    info.cards = info.cards.map(card => {
      return new vixcards.VxiCard(card)
    })

    const fileName = `/${hardware.name}.hw.vxi`
    let changed = this.cfg.assign(vixcards.VxiCards, info, fileName, this.engineName)

    changed = changed && (await this.cfg.write(vixcards.VxiCards, this.engineName, fileName))

    return changed
  }

  private async loadArinc429Cfg(card: VxiCard, fileName: string) {
    const { name, clock_tic, nips, device_host, index, slot, address, transmitters, receivers } =
      (await this.cfg.read(arinc429.Arinc429Cfg, this.engineName, false, fileName)) || {}

    const cfg = {
      name,
      clock_tic,
      nips,
      device_host,
      index,
      slot,
      address,
      transmitters,
      receivers
    } as ArincCardCfg

    if (cfg) {
      card.card_address = cfg.address.toString()
      card.card_slot = cfg.slot
      card.cfg = cfg
    }

    return cfg
  }

  private async saveArinc429Cfg<T>(card_file: string, info: T) {
    let changed = this.cfg.assign(
      arinc429.Arinc429Cfg,
      { ...(info as ArincCardCfg) },
      card_file,
      this.engineName
    )
    changed = changed && (await this.cfg.write(arinc429.Arinc429Cfg, this.engineName, card_file))
    return changed
  }

  private async loadOptions(force?: boolean) {
    await this.loadHardwares(force)
  }

  private async clearOptions() {
    this.cfg.remove(cust.Cust, '', this.engineName)
  }
}

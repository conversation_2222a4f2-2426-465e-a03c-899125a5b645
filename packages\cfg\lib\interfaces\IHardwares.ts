import { BaseItr, BaseMtr, IBase } from './IBase'
import { MapProto } from '@wuk/wkp'

import { vixcards, arinc429 } from '../proto'
import { PickField } from '../base'
import { HardwareType } from './type'

export type VxiCard<T extends Record<string, any> = Record<string, any>> = PickField<
  vixcards.VxiCard,
  MapProto<vixcards.VxiCard>
> & {
  card_type: string
  card_slot?: number
  cfg?: T
}

export type ArincSignal = PickField<arinc429.ArincSignal, MapProto<arinc429.ArincSignal>>
export type ArincCardCfg = {
  name: string
  clock_tic: number
  nips: number
  index: number
  slot: number
  address: number
  transmitters: ArincSignal[]
  receivers: ArincSignal[]
}

export interface PrinterInfo {
  name: string
  printer_type: string
}

export interface VxiInfo<T extends Record<string, any> = Record<string, any>> {
  cards: VxiCard<T>[]
}

export interface HardwareInfo<T = Record<string, any>> {
  name: string
  type: HardwareType
  inter: string
  scan_rate: number
  inst_addr: number

  info?: T
}

export abstract class IHardwares extends IBase<IHardwares> {
  // Hardware list
  abstract readHardwares(): Promise<Array<HardwareInfo>>
  abstract removeHardware(index: number): Promise<boolean>
  abstract addHardware(val: HardwareInfo, index?: number): Promise<boolean>
  abstract modifyHardware(index: number, val: Partial<HardwareInfo>): Promise<boolean>

  abstract loadHardware<T>(name: string): Promise<T | undefined>
  abstract saveHardware<T extends Record<string, any>>(name: string, info: T): Promise<boolean>

  abstract loadCardCfg<T>(name: string, card_file: string): Promise<T | undefined>
  abstract saveCardCfg<T>(name: string, card_file: string, info: T): Promise<boolean>

  static override get NAME() {
    return 'Hardwares'
  }

  static get OnHardwareList() {
    return 'Hardwares.OnHardwareList'
  }

  static get OnHardwareChanged() {
    return 'Hardwares.OnHardwareChanged'
  }
}

export type HardwaresItr = BaseItr<IHardwares>
export type HardwaresMtr = BaseMtr<IHardwares>

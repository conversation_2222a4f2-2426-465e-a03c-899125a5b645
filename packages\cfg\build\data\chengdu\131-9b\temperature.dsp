$DISPLAY "Temperature" background=NewBlue editres=MEDRES movegrid=5 resizegrid=5 showgrid=0 gridcolor=Cyan
$STOREDISPLAY Overwrite
$OBJECT Text font=1 startx=30 starty=10 endx=1160 endy=50
Black direction=1 alignment=1 fontweight=500 fontsize=20
"Temperature Averaging Control"
$END-OBJECT
***********
$OBJECT Box font=1 startx=30 starty=50 endx=1160 endy=295
style=1
linewidth=4
linecolor=ShallowBlue
boxcolor=DarkGray
shading=26
$END-OBJECT
***********
$OBJECT Text font=1 startx=30 starty=55 endx=1160 endy=101
Black direction=1 alignment=1 fontweight=500 fontsize=22
"Compressor Inlet Temperature (Deg F)"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=55 starty=95 endx=1160 endy=154
labelspace=2 unitspace=5 spacing=61 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=22 fontweight=400 labelfontsize=15 labelfontweight=400
CalibCIT1 type="FLOAT" width=6 prec=0 label="1" units="" 
CalibCIT2 type="FLOAT" width=6 prec=0 label="2" units=""
CalibCIT3 type="FLOAT" width=6 prec=0 label="3" units="" 
CalibCIT4 type="FLOAT" width=6 prec=0 label="4" units="" 
CalibCIT5 type="FLOAT" width=6 prec=0 label="5" units=""
CalibCIT6 type="FLOAT" width=6 prec=0 label="6" units="" 
CalibCIT7 type="FLOAT" width=6 prec=0 label="7" units="" 
CalibCIT8 type="FLOAT" width=6 prec=0 label="8" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=55 starty=170 endx=595 endy=228
labelspace=2 unitspace=5 spacing=61 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=22 fontweight=400 labelfontsize=15 labelfontweight=400
CalibCIT9 type="FLOAT" width=6 prec=0 label="9" units="" 
CalibCIT10 type="FLOAT" width=6 prec=0 label="10" units=""
CalibCIT11 type="FLOAT" width=6 prec=0 label="11" units="" 
CalibCIT12 type="FLOAT" width=6 prec=0 label="12" units=""
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=120 starty=92 endx=138 endy=110    
CITSwitch1 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=120 starty=92 endx=138 endy=110   
ACTION CITSwitch1 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch1 = 0 
CITSwitch1: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch1: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=262 starty=92 endx=280 endy=110    
CITSwitch2 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=262 starty=92 endx=280 endy=110  
ACTION CITSwitch2 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch2 = 0 
CITSwitch2: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch2: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=404 starty=92 endx=422 endy=110      
CITSwitch3 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=404 starty=92 endx=422 endy=110   
ACTION CITSwitch3 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch3 = 0 
CITSwitch3: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch3: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=546 starty=92 endx=564 endy=110     
CITSwitch4 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=546 starty=92 endx=564 endy=110
ACTION CITSwitch4 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch4 = 0 
CITSwitch4: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch4: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=688 starty=92 endx=706 endy=110      
CITSwitch5 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=688 starty=92 endx=706 endy=110      
ACTION CITSwitch5 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch5 = 0 
CITSwitch5: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch5: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=830 starty=92 endx=848 endy=110      
CITSwitch6 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=830 starty=92 endx=848 endy=110   
ACTION CITSwitch6 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch6 = 0 
CITSwitch6: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch6: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=972 starty=92 endx=990 endy=110    
CITSwitch7 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=972 starty=92 endx=990 endy=110  
ACTION CITSwitch7 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch7 = 0 
CITSwitch7: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch7: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=1114 starty=92 endx=1132 endy=110      
CITSwitch8 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=1114 starty=92 endx=1132 endy=110     
ACTION CITSwitch8 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch8 = 0 
CITSwitch8: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch8: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=120 starty=167 endx=138 endy=185    
CITSwitch9 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch startx=120 starty=167 endx=138 endy=185   
ACTION CITSwitch9 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch9 = 0 
CITSwitch9: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch9: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=262 starty=167 endx=280 endy=185    
CITSwitch10 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=262 starty=167 endx=280 endy=185
ACTION CITSwitch10 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch10 = 0 
CITSwitch10: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch10: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=404 starty=167 endx=422 endy=185      
CITSwitch11 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=404 starty=167 endx=422 endy=185
ACTION CITSwitch11 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch11 = 0 
CITSwitch11: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch11: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=546 starty=167 endx=564 endy=185     
CITSwitch12 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=546 starty=167 endx=564 endy=185
ACTION CITSwitch12 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if CITSwitch12 = 0 
CITSwitch12: 1
#elseif opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12) > 3
CITSwitch12: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT Digital font=20 startx=210 starty=243 endx=404 endy=281
labelspace=7 unitspace=0 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
CITOpenNum type="FLOAT" width=6 prec=0 label="CITs averaged" units="" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=484 starty=242 endx=660 endy=282
labelspace=6 unitspace=0 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
CITDev type="FLOAT" width=6 prec=1 label="CIT Spread" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=744 starty=244 endx=920 endy=284
labelspace=6 unitspace=0 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
CITAvg type="FLOAT" width=6 prec=1 label="Average CIT" units=""
$END-OBJECT
***********
$OBJECT Box font=1 startx=30 starty=320 endx=1160 endy=490
style=1
linewidth=4
linecolor=ShallowBlue
boxcolor=DarkGray
shading=26
$END-OBJECT
***********
$OBJECT Text font=1 startx=30 starty=325 endx=1160 endy=358
Black direction=1 alignment=1 fontweight=500 fontsize=22
"Bleed Air Temperature (Deg F)"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=340 starty=361 endx=877 endy=419
labelspace=2 unitspace=5 spacing=61 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=22 fontweight=400 labelfontsize=15 labelfontweight=400
CalibTB1 type="FLOAT" width=6 prec=0 label="1" units="" 
CalibTB2 type="FLOAT" width=6 prec=0 label="2" units=""
CalibTB3 type="FLOAT" width=6 prec=0 label="3" units="" 
CalibTB4 type="FLOAT" width=6 prec=0 label="4" units="" 
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=405 starty=358 endx=423 endy=376    
BTSwitch1 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch startx=405 starty=358 endx=423 endy=376  
ACTION BTSwitch1 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if BTSwitch1 = 0 
BTSwitch1: 1
#elseif opennumber(BTSwitch1 BTSwitch2 BTSwitch3 BTSwitch4) > 1
BTSwitch1: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=547 starty=358 endx=565 endy=376    
BTSwitch2 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch startx=547 starty=358 endx=565 endy=376    
ACTION BTSwitch2 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if BTSwitch2 = 0 
BTSwitch2: 1
#elseif opennumber(BTSwitch1 BTSwitch2 BTSwitch3 BTSwitch4) > 1
BTSwitch2: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=689 starty=358 endx=707 endy=376    
BTSwitch3 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch startx=689 starty=358 endx=707 endy=376  
ACTION BTSwitch3 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if BTSwitch3 = 0 
BTSwitch3: 1
#elseif opennumber(BTSwitch1 BTSwitch2 BTSwitch3 BTSwitch4) > 1
BTSwitch3: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=831 starty=358 endx=849 endy=376     
BTSwitch4 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch startx=831 starty=358 endx=849 endy=376     
ACTION BTSwitch4 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if BTSwitch4 = 0 
BTSwitch4: 1
#elseif opennumber(BTSwitch1 BTSwitch2 BTSwitch3 BTSwitch4) > 1
BTSwitch4: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT Digital font=20 startx=200 starty=444 endx=404 endy=480
labelspace=8 unitspace=0 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
TBOpenNum type="FLOAT" width=6 prec=0 label="Bleeds averaged" units="" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=490 starty=444 endx=681 endy=480
labelspace=7 unitspace=0 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
TBDev type="FLOAT" width=6 prec=1 label="Bleed Spread" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=750 starty=444 endx=937 endy=480
labelspace=7 unitspace=0 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
TBAvg type="FLOAT" width=6 prec=1 label="Average Bleed" units=""
$END-OBJECT
***********
$OBJECT Box font=1 startx=30 starty=510 endx=1160 endy=600
style=1
linewidth=4
linecolor=ShallowBlue
boxcolor=DarkGray
shading=26
$END-OBJECT
***********
$OBJECT Text font=1 startx=30 starty=515 endx=1160 endy=550
Black direction=1 alignment=1 fontweight=500 fontsize=22
"Eng EGT Temperature (Deg F)"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=108 starty=558 endx=262 endy=600
labelspace=4 unitspace=1 spacing=40 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
CalibEgtEng1 type="FLOAT" width=6 prec=0 label="Unit 1" units="" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=375 starty=558 endx=533 endy=600
labelspace=4 unitspace=1 spacing=40 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
CalibEgtEng2 type="FLOAT" width=6 prec=0 label="Unit 2" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=630 starty=558 endx=778 endy=600
labelspace=4 unitspace=1 spacing=40 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
EGTEngDev type="FLOAT" width=6 prec=0 label="Unit 3" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=890 starty=558 endx=1058 endy=593
labelspace=4 unitspace=1 spacing=40 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
EGTEngAvg type="FLOAT" width=6 prec=0 label="Unit 4" units=""
$END-OBJECT
***********
$OBJECT Box font=1 startx=30 starty=620 endx=1160 endy=880
style=1
linewidth=4
linecolor=ShallowBlue
boxcolor=DarkGray
shading=26
$END-OBJECT
***********
$OBJECT Text font=1 startx=30 starty=625 endx=1160 endy=658
Black direction=1 alignment=1 fontweight=500 fontsize=22
"EGT (Deg F)"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=55 starty=665 endx=1178 endy=723
labelspace=2 unitspace=5 spacing=61 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=22 fontweight=400 labelfontsize=15 labelfontweight=400
CalibEGT1 type="FLOAT" width=6 prec=0 label="1" units="" 
CalibEGT2 type="FLOAT" width=6 prec=0 label="2" units=""
CalibEGT3 type="FLOAT" width=6 prec=0 label="3" units="" 
CalibEGT4 type="FLOAT" width=6 prec=0 label="4" units="" 
CalibEGT5 type="FLOAT" width=6 prec=0 label="5" units=""
CalibEGT6 type="FLOAT" width=6 prec=0 label="6" units="" 
CalibEGT7 type="FLOAT" width=6 prec=0 label="7" units="" 
CalibEGT8 type="FLOAT" width=6 prec=0 label="8" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=55 starty=740 endx=762 endy=795
labelspace=2 unitspace=5 spacing=61 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=1 fontsize=22 fontweight=400 labelfontsize=15 labelfontweight=400
CalibEGT9 type="FLOAT" width=6 prec=0 label="9" units="" 
CalibEGT10 type="FLOAT" width=6 prec=0 label="10" units=""
CalibEGT11 type="FLOAT" width=6 prec=0 label="11" units="" 
CalibEGT12 type="FLOAT" width=6 prec=0 label="12" units="" 
CalibEGT13 type="FLOAT" width=6 prec=0 label="13" units=""
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=120 starty=662 endx=138 endy=680    
EGTSwitch1 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=120 starty=662 endx=138 endy=680 
ACTION EGTSwitch1 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch1 = 0 
EGTSwitch1: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch1: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=262 starty=662 endx=280 endy=680    
EGTSwitch2 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22  startx=262 starty=662 endx=280 endy=680    
ACTION EGTSwitch2 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch2 = 0 
EGTSwitch2: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch2: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=404 starty=662 endx=422 endy=680    
EGTSwitch3 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch startx=404 starty=662 endx=422 endy=680   
ACTION EGTSwitch3 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch3 = 0 
EGTSwitch3: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch3: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=546 starty=662 endx=564 endy=680    
EGTSwitch4 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=546 starty=662 endx=564 endy=680  
ACTION EGTSwitch4 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch4 = 0 
EGTSwitch4: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch4: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=688 starty=662 endx=706 endy=680    
EGTSwitch5 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=688 starty=662 endx=706 endy=680    
ACTION EGTSwitch5 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch5 = 0 
EGTSwitch5: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch5: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=830 starty=662 endx=848 endy=680    
EGTSwitch6 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=830 starty=662 endx=848 endy=680  
ACTION EGTSwitch6 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch6 = 0 
EGTSwitch6: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch6: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=972 starty=662 endx=990 endy=680    
EGTSwitch7 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=972 starty=662 endx=990 endy=680 
ACTION EGTSwitch7 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch7 = 0 
EGTSwitch7: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch7: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=1114 starty=662 endx=1132 endy=680    
EGTSwitch8 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=1114 starty=662 endx=1132 endy=680    
ACTION EGTSwitch8 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch8 = 0 
EGTSwitch8: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch8: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=120 starty=737 endx=138 endy=755    
EGTSwitch9 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=120 starty=737 endx=138 endy=755    
ACTION EGTSwitch9 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch9 = 0 
EGTSwitch9: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch9: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=262 starty=737 endx=280 endy=755    
EGTSwitch10 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=262 starty=737 endx=280 endy=755    
ACTION EGTSwitch10 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch10 = 0 
EGTSwitch10: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch10: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=404 starty=737 endx=422 endy=755    
EGTSwitch11 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=404 starty=737 endx=422 endy=755  
ACTION EGTSwitch11 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch11 = 0 
EGTSwitch11: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch11: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=546 starty=737 endx=564 endy=755    
EGTSwitch12 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=546 starty=737 endx=564 endy=755    
ACTION EGTSwitch12 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch12 = 0 
EGTSwitch12: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch12: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT StatusIndicator font=1 startx=688 starty=737 endx=706 endy=755    
EGTSwitch13 textcolor=Black radius="20" type="LIGHT"
Red = 0 ""
Green = 1 ""
$END-OBJECT
***********
$OBJECT Switch font=22 startx=688 starty=737 endx=706 endy=755    
ACTION EGTSwitch13 onlabel=" " offlabel=" " offcolor=Clear oncolor=Clear labelcolor=White fontsize=15 hideboxshadow=1
$RELEASE
#if EGTSwitch13 = 0 
EGTSwitch13: 1
#elseif opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13) > 5
EGTSwitch13: 0
#endif
$END-RELEASE
$END-OBJECT
***********
$OBJECT Digital font=20 startx=200 starty=830 endx=422 endy=870
labelspace=8 unitspace=0 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
EGTOpenNum type="FLOAT" width=6 prec=0 label="EGTs averaged" units="" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=490 starty=830 endx=675 endy=870
labelspace=6 unitspace=0 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
EGTDev type="FLOAT" width=6 prec=1 label="EGT Spread" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=750 starty=830 endx=948 endy=870
labelspace=7 unitspace=0 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=4 \
direction=0 fontsize=22 fontweight=400 labelfontsize=14 labelfontweight=400
EGTAvg type="FLOAT" width=6 prec=1 label="Average EGT" units=""
$END-OBJECT
***********
$OBJECT FuncButton font=66 startx=1090 starty=910 endx=1190 endy=955
type="CLOSECRT" label="Close" crtname="SECOND CRT" backgroundcolor=DarkGray textcolor=White  fontsize=24 fontweight=400
$END-OBJECT
***********
$END-DISPLAY


# IDisplay 接口文档

## 相关文档
- [类型定义](type.md) - 包含 `CrtResolutionType` 等类型定义
- [IBase](IBase.md) - 基础接口

## 概述
`IDisplay` 是一个接口，用于定义显示相关的类型和配置。它提供了各种显示对象的类型定义，包括基础对象、文本、数字、条形图、指示器、仪表盘、开关、按钮等。

## 类型定义

### 基础对象
```typescript
export interface UObject {
  left: number
  top: number
  width: number
  height: number
  font: number
  flag_id: string
  obj_type: string
}
```

### 框对象
```typescript
export interface UBox extends UObject {
  style: number
  box_color: string
  shading: number
  line_width: number
  line_color: string
}
```

### 文本对象
```typescript
export interface UText extends UObject {
  direction: number
  alignment: number
  color: string
  value: string
}
```

### 线条对象
```typescript
export interface ULine extends UObject {
  line_width: number
  line_color: string
  shading: number
}
```

### 字符串项
```typescript
export interface StringItem {
  color: string
  op: string
  value: string
  text: string
}
```

### 字符串对象
```typescript
export interface UString extends UObject {
  param_id: string
  label_space: number
  label: string
  label_color: string
  string_color: string
  param_box: number
  param_box_color: string
  shading: number
  item_vec: Array<StringItem>
}
```

### 数字项
```typescript
export interface DigitalItem {
  param_id: string
  type: string
  width: number
  prec: number
  label: string
  units: string
}
```

### 数字对象
```typescript
export interface UDigital extends UObject {
  label_space: number
  unit_space: number
  spacing: number
  param_box: number
  param_box_color: string
  label_color: string
  shading: number
  item_vec: Array<DigitalItem>
}
```

### 条形图项
```typescript
export interface BarItem {
  param_id: string
  target: string
  corr: string
  label: string
  units: string
  min: number
  max: number
  tic_inter: number
  label_inter: number
}
```

### 条形图对象
```typescript
export interface UBar extends UObject {
  label_space: number
  unit_space: number
  label_color: string
  length: number
  height: number
  format: string
  tic_pos: string
  dir: number
  shading: number
  spacing: number
  tic_font: number
  param_box: number
  param_box_color: string
  limit_width: number
  item_vec: Array<BarItem>
}
```

### 指示器项
```typescript
export interface IndicatorItem {
  color: string
  op: string
  test_value_id: string
  test_string: string
}
```

### 指示器对象
```typescript
export interface UIndicator extends UObject {
  param_id: string
  text_color: string
  item_vec: Array<IndicatorItem>
}
```

### 仪表盘对象
```typescript
export interface UGauge extends UObject {
  param_id: string
  gauge_type: string
  peak_param_id: string
  label_color: string
  needle_color: string
  nums_out_side: number
  tic_font: number
  format: string
  max: number
  min: number
  label: string
  units: string
  label_inter: number
  tic_inter: number
  radius: number
  param_box: number
  param_box_color: string
  shading: number
}
```

### 校准消息
```typescript
export interface CalMsg {
  uuid: string
  line_num: number
  path: string
}
```

### 开关对象
```typescript
export interface USwitch extends UObject {
  param_id: string
  type: string
  on_label: string
  off_label: string
  off_color: string
  on_color: string
  has_release: boolean
  release_msg: CalMsg
  release_vec: Array<string>
  has_push: boolean
  push_msg: CalMsg
  push_vec: Array<string>
}
```

### 按钮对象
```typescript
export interface UButton extends UObject {
  type: string
  param_id: string
  set_value: number
  on_label: string
  off_label: string
  label_color: string
  on_color: string
  off_color: string
}
```

### ECM 按钮面板
```typescript
export interface ECMButtonPanel {
  style: number
  line_width: number
  line_color: string
  box_color: string
  shading: number
  label: string
}
```

### ECM 按钮状态
```typescript
export interface ECMButtonState {
  state: number
  label: string
  on_color: string
  has_wait: boolean
  wait_msg: CalMsg
  wait_vec: Array<string>
  has_lock: boolean
  lock_msg: CalMsg
  lock_vec: Array<string>
  has_init: boolean
  init_msg: CalMsg
  init_cal_vec: Array<string>
  has_main: boolean
  main_msg: CalMsg
  main_cal_vec: Array<string>
  has_final: boolean
  final_msg: CalMsg
  final_cal_vec: Array<string>
}
```

### ECM 按钮对象
```typescript
export interface UECMButton extends UObject {
  param_id: string
  wait_param_id: string
  type: string
  label_color: string
  warn_color: string
  off_color: string
  default_state: number
  has_panel: boolean
  panel: ECMButtonPanel
  state_vec: Array<ECMButtonState>
}
```

### X 轴
```typescript
export interface XAxis {
  param_id: string
  left: string
  width: string
  tic_interval: string
  y_intercept: string
  axis_color: string
  label_interval: string
  tic_position: string
  label_position: string
}
```

### 参考 X 轴
```typescript
export interface ReferenceX {
  y_intercept: number
  tic_position: string
  label_position: string
}
```

### 表格绘图
```typescript
export interface TablePlot {
  color: string
  table_name: string
}
```

### Y 轴
```typescript
export interface Yaxis {
  param_id: string
  top: string
  height: string
  tic_interval: string
  x_intercept: string
  axis_color: string
  label_interval: string
  grid: string
  tic_position: string
  label_position: string
  bottom_to_x_axis: string
  top_to_x_axis: string
  color_vec: Array<string>
  table_plot_vec: Array<TablePlot>
}
```

### 硬拷贝绘图项
```typescript
export interface HardCopyPlotItem {
  position_x: number
  position_y: number
  text_string: string
}
```

### 硬拷贝绘图
```typescript
export interface HardCopyPlot {
  pen_number: number
  direction: number
  plot_item_vec: Array<HardCopyPlotItem>
}
```

### 绘图对象
```typescript
export interface UPlot extends UObject {
  plot_key: string
  type: string
  points: Array<string>
  spot_param_id: string
  line_width: number
  has_x_axis: boolean
  x_axis: XAxis
  has_reference_x: boolean
  reference_x: ReferenceX
  yaxis_vec: Array<Yaxis>
  has_hard_copy_plot: boolean
  hard_copy_plot: HardCopyPlot
}
```

### 显示对象
```typescript
export interface UDisplay {
  name: string
  description: string
  background: string
  editres: CrtResolutionType
  move_grid: number
  resize_grid: number
  show_grid: number
  grid_color: string
  obj_vec: Array<UObject>
}
``` 
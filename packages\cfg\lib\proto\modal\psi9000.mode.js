/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let EuconvValue={$keys:["type","name"],type:types.int32,name:types.string},PsiEngine={$keys:["name","calib_mode","alias"],name:types.string,calib_mode:types.int32,alias:types.string},PsiSignal={$keys:["name","channel","calib_mode","engines"],name:types.string,channel:types.int32,calib_mode:types.int32,engines:types.arrayOf(PsiEngine)},Psi9000Cfg={$MAX:9e3,$MIN:2e3,$keys:["name","alia","over_sample","trigger","trig_divisor","euconv","euconv","zero_relay","signals"],name:types.string,alia:types.string,over_sample:types.int32,trigger:types.string,trig_divisor:types.int32,euconv:EuconvValue,zero_relay:types.string,signals:types.arrayOf(PsiSignal)};export{EuconvValue,PsiEngine,PsiSignal,Psi9000Cfg};
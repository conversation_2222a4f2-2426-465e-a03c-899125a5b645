import { PageConfig, IPage, ISdk, SdkConfig, IBaseProxy, IBase } from '../interfaces'
import { Root } from '../base'
import Bridge from './Bridge'
import Page from './Page'

export default class Sdk extends Root implements ISdk {
  private static _instance: Sdk | null = null
  private _bridge: Bridge
  private _pages: Map<string, IPage<IBaseProxy>>
  private _master: boolean

  constructor() {
    super(ISdk.NAME)

    this._pages = new Map<string, IPage<IBaseProxy>>()
    this._modules = [(this._bridge = new Bridge())]
    this._master = false
  }

  override async stop() {
    this._pages.forEach(async it => await it.stop())
    await super.stop()
    this._bridge.stop()
  }

  override async destroy() {
    this._modules = []
    this._pages.forEach(async it => await it.destroy())
    await this._bridge.destroy()
    await super.destroy()
  }

  override async start() {
    await this._bridge.start()
    this._pages.forEach(async it => await it.start())
    await super.start()
  }

  has(id: string): boolean {
    return this._pages.has(id)
  }

  get proxy() {
    return this._bridge.target
  }

  createSdk<T extends IBase, K>({ name }: SdkConfig, proxy: T): ISdk<T, K> {
    this._master = true
    this._bridge.create(name, proxy, this._master)

    return this as ISdk<T, K>
  }

  createPage<T extends IBase, K>(config: PageConfig, proxy: T): IPage<T, K> {
    const id = config.id
    let result = this._pages.get(id)
    if (!result) {
      const { name } = config
      result = new Page(config, this._bridge.create(name, proxy))
      this._pages.set(id, result)
    }
    return result as IPage<T, K>
  }

  override get valid(): boolean {
    return true
  }

  static get impl() {
    return Sdk._instance || (Sdk._instance = new Sdk())
  }

  static createSdk<T extends IBase, K>(config: SdkConfig, proxy: T): ISdk<T, K> {
    return Sdk.impl.createSdk<T, K>(config, proxy)
  }

  static createPage<T extends IBase, K>(config: PageConfig, proxy: T): IPage<T, K> {
    return Sdk.impl.createPage<T, K>(config, proxy)
  }
}

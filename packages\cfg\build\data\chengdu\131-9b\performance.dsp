$DISPLAY "Performance" background=NewGray editres=1280:448 movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$STOREDISPLAY Overwrite
***********
$OBJECT Box font=1 startx=5 starty=55 endx=766 endy=385
style=3
linewidth=2 linecolor=Blue
boxcolor=NewGray
shading=0
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=5 starty=5 endx=150 endy=40
CorrectAdapter_Stat textcolor=White  fontsize=12 fontweight=500 type="LIMIT" label="CIT Over Table Range"
$END-OBJECT
***********
$OBJECT Line font=21 startx=132 starty=55 endx=134 endy=385
linewidth=1 linecolor=Blue
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=146 starty=65 endx=241 endy=388
labelspace=6 unitspace=0 direction=1 spacing=18 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
fontsize=24 fontweight=500 labelfontsize=20 labelfontweight=500
WB type="DEFAULT" width=6 prec=2 label="Wb" units=""
DelWb type="DEFAULT" width=6 prec=2 label="" units=""
WbCor type="DEFAULT" width=6 prec=2 label="" units=""
WbSpe type="DEFAULT" width=6 prec=2 label="" units=""
WbMargin type="DEFAULT" width=6 prec=2 label="" units=""
$END-OBJECT
***********
$OBJECT Line font=21 startx=5 starty=140 endx=768 endy=141
linewidth=1 linecolor=Blue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=255 starty=55 endx=256 endy=385
linewidth=1 linecolor=Blue
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=270 starty=65 endx=368 endy=388
labelspace=6 unitspace=0 direction=1 spacing=18 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
fontsize=24 fontweight=500 labelfontsize=20 labelfontweight=500
PB type="DEFAULT" width=6 prec=2 label="Pb" units=""
DelPb type="DEFAULT" width=6 prec=2 label="" units=""
PbCor type="DEFAULT" width=6 prec=2 label="" units=""
PbSpe type="DEFAULT" width=6 prec=2 label="" units=""
PbMargin type="DEFAULT" width=6 prec=2 label="" units=""
$END-OBJECT
***********
$OBJECT Line font=21 startx=378 starty=55 endx=379 endy=385
linewidth=1 linecolor=Blue
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=391 starty=65 endx=485 endy=388
labelspace=6 unitspace=0 direction=1 spacing=18 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
fontsize=24 fontweight=500 labelfontsize=20 labelfontweight=500
TB type="DEFAULT" width=6 prec=2 label="Tb" units=""
DelTb type="DEFAULT" width=6 prec=2 label="" units=""
TbCor type="DEFAULT" width=6 prec=2 label="" units=""
TbSpe type="DEFAULT" width=6 prec=2 label="" units=""
TbMargin type="DEFAULT" width=6 prec=2 label="" units=""
$END-OBJECT
***********
$OBJECT Line font=21 startx=501 starty=55 endx=502 endy=385
linewidth=1 linecolor=Blue
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=530 starty=65 endx=626 endy=388
labelspace=6 unitspace=0 direction=1 spacing=18 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
fontsize=24 fontweight=500 labelfontsize=20 labelfontweight=500
WF type="DEFAULT" width=6 prec=2 label="Wf" units=""
DelWf type="DEFAULT" width=6 prec=2 label="" units=""
WfCor type="DEFAULT" width=6 prec=2 label="" units=""
WfSpe type="DEFAULT" width=6 prec=2 label="" units=""
WfMargin type="DEFAULT" width=6 prec=2 label="" units=""
$END-OBJECT
***********
$OBJECT Line font=21 startx=643 starty=55 endx=644 endy=385
linewidth=1 linecolor=Blue
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=652 starty=65 endx=754 endy=388
labelspace=6 unitspace=0 direction=1 spacing=18 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
fontsize=24 fontweight=500 labelfontsize=20 labelfontweight=500
EGT type="DEFAULT" width=6 prec=2 label="EGT" units=""
DelEgt type="DEFAULT" width=6 prec=2 label="" units=""
EgtCor type="DEFAULT" width=6 prec=2 label="" units=""
EgtSpe type="DEFAULT" width=6 prec=2 label="" units=""
EgtMargin type="DEFAULT" width=6 prec=2 label="" units=""
$END-OBJECT
***********
$OBJECT Line font=21 startx=5 starty=204 endx=768 endy=205
linewidth=1 linecolor=Blue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=5 starty=265 endx=768 endy=266
linewidth=1 linecolor=Blue
shading=0
$END-OBJECT
***********
$OBJECT Line font=21 startx=5 starty=329 endx=768 endy=330
linewidth=1 linecolor=Blue
shading=0
$END-OBJECT
***********
$OBJECT Text font=22 startx=20 starty=90 endx=130 endy=115
Black direction=1 alignment=1 fontweight=500 fontsize=18
"Measured"
$END-OBJECT
***********
$OBJECT Text font=22 startx=5 starty=142 endx=130 endy=167
Black direction=1 alignment=1 fontweight=500 fontsize=18
"Correction\nFactor"
$END-OBJECT
***********
$OBJECT Text font=22 startx=5 starty=210 endx=130 endy=235
Black direction=1 alignment=1 fontweight=500 fontsize=18
"Final\nCorrected"
$END-OBJECT
***********
$OBJECT Text font=22 startx=5 starty=285 endx=130 endy=305
Black direction=1 alignment=1 fontweight=500 fontsize=18
"Specification"
$END-OBJECT
***********
$OBJECT Text font=22 startx=5 starty=345 endx=130 endy=365
Black direction=1 alignment=1 fontweight=500 fontsize=18
"Margin"
$END-OBJECT
***********
$OBJECT Box font=1 startx=775 starty=10 endx=1130 endy=440
style=3
linewidth=2 linecolor=Blue
boxcolor=NewGray
shading=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=788 starty=20 endx=884 endy=432
labelspace=6 unitspace=0 direction=1 spacing=5 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=26 fontweight=500 labelfontsize=12 labelfontweight=500
Wb22 type="DEFAULT" width=6 prec=2 label="Inlet\nTemperature" units=""
Wb23 type="DEFAULT" width=6 prec=2 label="ARNC T2" units=""
Wb24 type="DEFAULT" width=6 prec=2 label="Oil Temp" units=""
Wb25 type="DEFAULT" width=6 prec=2 label="Comp Dis Tmp 1" units=""
Wb26 type="DEFAULT" width=6 prec=2 label="Com Dis Tmp 2" units=""
Wb27 type="DEFAULT" width=6 prec=2 label="Orifice Temp" units=""
Wb28 type="DEFAULT" width=6 prec=2 label="Gear Box Temp" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=905 starty=31 endx=1000 endy=263
labelspace=6 unitspace=0 direction=1 spacing=5 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=26 fontweight=500 labelfontsize=12 labelfontweight=500
Wb22 type="DEFAULT" width=6 prec=2 label="Pcell" units=""
CalibPS9 type="DEFAULT" width=6 prec=2 label="PS9 Pressure" units=""
Wb24 type="DEFAULT" width=6 prec=2 label="Oil Pressure" units=""
Wb25 type="DEFAULT" width=6 prec=2 label="Comp Static Prs" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=903 starty=323 endx=996 endy=432
labelspace=6 unitspace=0 direction=1 spacing=5 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=26 fontweight=500 labelfontsize=12 labelfontweight=500
Wb27 type="DEFAULT" width=6 prec=2 label="Orifice Pressure" units=""
Wb28 type="DEFAULT" width=6 prec=2 label="Unit EGT" units=""
$END-OBJECT
***********
$OBJECT Digital font=20 startx=1019 starty=33 endx=1111 endy=432
labelspace=6 unitspace=0 direction=1 spacing=5 labelcolor=Black parambox=2 paramboxcolor=White shading=0 fontsize=26 fontweight=500 labelfontsize=12 labelfontweight=500
Wb22 type="DEFAULT" width=6 prec=2 label="Gearbox Vib" units=""
Wb23 type="DEFAULT" width=6 prec=2 label="Turbine Vib" units=""
Wb24 type="DEFAULT" width=6 prec=2 label="Spare" units=""
Wb25 type="DEFAULT" width=6 prec=2 label="IGV Position" units=""
Wb26 type="DEFAULT" width=6 prec=2 label="RPM" units=""
Wb27 type="DEFAULT" width=6 prec=2 label="Orifice Diff Press" units=""
Wb28 type="DEFAULT" width=6 prec=2 label="KW LOAD" units=""
$END-OBJECT
***********
$OBJECT FuncButton font=66 startx=1145 starty=310 endx=1250 endy=375
type="COLLECT" label="Log Data\nPoint" backgroundcolor=BrightBlue textcolor=DarkGray fontsize=18 fontweight=400
$END-OBJECT
***********
$OBJECT FuncButton font=22 startx=1145 starty=390 endx=1230 endy=440
type="CHANGEDSP" label="Close" backgroundcolor=BrightBlue textcolor=DarkGray fontsize=18 fontweight=400
crtname="Test CRT" quadindx=1 displayname="Main Second"
$END-OBJECT
***********
$OBJECT Buttonobj font=66 startx=1135 starty=30 endx=1240 endy=250
parameter=PerType type=RADIO
labelcolor=Black warncolor=Yellow offcolor=LightGray
$PANEL
style=1
boxcolor=DarkGray
shading=0
label=""
$END-PANEL
defaultstate=0
$STATE 0
label="ECS"
oncolor=Green
$END-STATE
$STATE 1
label="MES"
oncolor=Green
$END-STATE
$STATE 2
label="RTL"
oncolor=Green
$END-STATE
$END-OBJECT
***********
$END-DISPLAY

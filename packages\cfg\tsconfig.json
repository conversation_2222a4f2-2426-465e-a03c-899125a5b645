{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "baseUrl": ".",
    "outDir": "dist",
    // "declarationDir": "dist/types",
		"declaration": true,
		// "noUnusedLocals": true,
		// "noUnusedParameters": true,
    "removeComments": false,
    "stripInternal": false,
		"esModuleInterop": true,
    "paths": {
      "lib/*": [
        "./lib/*"
      ]
    },
  },
  "include": [
    "lib",
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}

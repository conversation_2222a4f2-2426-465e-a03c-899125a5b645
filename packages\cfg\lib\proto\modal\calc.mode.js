/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let Lineable={$keys:["comments","type","name","unit","value"],comments:types.arrayOf(types.string),type:types.int32,name:types.string,unit:types.string,value:types.string},Groupable={$keys:["comments","name","excute","lines"],comments:types.arrayOf(types.string),name:types.string,excute:types.int32,lines:types.arrayOf(Lineable)},Calc={$MAX:6e3,$MIN:100,$keys:["group"],group:Groupable};export{Lineable,Groupable,Calc};
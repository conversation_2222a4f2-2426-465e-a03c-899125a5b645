/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let RangeValue={$keys:["min","max","unit"],min:types.double,max:types.double,unit:types.string},VxiCalcLib={$keys:["type","min","max","data"],type:types.int32,min:types.double,max:types.double,data:types.arrayOf(types.double)},VxiEngine={$keys:["name","rate_group","signal_range","scale_range","progfilter","sensitivity","measwindow","calib_mode","calib","calib_data","alias","dfilter"],name:types.string,rate_group:types.string,signal_range:RangeValue,scale_range:RangeValue,progfilter:types.int32,sensitivity:types.int32,measwindow:types.double,calib_mode:types.int32,calib:types.int32,calib_data:VxiCalcLib,alias:types.string,dfilter:types.int32},VxiSignal={$keys:["name","channel","type","rate_group","signal_range","scale_range","progfilter","sensitivity","measwindow","dfilter","calib_mode","calib","calib_data","engines"],name:types.string,channel:types.int32,type:types.int32,rate_group:types.string,signal_range:RangeValue,scale_range:RangeValue,progfilter:types.int32,sensitivity:types.int32,measwindow:types.double,dfilter:types.int32,calib_mode:types.int32,calib:types.int32,calib_data:VxiCalcLib,engines:types.arrayOf(VxiEngine)},VixCfg={$MAX:8e3,$MIN:1e3,$keys:["name","rate_groups","scp","board_calcs_on","value1","value2","value3"],name:types.string,rate_groups:types.arrayOf(types.int32),scp:types.arrayOf(types.int32),board_calcs_on:types.boolean,value1:types.int32,value2:types.int32,value3:types.int32};export{RangeValue,VxiCalcLib,VxiEngine,VxiSignal,VixCfg};
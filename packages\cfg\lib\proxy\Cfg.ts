import { BrowserWindow } from 'electron'
import { BaseProxy, method } from './Base'
import { AppCustomer, CfgVersion, ErrorCode, ICfg, MessageInfo } from '../interfaces'
import { BaseProto, ProtoClass } from '@wuk/wkp'
import {
  Cmd,
  copyDir,
  copyLocalFile,
  existed,
  makeDir,
  Path,
  Rcs,
  removeDir,
  removeFile,
  rename,
  scanDir,
  writeFile
} from '../utils'
import { computeChecksum, extract, IFile, zip } from '@wuk/base/dist/node/zip'
import { CancellationToken } from '@wuk/base/dist/cancellation'
import { format } from 'date-fns'
import { entry, versions } from '../proto'

export type PartialT<T> = {
  [P in keyof T]?: T[P] extends BaseProto ? PartialT<T[P]> : T[P]
}

export interface CacheCfg<T extends Record<string, any> = Record<string, any>> {
  cfg: T
  changed?: boolean
  readed?: boolean
  cfg_name?: string
}

export class Cfg extends BaseProxy<ICfg> implements ICfg {
  private static kNameIndex = 0
  private static kMaxCount = 500

  private _customers: Map<string, AppCustomer>
  private _customer: string
  private _versions: Map<string, CfgVersion[]>
  private _version?: CfgVersion
  private _messages: Array<MessageInfo>

  private _caches: Map<string, CacheCfg>
  private _files: Map<string, string>

  constructor(win?: BrowserWindow, master = false) {
    super(ICfg.NAME, master, win)

    this._customers = new Map()
    this._customer = ''
    this._versions = new Map()

    this._messages = []
    this._caches = new Map()
    this._files = new Map()
  }

  override async init() {
    this.log('init====== begin')
    await this.loadOptions()
    await super.init()
    this.log('init====== end')
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  get customer() {
    return this._customer
  }

  get cfgDir() {
    return Path.dataDir
  }

  get customers() {
    return this._customers
  }

  get version() {
    return this._version
  }

  get versions() {
    return this._versions
  }

  async remove<T extends BaseProto<T>>(cls: ProtoClass<T>, fileName = '', engineName = '') {
    const uri = `${engineName}_${cls.uri}_${fileName}`
    this._caches.delete(uri)
  }

  async loadCustomer() {
    const result = this._customers.get(this._customer)

    this.log('loadCustomer======customer={%1} result={%2}', this._customer, result)
    return result
  }

  async loadCustomers() {
    const result = Array.from(this._customers.values())
    this.log('loadCustomers======result={%1}', result)

    return result
  }

  async loadVersion() {
    const result = this._version

    this.log('loadVersion======version={%1}', result)

    return result
  }

  async loadVersions() {
    const result = (this._customer && this._versions.get(this._customer)) || []
    this.log('loadVersions======customer={%1} versions={%2}', this._customer, result)

    return result
  }

  async changeCustomer(name: string): Promise<boolean> {
    const result = this.checkChangeCustomer(name)
    this.log('changeCustomer======name={%1} result={%2}', name, result)

    this.writeMessage(`Customer is changed to ${name}`)
    return result
  }

  async read<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    engineName = '',
    force?: boolean,
    fileName = ''
  ) {
    const cache = this.cache<T>(cls, fileName, engineName)
    let ret = undefined
    const readed = cache.readed
    const result = cache.cfg
    let file_path = ''
    if (!readed || force) {
      const {
        code,
        rsp,
        cfg_name,
        file_path: path
      } = await Cmd.read<T>(cls, this._customer, engineName, fileName)
      if (code === ErrorCode.OK && rsp) {
        cache.readed = true
        cache.cfg_name = cfg_name
        cache.changed && result.assign(rsp)
        file_path = path
        this.assign(cls, rsp, fileName, engineName)
      }
      ret = code
    }

    this.log(
      `Cfg====read customer={%1} engineName={%2} uri={%3} code={%4} readed={%5} force={%6} fileName={%7} cfg_name={%8}`,
      this._customer,
      engineName,
      cls.uri,
      ret,
      readed,
      force,
      fileName,
      cache.cfg_name
    )

    this.writeMessage(
      `Loading file:${file_path}${cache.cfg_name || ''} engineName=${engineName} uri=${
        cls.uri
      } code=${ret}`
    )

    return result
  }

  async readG<T extends BaseProto<T>>(cls: ProtoClass<T>) {
    const cache = this.cache<T>(cls)
    let ret = undefined
    const readed = cache.readed
    let file_path = ''
    if (!readed) {
      const { code, rsp, cfg_name, file_path: path } = await Cmd.read<T>(cls)
      if (code === ErrorCode.OK && rsp) {
        cache.readed = true
        cache.cfg_name = cfg_name
        this.assign(cls, rsp)
      }
      file_path = path
      ret = code
    }

    this.log(
      `Cfg====readG uri={%1} code={%2} readed={%3} cfg={%4}`,
      cls.uri,
      ret,
      readed,
      cache.cfg
    )

    this.writeMessage(`Loading file:${file_path}${cache.cfg_name || ''} uri=${cls.uri} code=${ret}`)

    return cache.cfg
  }

  cache<T extends BaseProto<T>>(cls: ProtoClass<T>, fileName = '', engineName = '') {
    const uri = `${engineName}_${cls.uri}_${fileName}`
    if (!this._caches.has(uri)) {
      const cfg = new cls()
      const changed = false
      const readed = false
      this._caches.set(uri, { cfg, changed, readed })
    }

    return this._caches.get(uri) as CacheCfg<T>
  }

  getFile<T extends BaseProto<T>>(cls: ProtoClass<T>, fileName = '', engineName = '') {
    const uri = `${engineName}_${cls.uri}_${fileName}`
    return this._files.get(uri)
  }

  setFile<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    fileName = '',
    engineName = '',
    data: string
  ) {
    const uri = `${engineName}_${cls.uri}_${fileName}`
    this._files.set(uri, data)
  }

  assign<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    val: Partial<T>,
    fileName = '',
    engineName = ''
  ) {
    const cache = this.cache<T>(cls, fileName, engineName)
    const { cfg } = cache
    const changed = cfg.write(val)
    cache.changed = changed

    this.log(
      `Cfg====assign uri={%1} engineName={%2} fileName={%3} changed={%4} readed={%5}`,
      cls.uri,
      engineName,
      fileName,
      changed,
      cache?.readed
    )

    // this.writeMessage(
    //   `Config is assigned: customer=${this._customer}, fileName=${fileName}, cfg_name=${cache.cfg_name}, uri=${cls.uri}, changed=${changed}`
    // )

    return changed
  }

  async write<T extends BaseProto<T>>(cls: ProtoClass<T>, engineName = '', fileName = '') {
    const cache = this.cache<T>(cls, fileName, engineName)
    if (!cache?.readed) {
      await this.read<T>(cls, engineName, false, fileName)
    }
    let code = undefined
    let file_path = ''
    let cfg_name = fileName
    const changed = cache?.changed
    if (cache && changed) {
      const rsp = await Cmd.write<T>(cls, cache.cfg, this._customer, engineName, fileName)
      code = rsp.code
      file_path = rsp.file_path
      cfg_name = rsp.cfg_name
      cache.changed = code !== ErrorCode.OK
    }

    this.log(
      `Cfg====write customer={%1} engineName={%2} uri={%3} fileName={%4} cfg_name={%5} code={%6} readed={%7}`,
      this._customer,
      engineName,
      cls.uri,
      fileName,
      cache.cfg_name,
      code,
      changed
    )

    this.writeMessage(
      `Writting file:${file_path}${cache.cfg_name || ''} engineName=${engineName} uri=${
        cls.uri
      } code=${code}`
    )

    return code === ErrorCode.OK
  }

  async writeG<T extends BaseProto<T>>(cls: ProtoClass<T>) {
    const cache = this.cache<T>(cls)
    if (!cache?.readed) {
      await this.readG<T>(cls)
    }
    let code = undefined
    let file_path = ''
    let cfg_name = ''
    const changed = cache?.changed
    if (cache && changed) {
      const rsp = await Cmd.write<T>(cls, cache.cfg)
      code = rsp.code
      file_path = rsp.file_path
      cfg_name = rsp.cfg_name
      cache.changed = code !== ErrorCode.OK
    }

    this.log(
      `Cfg====writeG uri={%1} cfg_name={%2} code={%3} readed={%4}`,
      cls.uri,
      cache.cfg_name,
      code,
      changed
    )

    this.writeMessage(
      `Writting file:${file_path}${cache.cfg_name || ''} uri=${cls.uri} code=${code}`
    )

    return code === ErrorCode.OK
  }

  async importCfg(name: string) {
    const list = this._versions.get(this._customer) || []
    const version = list.find(ver => ver.name === name)
    let result = false
    let err = ''
    do {
      if (!version || version?.name === this._version?.name) {
        err = `version error to: ${version?.name} from: ${this._version?.name}`
        break
      }
      // check version file
      let path = Path.ensureVersionDir()
      if (!path) {
        err = `check version file error path: ${path}`
        break
      }

      const zipFile = Path.join(path, this._customer, `${name}.ver`)
      if (!existed(zipFile)) {
        err = `check zip file not existed zipFile: ${zipFile}`
        break
      }

      // back current cfg
      path = Path.ensureBackDir()
      if (!path) {
        err = `back current cfg path not existed path: ${path}`
        break
      }

      if (!(await this.backCfg(path))) {
        err = `back cfg path error`
        break
      }

      path = Path.join(Path.dataDir, this._customer)
      removeDir(path)

      if (!makeDir(path)) {
        err = `make dir path error path=${path}`
        break
      }

      await this.unzip(zipFile, path)

      result = true
      // eslint-disable-next-line no-constant-condition
    } while (false)

    if (result) {
      this._version = version

      const customer = this._customers.get(this._customer)
      if (customer && version) {
        customer.version = version?.name

        const {
          cfg: { customers = [] }
        } = this.cache(entry.Entry)
        const item = customers.find(item => item.name === this._customer)
        if (item) {
          item.version = customer.version

          const changed = this.assign(entry.Entry, {
            customers
          })
          changed && (await this.writeG(entry.Entry))
        }
      }

      this.emit(ICfg.OnVersion)
    }

    this.log('importCfg====name={%1} result={%2} err={%3}', name, result, err)

    this.writeMessage(
      `Config is imported: customer=${this._customer}, name=${name}, result=${result}, err=${err}`
    )

    return result
  }

  async exportCfg(comments: string) {
    const path = Path.ensureVersionDir()
    const [name, file] = this.makeName('ver')
    let zipFile = Path.join(path, this._customer)
    if (!existed(zipFile)) {
      makeDir(zipFile)
    }
    zipFile = Path.join(zipFile, file)

    const cfgPath = Path.join(Path.dataDir, this._customer)
    let result = false
    const zipFiles: IFile[] = []
    if (existed(cfgPath)) {
      result = !!(await this.zipOutput(cfgPath, zipFile))

      if (result) {
        const {
          cfg: { customers = [] }
        } = this.cache(versions.Versions)
        const md5 = computeChecksum(zipFile)
        const customer = customers.find(item => item.name.toLowerCase() === this._customer)
        if (customer) {
          const version = new versions.Version({ name, comments, md5 })
          customer.versions.push(version)

          const changed = this.assign(versions.Versions, {
            customers
          })
          changed && (await this.writeG(versions.Versions))
        }
      }
    }

    this.log(
      'exportCfg====comments={%1} zipFile={%2} files={%3} result={%4}',
      comments,
      zipFile,
      zipFiles,
      result
    )

    this.writeMessage(
      `Config is exported: customer=${this._customer}, comments=${comments}, zipFile=${zipFile}, result=${result}`
    )
    return !!result
  }

  async createEngine(name: string) {
    const result = await this.ensureDir(name)
    this.log('createEngine======customer={%1} name={%2} ret={%3}', this._customer, name, result)

    this.writeMessage(
      `Engine is created: customer=${this._customer}, name=${name}, result=${result}`
    )
    return result
  }

  async copyEngine(name: string, newName: string) {
    const result = await this.copyDir(name, newName)
    this.log(
      'copyEngine======customer={%1} name={%2} newName={%3} ret={%4}',
      this._customer,
      name,
      newName,
      result
    )
    this.writeMessage(
      `Engine is copyed: customer=${this._customer}, from=${name}, to=${newName}, result=${result}`
    )
    return result
  }

  async deleteEngine(name: string) {
    const result = await this.deleteDir(name)
    this.log('deleteEngine======customer={%1} name={%2} ret={%3}', this._customer, name, result)

    this.writeMessage(
      `Engine is deleted: customer=${this._customer}, name=${name}, result=${result}`
    )
    return result
  }

  async createDisplay(engine: string, name: string) {
    const file = `${name}.dsp`
    const ret = await this.checkFile(file, engine)

    const result = (ret && file) || ''
    this.log(
      'createDisplay======customer={%1} engine={%2} name={%3} result={%4}',
      this._customer,
      engine,
      name,
      result
    )
    this.writeMessage(
      `Display is created: customer=${this._customer}, engine=${engine}, name=${name}, result=${result}`
    )
    return result
  }

  async removeDisplay(engine: string, fileName: string) {
    const result = await this.removeFile(fileName, engine)
    this.log(
      'removeDisplay======customer={%1} engine={%2} fileName={%3} ret={%4}',
      this._customer,
      engine,
      fileName,
      result
    )
    this.writeMessage(
      `Display is removed: customer=${this._customer}, engine=${engine}, fileName=${fileName}, result=${result}`
    )
    return result
  }

  async renameDisplay(engine: string, oldName: string, newName: string) {
    const file = `${newName}.dsp`
    const ret = await this.renameFile(`${oldName}.dsp`, file, engine)
    const result = (ret && file) || ''
    this.log(
      'renameDisplay======customer={%1} engine={%2} oldName={%3} newName={%4} ret={%5}',
      this._customer,
      engine,
      oldName,
      newName,
      result
    )
    this.writeMessage(
      `Display is renamed: customer=${this._customer}, engine=${engine}, oldName=${oldName}, newName=${newName}, result=${result}`
    )
    return result
  }

  async createCalc(name: string, engine?: string) {
    const file = `${name}.cal`
    const ret = await this.checkFile(file, engine)

    const result = (ret && file) || ''
    this.log(
      'createCalc======customer={%1} engine={%2} name={%3} result={%4}',
      this._customer,
      engine,
      name,
      result
    )
    this.writeMessage(
      `Calc is created: customer=${this._customer}, engine=${engine}, name=${name}, result=${result}`
    )
    return result
  }

  async removeCalc(name: string, engine?: string): Promise<boolean> {
    const result = await this.removeFile(name, engine)
    this.log(
      'removeCalc======customer={%1} engine={%2} name={%3} ret={%4}',
      this._customer,
      engine,
      name,
      result
    )
    this.writeMessage(
      `Calc is removed: customer=${this._customer}, engine=${engine}, name=${name}, result=${result}`
    )
    return result
  }

  async renameCalc(oldName: string, newName: string, engine?: string): Promise<string> {
    const file = `${newName}.cal`
    const ret = await this.renameFile(`${oldName}.cal`, file, engine)
    const result = (ret && file) || ''
    this.log(
      'renameCalc======customer={%1} engine={%2} oldName={%3} newName={%4} ret={%5}',
      this._customer,
      engine,
      oldName,
      newName,
      result
    )
    this.writeMessage(
      `Calc is renamed: customer=${this._customer}, engine=${engine}, oldName=${oldName}, newName=${newName}, result=${result}`
    )
    return result
  }

  async removeFile(name: string, path?: string) {
    let dir = Path.dataDir
    dir = Path.join(dir, this._customer)
    path && (dir = Path.join(dir, path))
    dir = Path.join(dir, name)
    if (!existed(dir)) {
      return false
    }
    removeFile(dir)

    return true
  }

  async moveFile(name: string, from: string, to: string) {
    this.log(
      'moveFile======customer={%1} name={%2} from={%3} to={%4}',
      this._customer,
      name,
      from,
      to
    )
    const dir = Path.dataDir
    const path = Path.join(dir, this._customer, from, name)
    if (existed(path)) {
      const dest = Path.join(dir, this._customer, to, name)
      copyLocalFile(path, dest)
      removeFile(path)

      return true
    }

    return false
  }

  async copyFile(path: string, fromName: string, toName: string) {
    this.log(
      'copyFile======customer={%1} path={%2} fromName={%3} toName={%4}',
      this._customer,
      path,
      fromName,
      toName
    )
    const dir = Path.dataDir
    const from = Path.join(dir, this._customer, path, fromName)
    if (existed(from)) {
      const dest = Path.join(dir, this._customer, path, toName)
      copyLocalFile(from, dest)
      return true
    }

    return false
  }

  async createFile(name: string, path: string) {
    const dir = Path.dataDir
    const file = Path.join(dir, this._customer, path, name)
    if (!existed(file)) {
      writeFile(file, '')

      return true
    }

    return false
  }

  @method()
  async writeMessage(message: string) {
    const timestamp = Date.now()
    const msg = { message, timestamp }

    if (this._messages.length > Cfg.kMaxCount) {
      this._messages = this._messages.slice(300)
    }
    this._messages.push(msg)

    this.emit(ICfg.OnMessage)
  }

  @method()
  async loadMessage() {
    return this._messages
  }

  @method()
  async scanFiles(engine: string, recurse = true, exclude = [], exts = []) {
    const dir = Path.dataDir
    let path = Path.join(dir, this._customer)
    engine && (path = Path.join(path, engine))

    if (!existed(path)) {
      return []
    }

    return scanDir(path, recurse, exclude, exts)
  }

  async readText<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    engineName?: string,
    fileName?: string,
    force?: boolean
  ): Promise<string | undefined> {
    let result = this.getFile<T>(cls, fileName, engineName)
    let ret = undefined
    let file_path = ''
    if (!result || force) {
      const {
        code,
        rsp,
        cfg_name,
        file_path: path
      } = await Cmd.text<T>(cls, this._customer, engineName, fileName)
      if (code === ErrorCode.OK && rsp) {
        this.setFile<T>(cls, fileName, engineName, rsp)
        result = this.getFile<T>(cls, fileName, engineName)
        file_path = path
      }
      ret = code
    }

    this.log(
      `Cfg====readText customer={%1} engineName={%2} uri={%3} code={%4} fileName={%5} file_path={%6}`,
      this._customer,
      engineName,
      cls.uri,
      ret,
      fileName,
      file_path
    )

    this.writeMessage(`Read Text:${file_path} engineName=${engineName} uri=${cls.uri} code=${ret}`)

    return result
  }

  async writeText<T extends BaseProto<T>>(
    cls: ProtoClass<T>,
    data: string,
    engineName?: string,
    fileName?: string
  ): Promise<boolean> {
    const result = this.getFile<T>(cls, fileName, engineName)
    let code = undefined
    let file_path = ''
    let cfg_name = fileName
    const changed = result !== data
    if (changed) {
      const rsp = await Cmd.save<T>(cls, data, this._customer, engineName, fileName)
      code = rsp.code
      file_path = rsp.file_path
      cfg_name = rsp.cfg_name
      if (code === ErrorCode.OK) {
        this.setFile<T>(cls, fileName, engineName, data)
      }
    } else {
      code = ErrorCode.OK
    }

    this.log(
      `Cfg====writeText customer={%1} engineName={%2} uri={%3} fileName={%4} cfg_name={%5} code={%6} readed={%7}`,
      this._customer,
      engineName,
      cls.uri,
      fileName,
      cfg_name,
      code,
      changed
    )

    this.writeMessage(
      `Writting file:${file_path}${cfg_name || ''} engineName=${engineName} uri=${
        cls.uri
      } code=${code}`
    )

    return code === ErrorCode.OK
  }

  private makesureCustomerDir() {
    if (this._customer) {
      const path = Path.join(Path.dataDir, this._customer)
      if (!existed(path)) {
        makeDir(path)
      }
    }
  }

  private async backCfg(path: string) {
    const [name, file] = this.makeName()
    let zipFile = Path.join(path, this._customer)
    if (!existed(zipFile)) {
      makeDir(zipFile)
    }
    zipFile = Path.join(zipFile, file)

    const cfgPath = Path.join(Path.dataDir, this._customer)
    let result = false
    const zipFiles: IFile[] = []
    if (existed(cfgPath)) {
      result = !!(await this.zipOutput(cfgPath, zipFile))
    }

    this.log('backCfg====path={%1} files={%2} result={%3}', path, zipFiles, result)

    return !!result
  }

  private async zipOutput(cfgPath: string, zipFile: string) {
    const zipFiles: IFile[] = []
    const files = scanDir(cfgPath)
    files.forEach(({ path: localPath }) => {
      let path = localPath.replace(cfgPath, '')
      if (path.startsWith('\\') || path.startsWith('/')) {
        path = path.substring(1)
      }
      zipFiles.push({ localPath, path })
    })
    return !!(await this.zip(zipFile, zipFiles))
  }

  private makeName(ext = 'bak') {
    const now = new Date()
    let name = format(now, 'yyyy-MM-dd-HH-mm-ss')
    const no = ++Cfg.kNameIndex
    name = `${name}-${no}`
    const file = `${name}.${ext}`
    return [name, file]
  }

  private async zip(zipPath: string, files: IFile[]) {
    this.log('zip====zipPath={%1} files={%2}', zipPath, files)
    return await zip(zipPath, files)
  }

  private async unzip(zipPath: string, targetPath: string) {
    this.log('unzip====zipPath={%1} targetPath={%2}', zipPath, targetPath)
    await extract(zipPath, targetPath, { overwrite: true }, CancellationToken.None)
  }

  private async loadOptions(force?: boolean) {
    await this.loadAppCustomers()
    await this.loadAppVersions()

    this.checkCustomerVersion()
  }

  private async clearOptions() {}

  private async loadAppCustomers() {
    this._customers.clear()
    const { current, customers = [] } = (await this.readG(entry.Entry)) || {}
    customers.forEach(customer => this._customers.set(customer.name.toLowerCase(), customer))

    this.checkChangeCustomer(current)

    this.log(
      'loadAppCustomers====== customers={%1} current={%2} customer={%3}',
      customers,
      current,
      this._customer
    )

    this.emit(ICfg.OnCustomers)
  }

  private checkChangeCustomer(name: string) {
    let customer = name.toLowerCase()
    if (!this._customers.has(customer)) {
      customer = ''
    }

    let result = false
    if (customer !== this._customer) {
      this._customer = customer
      this.makesureCustomerDir()

      this.checkCustomerVersion()

      this.emit(ICfg.OnCustomer)

      result = true
    }

    this.log(
      'checkChangeCustomer====== name={%1} current={%2} result={%3}',
      name,
      this._customer,
      result
    )
    return result
  }

  private checkCustomerVersion() {
    if (this._customer) {
      const versions = this._versions.get(this._customer)
      const data = this._customers.get(this._customer)
      const version = versions?.find(item => item.name === data?.version)
      this._version = version

      this.emit(ICfg.OnVersion)
    }

    this.log('checkCustomerVersion====== customer={%1} version={%2}', this._customer, this._version)
  }

  private async loadAppVersions() {
    this._versions.clear()
    const { customers = [] } = (await this.readG(versions.Versions)) || {}
    customers.forEach(item => this._versions.set(item.name.toLowerCase(), item.versions))

    this.checkCustomerVersion()

    this.log('loadAppVersions====== versions={%1}', customers)

    this.emit(ICfg.OnVersions)
  }

  // async load() {
  //   kCfgFiles.forEach(type => this.loadCfg(type))
  //   return false
  // }

  // async read<T>(type: CfgType) {
  //   const result = this._caches.get(type) || this.loadCfg(type)

  //   return result as T
  // }

  // async copyEngine(name: string, dest: string) {
  //   const cfg = this._caches.get(CfgType.CFG_CUST)
  //   const data: CfgCust = cfg?.data
  //   if (!name || !dest || !data) {
  //     return false
  //   }
  //   const engine_type = data.engine_type || []
  //   if (engine_type.findIndex(item => item.name === name) < 0) {
  //     return false
  //   }

  //   data.engine_type = [{ name: dest, label: dest.toUpperCase() }, ...engine_type]
  //   const result = await this.writeCfg(CfgType.CFG_CUST, data)
  //   result && this.copyDir(CfgType.CFG_CUST, name, dest)
  //   this.emit(CfgType.CFG_CUST, data)

  //   return result
  // }

  // async deleteEngine(name: string) {
  //   const cfg = this._caches.get(CfgType.CFG_CUST)
  //   const data: CfgCust = cfg?.data
  //   if (!name || !data) {
  //     return false
  //   }
  //   const engine_type = data.engine_type || []
  //   if (engine_type.findIndex(item => item.name === name) < 0) {
  //     return false
  //   }

  //   data.engine_type = engine_type.filter(it => it.name !== name)
  //   const result = await this.writeCfg(CfgType.CFG_CUST, data)
  //   result && this.deleteDir(CfgType.CFG_CUST, name)
  //   this.emit(CfgType.CFG_CUST, data)

  //   return result
  // }

  // async write<T>(type: CfgType, data: T) {
  //   return await this.writeCfg<T>(type, data)
  // }

  // async save(type: CfgType, msg: string) {
  //   return await this.saveCfg(type, msg)
  // }

  // async saveEngine(name: string) {
  //   return false
  // }

  // async saveEngineAs(name: string, dest: string) {
  //   if (!name || !dest) {
  //     return false
  //   }

  //   const dir = kCfgDirs[CfgType.CFG_CUST]
  //   const path = joinPath(kCfgWorkDir, dir, name)
  //   if (existed(path)) {
  //     copyDir(path, dest, [Rcs.kRCS])
  //   }

  //   return true
  // }

  // private async loadCfg(type: CfgType) {
  //   const [name, file, path] = await this.ensureCfg(type)
  //   const [code, info] = await this._reader.read(type, name, file, path)
  //   if (info && code === ErrorCode.OK) {
  //     this._caches.set(type, info)
  //     this.emit(type, info.data)
  //     return info
  //   }

  //   return undefined
  // }

  // private async ensureCfg(type: CfgType) {
  //   const dir = kCfgDirs[type]
  //   const path = joinPath(kCfgWorkDir, dir)
  //   if (!existed(path)) {
  //     makeDir(path)
  //   }
  //   const initedRcs = Rcs.init(path)
  //   const name = kCfgNames[type]
  //   const file = joinPath(path, name)
  //   if (!existed(file) && !initedRcs) {
  //     Rcs.co(path, name)
  //   }
  //   return [name, file, path]
  // }

  private async ensureDir(name: string) {
    const dir = Path.dataDir
    const path = Path.join(dir, this._customer, name)
    if (!existed(path)) {
      makeDir(path)
    }
    Rcs.init(path)

    return true
  }

  private async checkFile(name: string, sub?: string) {
    const dir = Path.dataDir
    let path = Path.join(dir, this._customer)
    sub && (path = Path.join(path, sub))
    path = Path.join(path, name)
    // if (existed(path)) {
    //   return false
    // }

    writeFile(path, '')

    return true
  }

  private async renameFile(name: string, to: string, sub?: string) {
    const dir = Path.dataDir
    let path = Path.join(dir, this._customer)
    sub && (path = Path.join(path, sub))
    const from = Path.join(path, name)
    const file = Path.join(path, to)

    if (!existed(path) || existed(file)) {
      return false
    }

    rename(from, file)

    return true
  }

  private async copyDir(from: string, to: string) {
    const dir = Path.dataDir
    const path = Path.join(dir, this._customer, from)
    if (existed(path)) {
      const dest = Path.join(dir, this._customer, to)
      copyDir(path, dest, [Rcs.kRCS])

      // Rcs.init(dest)

      return true
    }

    return false
  }

  private async deleteDir(name: string) {
    const dir = Path.dataDir
    const path = Path.join(dir, this._customer, name)
    if (existed(path)) {
      removeDir(path)
    }

    return true
  }

  // private async writeCfg<T>(type: CfgType, data: T) {
  //   const cache = this._caches.get(type)
  //   if (!cache) {
  //     return false
  //   }
  //   cache.data = data
  //   const path = cache.path
  //   if (!existed(path)) {
  //     makeDir(path)
  //   }
  //   const code = await this._reader.write(cache)
  //   this.emit(type, data)

  //   return code === ErrorCode.OK
  // }

  // private async saveCfg(type: CfgType, msg: string) {
  //   const cache = this._caches.get(type)
  //   if (!cache) {
  //     return false
  //   }
  //   const { name, path } = cache
  //   if (!existed(path)) {
  //     makeDir(path)
  //   }
  //   Rcs.init(path)
  //   const code = await Rcs.ci(path, name, msg)

  //   return code === RcsRes.RCS_OK
  // }
}

import fs from 'fs'
import path from 'path'

export const copyDir = (src: string, dest: string) => {
  const entries = fs.readdirSync(src, { withFileTypes: true })

  fs.mkdirSync(dest, { recursive: true })

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name)
    const destPath = path.join(dest, entry.name)

    if (entry.isDirectory()) {
      copyDir(srcPath, destPath)
    } else {
      fs.copyFileSync(srcPath, destPath)
    }
  }
}

export const writeFileSync = (
  path: string,
  contents: NodeJS.ArrayBufferView | string,
  encoding: BufferEncoding = 'utf8'
) => {
  fs.writeFileSync(path, contents, encoding)
}
const dirCache: { [dir: string]: boolean } = {}
export const writeFile = (
  filePath: string,
  contents: NodeJS.ArrayBufferView | string,
  encoding: BufferEncoding = 'utf8'
) => {
  const ensureDirs = (dirPath: string) => {
    if (dirCache[dirPath]) {
      return
    }
    dirCache[dirPath] = true

    ensureDirs(path.dirname(dirPath))
    if (fs.existsSync(dirPath)) {
      return
    }
    fs.mkdirSync(dirPath)
  }
  ensureDirs(path.dirname(filePath))
  writeFileSync(filePath, contents, encoding)
}

export const readFile = (path: string, encoding: BufferEncoding = 'utf8') => {
  return fs.readFileSync(path, encoding) || ''
}

const copied: { [fileName: string]: boolean } = {}
export const copyFile = (srcPath: string, dstPath: string) => {
  if (copied[srcPath]) {
    return
  }
  copied[srcPath] = true
  writeFile(dstPath, readFile(srcPath))
}

export const isDirectory = (path: string) => {
  const stats = fs.statSync(path)
  return !!stats?.isDirectory()
}

export const existed = (path: string) => {
  return fs.existsSync(path)
}

export const rename = (oldPath: string, newPath: string) => {
  fs.renameSync(oldPath, newPath)
}

export type ScanDirResult = { type: string; path: string; name: string; entry: string }[]
export const scanDir = (dirPath: string, exclude: string[] = []): ScanDirResult => {
  const entries = fs.readdirSync(dirPath)
  const result: ScanDirResult = []
  for (const entry of entries) {
    const ext = path.extname(entry)
    const name = entry.replace(ext, '')
    const type = ext.substring(1)
    const filePath = path.join(dirPath, entry)
    const stats = fs.statSync(filePath)
    if (stats.isDirectory()) {
      result.push(...scanDir(filePath, exclude))
    } else if (exclude.indexOf(type) < 0) {
      result.push({ type, path: filePath, name, entry })
    }
  }

  return result
}

export const removeFile = (fileName: string) => {
  fs.unlinkSync(fileName)
}

export const removeDirSync = (dirPath: string) => {
  fs.unlinkSync(dirPath)
}

export const removeDir = (dirPath: string) => {
  if (!existed(dirPath)) {
    return
  }
  const entries = fs.readdirSync(dirPath, { withFileTypes: true })
  for (const entry of entries) {
    const srcPath = path.join(dirPath, entry.name)
    if (entry.isDirectory()) {
      removeDir(srcPath)
    } else {
      removeFile(srcPath)
    }
  }
  removeDirSync(dirPath)
}

export const makeDir = (dirPath: string) => {
  if (existed(dirPath)) {
    return true
  }
  if (makeDir(path.dirname(dirPath))) {
    fs.mkdirSync(dirPath)
    return true
  }
  return false
}

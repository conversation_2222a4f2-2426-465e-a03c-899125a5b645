import { BaseItr, BaseMtr, IBase } from './IBase'

export interface AttributeItem {
  /**
   * name
   */
  name: string
  /**
   * Units
   */
  units: string
  /**
   * Long Name
   */
  long_name: string
  /**
   * Display Format width max 8
   */
  width: number
  /**
   * Display Format precision max 8
   */
  precision: number
}

export interface AttributeOptions {
  list: AttributeItem[]
}

export abstract class IAttribute extends IBase<IAttribute> {
  abstract readOptions(): Promise<AttributeOptions>
  abstract removeAttribute(index: number): Promise<boolean>
  abstract addAttribute(val: AttributeItem, index?: number): Promise<boolean>
  abstract modifyAttribute(index: number, val: Partial<AttributeItem>): Promise<boolean>

  static override get NAME() {
    return 'Attribute'
  }

  static get OnOptions() {
    return 'Attribute.OnOptions'
  }
}

export type AttributeItr = BaseItr<IAttribute>
export type AttributeMtr = BaseMtr<IAttribute>

/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  IncludeFile as IncludeFileModal,
  CalcVxi as CalcVxiModal
} from '../modal/calcvxi.mode'

export class IncludeFile extends MapProto<IncludeFile> {
  file = ''
  uri = ''
  depends: Array<string> = []

  constructor(val?: Partial<IncludeFile>) {
    super(IncludeFileModal, 'IncludeFile')
    val && this.assign(val)
  }
}

export class CalcVxi extends RootProto<CalcVxi> {
  includes: Array<IncludeFile> = []

  constructor(val?: Partial<CalcVxi>) {
    super(CalcVxiModal, CalcVxi.key)
    val && this.assign(val)
  }

  static get maxType() {
    return CalcVxiModal.$MAX
  }

  static get minType() {
    return CalcVxiModal.$MIN
  }

  static get uri() {
    return URI(CalcVxiModal.$MAX, CalcVxiModal.$MIN)
  }

  static get types(): [number, number] {
    return [CalcVxiModal.$MAX, CalcVxiModal.$MIN]
  }

  static get key() {
    return 'CalcVxi'
  }
}

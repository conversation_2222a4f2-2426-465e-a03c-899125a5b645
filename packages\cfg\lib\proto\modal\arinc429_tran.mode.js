/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let BitValue={$keys:["name","bit_num","val_0","val_1","lable"],name:types.string,bit_num:types.int32,val_0:types.string,val_1:types.string,lable:types.string},RangeValue={$keys:["min","max","unit"],min:types.double,max:types.double,unit:types.string},ArincTranSignal={$keys:["name","label","data","start_bit","stop_bit","rate","sdi","ssm","bin_range","sig_bits","signal_range","bits"],name:types.string,label:types.string,data:types.string,start_bit:types.int32,stop_bit:types.int32,rate:types.int32,sdi:types.string,ssm:types.string,bin_range:types.double,sig_bits:types.int32,signal_range:RangeValue,bits:types.arrayOf(BitValue)},Arinc429TranCfg={$MAX:8e3,$MIN:12,$keys:["channel","eec","speed","parity","active","tran_signals"],channel:types.int32,eec:types.string,speed:types.string,parity:types.string,active:types.string,tran_signals:types.arrayOf(ArincTranSignal)};export{BitValue,RangeValue,ArincTranSignal,Arinc429TranCfg};
/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  TableLib as TableLibModal,
  TableCfg as TableCfgModal
} from '../modal/tablecfg.mode'

export class TableLib extends MapProto<TableLib> {
  dim = 0
  name = ''
  data: Array<number> = []

  constructor(val?: Partial<TableLib>) {
    super(TableLibModal, 'TableLib')
    val && this.assign(val)
  }
}

export class TableCfg extends RootProto<TableCfg> {
  name = ''
  tables: Array<TableLib> = []

  constructor(val?: Partial<TableCfg>) {
    super(TableCfgModal, TableCfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return TableCfgModal.$MAX
  }

  static get minType() {
    return TableCfgModal.$MIN
  }

  static get uri() {
    return URI(TableCfgModal.$MAX, TableCfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [TableCfgModal.$MAX, TableCfgModal.$MIN]
  }

  static get key() {
    return 'TableCfg'
  }
}

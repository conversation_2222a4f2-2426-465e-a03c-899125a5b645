$DISPLAY "Start" background=NewGray editres=1280:448 movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$STOREDISPLAY Overwrite
$OBJECT Text font=1 startx=20 starty=15 endx=310 endy=50
Black direction=1 alignment=1 fontweight=500 fontsize=20
"Start Screen"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=15 starty=25 endx=80 endy=50
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
direction=1 fontsize=16 fontweight=400 
Digital1 type="FLOAT" width=6 prec=2 label="" units="" 
$END-OBJECT
***********
$OBJECT Plot font=1 startx=30 starty=50 endx=355 endy=400
$TYPE SCROLLED
$SPOT-PARAM Cycle
$LINE_WIDTH 2
$HIDE_BUTTON 1
$XAXIS TestRunTime 533 40 902 100 1 BELOW BELOW 0 100 White 0.000000
$YAXIS N1Percent 40 0 533 10 1 LEFT LEFT 0 120 White 1 0.000000
$COLOR
Black
White
$END-COLOR
$TABLE-PLOT Red
InvestMOP_T
$END-TABLE-PLOT
$END-YAXIS
$HARDCOPYPLOT 1 0 QUANTITY=1
$END-HARDCOPYPLOT
$END-OBJECT
***********
$OBJECT Text font=22 startx=8 starty=150 endx=28 endy=250
White direction=0 alignment=0 fontsize=14 fontweight=600
"Peeds %"
$END-OBJECT
***********
$OBJECT FuncButton font=22 startx=100 starty=385 endx=220 endy=410
type="PLOTACTIVE" label="PLOTACTIVE" backgroundcolor=Yellow textcolor=Black fontsize=14 fontweight=400
$END-OBJECT
***********
$OBJECT Box font=1 startx=350 starty=10 endx=560 endy=220
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Text font=1 startx=370 starty=30 endx=540 endy=60
Blue direction=1 alignment=1 fontweight=500 fontsize=20
"EGT"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=370 starty=75 endx=435 endy=150
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
direction=1 fontsize=18 fontweight=400 labelfontsize=14 labelfontweight=400 
Digital1 type="FLOAT" width=7 prec=0 label="Peak EGT" units="" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=475 starty=75 endx=540 endy=150
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
direction=1 fontsize=18 fontweight=400 labelfontsize=14 labelfontweight=400 
Digital1 type="FLOAT" width=7 prec=0 label="Peak EGT" units="" 
$END-OBJECT
***********
$OBJECT Switch font=22 startx=365 starty=158 endx=440 endy=188
TOGGLE Switch1 onlabel="Reset" offlabel="Reset" offcolor=NewGray oncolor=NewGray labelcolor=Yellow fontsize=15
$END-OBJECT
***********
$OBJECT Digital font=20 startx=350 starty=245 endx=485 endy=391
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
direction=1 fontsize=18 fontweight=400 labelfontsize=14 labelfontweight=400 \
unitfontsize=12 unitfontweight=400 
Digital1 type="FLOAT" width=7 prec=1 label="Starter Dropout\nSpeed" units="%" 
Digital1 type="FLOAT" width=7 prec=1 label="Start Time Seconds" units="" 
$END-OBJECT
***********
$OBJECT Box font=1 startx=570 starty=10 endx=720 endy=305
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Text font=1 startx=570 starty=13 endx=720 endy=30
Black direction=1 alignment=1 fontweight=400 fontsize=15
"Starter"
$END-OBJECT
***********
$OBJECT Text font=1 startx=576 starty=30 endx=651 endy=52
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Volts"
$END-OBJECT
***********
$OBJECT Text font=1 startx=645 starty=30 endx=720 endy=52
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Amps"
$END-OBJECT
***********
$OBJECT Bar font=20 startx=590 starty=60 endx=638 endy=275
labelspace=5 unitspace=1 labelcolor=Black length=185 \
height=8 format=4.0 ticpos=LEFT dir=1 shading=0 spacing=6 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 digitfontsize=18 ticfontsize=12
Spare target=Spare label="" units="" min=0 max=30 ticinter=5 labelinter=1
$END-OBJECT
***********
$OBJECT Bar font=20 startx=660 starty=60 endx=709 endy=275
labelspace=5 unitspace=1 labelcolor=Black length=185 \
height=8 format=4.1 ticpos=LEFT dir=1 shading=0 spacing=6 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 digitfontsize=18 ticfontsize=12
Spare target=Spare label="" units="" min=0 max=1500 ticinter=250 labelinter=1
$END-OBJECT
***********
$OBJECT Text font=1 startx=570 starty=278 endx=645 endy=300
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Volts"
$END-OBJECT
***********
$OBJECT Text font=1 startx=645 starty=278 endx=720 endy=300
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Amps"
$END-OBJECT
***********
$OBJECT Text font=1 startx=570 starty=310 endx=720 endy=315
Black direction=1 alignment=1 fontweight=400 fontsize=12
"DC Start"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=637 starty=335 endx=652 endy=350
CrsCollectDataSwitch textcolor=White fontsize=18 fontweight=500 radius="20" type="LIGHT"
Green = 1
Red = 0
$END-OBJECT
***********
$OBJECT Box font=1 startx=730 starty=10 endx=880 endy=305
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Text font=1 startx=730 starty=13 endx=880 endy=30
Black direction=1 alignment=1 fontweight=400 fontsize=15
"Generator"
$END-OBJECT
***********
$OBJECT Text font=1 startx=752 starty=30 endx=789 endy=52
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Volts"
$END-OBJECT
***********
$OBJECT Text font=1 startx=823 starty=30 endx=868 endy=52
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Amps"
$END-OBJECT
***********
$OBJECT Bar font=20 startx=730 starty=52 endx=803 endy=240
labelspace=5 unitspace=1 labelcolor=Black length=185 \
height=8 format=0.0 ticpos=LEFT dir=1 shading=0 spacing=0 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 ticfontsize=12
CalibGenVA label="" units="" min=0 max=150 ticinter=25 labelinter=1
CalibGenVB label="" units="" min=0 max=150 ticinter=0 labelinter=0
CalibGenVC label="" units="" min=0 max=150 ticinter=0 labelinter=0
$END-OBJECT
***********
$OBJECT Bar font=20 startx=805 starty=52 endx=874 endy=240
labelspace=5 unitspace=1 labelcolor=Black length=185 \
height=8 format=0.0 ticpos=LEFT dir=1 shading=0 spacing=0 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 ticfontsize=12
GenAmpsA label="" units="" min=0 max=300 ticinter=50 labelinter=1
GenAmpsB label="" units="" min=0 max=300 ticinter=0 labelinter=0
GenAmpsC label="" units="" min=0 max=300 ticinter=0 labelinter=0
$END-OBJECT
***********
$OBJECT Digital font=20 startx=752 starty=245 endx=797 endy=275
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
direction=1 fontsize=16 fontweight=400 
GenVAvg type="FLOAT" width=6 prec=2 label="" units="" 
$END-OBJECT
***********
$OBJECT Text font=1 startx=752 starty=278 endx=797 endy=300
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Volts"
$END-OBJECT
***********
$OBJECT Digital font=20 startx=805 starty=245 endx=890 endy=275
labelspace=5 unitspace=5 spacing=0 labelcolor=Black parambox=2 paramboxcolor=White shading=2 \
direction=1 fontsize=16 fontweight=400 
GenAmpsAvg type="FLOAT" width=6 prec=2 label="" units="" 
$END-OBJECT
***********
$OBJECT Text font=1 startx=803 starty=278 endx=888 endy=300
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Amps"
$END-OBJECT
***********
$OBJECT Text font=1 startx=730 starty=310 endx=880 endy=315
Black direction=1 alignment=1 fontweight=400 fontsize=12
"DC Start"
$END-OBJECT
***********
$OBJECT StatusIndicator font=66 startx=795 starty=335 endx=810 endy=350
CrsCollectDataSwitch textcolor=White fontsize=18 fontweight=500 radius="20" type="LIGHT"
Green = 1
Red = 0
$END-OBJECT
***********
$OBJECT Box font=1 startx=890 starty=10 endx=1020 endy=305
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Text font=1 startx=890 starty=13 endx=1020 endy=30
Black direction=1 alignment=1 fontweight=400 fontsize=15
"Fuel"
$END-OBJECT
***********
$OBJECT Text font=1 startx=890 starty=30 endx=955 endy=52
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Flow"
$END-OBJECT
***********
$OBJECT Text font=1 startx=955 starty=30 endx=1019 endy=52
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Boost"
$END-OBJECT
***********
$OBJECT Bar font=20 startx=895 starty=60 endx=955 endy=274
labelspace=5 unitspace=1 labelcolor=Black length=185 \
height=8 format=4.0 ticpos=LEFT dir=1 shading=0 digitfontsize=18 spacing=6 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 ticfontsize=12
Spare target=Spare label="" units="" min=0 max=400 ticinter=50 labelinter=1
$END-OBJECT
***********
$OBJECT Bar font=20 startx=960 starty=60 endx=1019 endy=275
labelspace=5 unitspace=1 labelcolor=Black length=185 \
height=8 format=4.1 ticpos=LEFT dir=1 shading=0 spacing=6 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 digitfontsize=18 ticfontsize=12
Spare target=Spare label="" units="" min=0 max=100 ticinter=20 labelinter=1
$END-OBJECT
***********
$OBJECT Text font=1 startx=892 starty=278 endx=957 endy=300
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Lb/Hr"
$END-OBJECT
***********
$OBJECT Text font=1 startx=957 starty=278 endx=1022 endy=300
Black direction=1 alignment=1 fontweight=400 fontsize=12
"PSI"
$END-OBJECT
***********
$OBJECT Box font=1 startx=1030 starty=10 endx=1270 endy=305
style=3
linewidth=0 linecolor=NewBlue
boxcolor=ShallowBlue
shading=0
$END-OBJECT
***********
$OBJECT Text font=1 startx=1030 starty=13 endx=1270 endy=30
Black direction=1 alignment=1 fontweight=400 fontsize=15
"Vibration"
$END-OBJECT
***********
$OBJECT Text font=1 startx=1030 starty=30 endx=1109 endy=52
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Gearbox"
$END-OBJECT
***********
$OBJECT Text font=1 startx=1110 starty=30 endx=1191 endy=52
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Turbine"
$END-OBJECT
***********
$OBJECT Text font=1 startx=1190 starty=29 endx=1270 endy=52
Black direction=1 alignment=1 fontweight=400 fontsize=12
"Turb Post"
$END-OBJECT
***********
$OBJECT Bar font=20 startx=1040 starty=60 endx=1110 endy=300
labelspace=5 unitspace=1 labelcolor=Black length=185 \
height=8 format=5.1 ticpos=LEFT dir=1 shading=0 spacing=6 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 digitfontsize=18 ticfontsize=12
CalibVB_1 target=CalibVB_1 label="" units="" min=0 max=2 ticinter=0.5 labelinter=1
$END-OBJECT
***********
$OBJECT Bar font=20 startx=1120 starty=60 endx=1190 endy=300
labelspace=5 unitspace=1 labelcolor=Black length=185 \
height=8 format=5.1 ticpos=LEFT dir=1 shading=0 spacing=6 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 digitfontsize=18 ticfontsize=12
CalibVB_2 target=CalibVB_2 label="" units="" min=0 max=10 ticinter=2 labelinter=1
$END-OBJECT
***********
$OBJECT Bar font=20 startx=1200 starty=60 endx=1270 endy=300
labelspace=5 unitspace=1 labelcolor=Black length=185 \
height=8 format=5.1 ticpos=LEFT dir=1 shading=0 spacing=6 \
ticfont=2 parambox=2 paramboxcolor=White limitwidth=2 digitfontsize=18 ticfontsize=12
CalibVB_3 target=CalibVB_3 label="" units="" min=0 max=6 ticinter=1 labelinter=1
$END-OBJECT
***********
$OBJECT Text font=1 startx=1035 starty=278 endx=1115 endy=300
Black direction=1 alignment=1 fontweight=400 fontsize=12
"IPS"
$END-OBJECT
***********
$OBJECT Text font=1 startx=1115 starty=278 endx=1195 endy=300
Black direction=1 alignment=1 fontweight=400 fontsize=12
"IPS"
$END-OBJECT
***********
$OBJECT Text font=1 startx=1195 starty=278 endx=1275 endy=300
Black direction=1 alignment=1 fontweight=400 fontsize=12
"IPS"
$END-OBJECT
***********
$OBJECT FuncButton font=22 startx=1145 starty=380 endx=1230 endy=430
type="CHANGEDSP" label="Close" backgroundcolor=DarkGray textcolor=Red fontsize=18 fontweight=400
crtname="Test CRT" quadindx=1 displayname="Main Second"
$END-OBJECT
***********
$OBJECT FuncButton font=22 startx=230 starty=385 endx=280 endy=410
type="REFRESHPLOT" label="Clear" backgroundcolor=Yellow textcolor=Black fontsize=14 fontweight=400
crtname="Test CRT" quadindx=1
$END-OBJECT
***********
$END-DISPLAY

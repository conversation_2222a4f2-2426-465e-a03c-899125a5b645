import { types } from '@wuk/wkp'

export const EuconvValue = {
  $keys: ['type', 'name'],

  type: types.int32,
  name: types.string
}

export const PsiEngine = {
  $keys: ['name', 'calib_mode', 'alias'],

  name: types.string,
  calib_mode: types.int32,
  alias: types.string
}

export const PsiSignal = {
  $keys: ['name', 'channel', 'calib_mode', 'engines'],

  name: types.string,
  channel: types.int32,
  calib_mode: types.int32,

  engines: types.arrayOf(PsiEngine)
}

export const Psi9000Cfg = {
  $MAX: 9000,
  $MIN: 2000,
  $keys: ['name', 'alia', 'over_sample', 'trigger', 'trig_divisor', 'euconv', 'euconv', 'zero_relay', 'signals'],

  name: types.string,
  alia: types.string,
  over_sample: types.int32,
  trigger: types.string,
  trig_divisor: types.int32,
  euconv: EuconvValue,
  zero_relay: types.string,

  signals: types.arrayOf(PsiSignal)
}

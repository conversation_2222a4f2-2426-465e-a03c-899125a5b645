# SDK 接口文档

## 文档索引

### 基础接口
- [IBase](IBase.md) - 基础接口，提供事件发射器和代理功能
- [ISdk](ISdk.md) - SDK 基础接口
- [ICfg](ICfg.md) - 配置接口
- [ISvc](ISvc.md) - 服务接口
- [IWin](IWin.md) - 窗口接口
- [IMenu](IMenu.md) - 菜单接口
- [IPage](IPage.md) - 页面接口

### 核心功能接口
- [IApp](IApp.md) - 应用程序接口
- [IEngine](IEngine.md) - 引擎接口
- [IDisplay](IDisplay.md) - 显示接口

### 类型定义
- [类型定义](type.md) - 系统使用的所有类型定义

## 接口关系图

```mermaid
graph TD
    IBase --> ISdk
    IBase --> ICfg
    IBase --> ISvc
    IBase --> IWin
    IBase --> IMenu
    IBase --> IPage
    IBase --> IApp
    IBase --> IEngine
    IBase --> IDisplay
    
    IApp --> IEngine
    IApp --> IDisplay
    IApp --> IMenu
    IApp --> IWin
    
    IEngine --> IDisplay
```

## 文档说明
1. 所有接口文档都包含以下部分：
   - 概述
   - 接口定义
   - 方法说明
   - 静态属性
   - 使用示例
   - 注意事项

2. 类型定义文档包含：
   - 基础类型
   - 配置接口
   - 枚举类型
   - 常量映射
   - 注意事项

3. 文档更新
   - 文档会随着代码的更新而更新
   - 保持与代码的同步
   - 确保文档的准确性和完整性 
$GROUP "temperature" 
CITOpenNum: opennumber(CITSwitch1 CITSwitch2 CITSwitch3 CITSwitch4 CITSwitch5 CITSwitch6 CITSwitch7 CITSwitch8 CITSwitch9 CITSwitch10 CITSwitch11 CITSwitch12)
CITDev: deviation(CITSwitch1 CalibCIT1 CITSwitch2 CalibCIT2 CITSwitch3 CalibCIT3 CITSwitch4 CalibCIT4 CITSwitch5 CalibCIT5 CITSwitch6 CalibCIT6 CITSwitch7 CalibCIT7 CITSwitch8 CalibCIT8 CITSwitch9 CalibCIT9 CITSwitch10 CalibCIT10 CITSwitch11 CalibCIT11 CITSwitch12 CalibCIT12)
CITAvg: avgvalue(CITSwitch1 CalibCIT1 CITSwitch2 CalibCIT2 CITSwitch3 CalibCIT3 CITSwitch4 CalibCIT4 CITSwitch5 CalibCIT5 CITSwitch6 CalibCIT6 CITSwitch7 CalibCIT7 CITSwitch8 CalibCIT8 CITSwitch9 CalibCIT9 CITSwitch10 CalibCIT10 CITSwitch11 CalibCIT11 CITSwitch12 CalibCIT12)
TBOpenNum: opennumber(BTSwitch1 BTSwitch2 BTSwitch3 BTSwitch4)
TBDev: deviation(BTSwitch1 CalibTB1 BTSwitch2 CalibTB2 BTSwitch3 CalibTB3 BTSwitch4 CalibTB4)
TBAvg: avgvalue(BTSwitch1 CalibTB1 BTSwitch2 CalibTB2 BTSwitch3 CalibTB3 BTSwitch4 CalibTB4)
EGTEngAvg: (CalibEgtEng1 + CalibEgtEng2) / 2
#if (CalibEgtEng1 > CalibEgtEng2) 
EGTEngDev: CalibEgtEng1 - CalibEgtEng2
#else
EGTEngDev: CalibEgtEng2 - CalibEgtEng1
#endif
EGTOpenNum: opennumber(EGTSwitch1 EGTSwitch2 EGTSwitch3 EGTSwitch4 EGTSwitch5 EGTSwitch6 EGTSwitch7 EGTSwitch8 EGTSwitch9 EGTSwitch10 EGTSwitch11 EGTSwitch12 EGTSwitch13)
EGTDev: deviation(EGTSwitch1 CalibEGT1 EGTSwitch2 CalibEGT2 EGTSwitch3 CalibEGT3 EGTSwitch4 CalibEGT4 EGTSwitch5 CalibEGT5 EGTSwitch6 CalibEGT6 EGTSwitch7 CalibEGT7 EGTSwitch8 CalibEGT8 EGTSwitch9 CalibEGT9 EGTSwitch10 CalibEGT10 EGTSwitch11 CalibEGT11 EGTSwitch12 CalibEGT12 EGTSwitch13 CalibEGT13)
EGTAvg: avgvalue(EGTSwitch1 CalibEGT1 EGTSwitch2 CalibEGT2 EGTSwitch3 CalibEGT3 EGTSwitch4 CalibEGT4 EGTSwitch5 CalibEGT5 EGTSwitch6 CalibEGT6 EGTSwitch7 CalibEGT7 EGTSwitch8 CalibEGT8 EGTSwitch9 CalibEGT9 EGTSwitch10 CalibEGT10 EGTSwitch11 CalibEGT11 EGTSwitch12 CalibEGT12 EGTSwitch13 CalibEGT13)
$END-GROUP
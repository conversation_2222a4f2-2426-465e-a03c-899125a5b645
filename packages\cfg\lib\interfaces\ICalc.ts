import { BaseItr, BaseMtr, IBase } from './IBase'
import { CalcWhenToExcuteType, CalcLineType, CfgFileType } from './type'

export interface CalcLineable {
  comments: string[]
  type: CalcLineType
  name: string
  value: string
  unit: string
}

export interface CalcGroupable {
  comments: string[]
  excute: CalcWhenToExcuteType
  name: string
  lines: CalcLineable[]
}

export interface CalcFile {
  name: string
  file: string
  type: CfgFileType
  depends: string[]
}

export interface CalcsOptions {
  files: CalcFile[]
}

export abstract class ICalc extends IBase<ICalc> {
  abstract readOptions(type?: CfgFileType): Promise<CalcsOptions>
  abstract removeFile(index: number, type?: CfgFileType): Promise<boolean>
  abstract addFile(val: CalcFile, index?: number, type?: CfgFileType): Promise<boolean>
  abstract modifyFile(index: number, val: Partial<CalcFile>, type?: CfgFileType): Promise<boolean>

  abstract loadCalc(
    file: string,
    type?: CfgFileType,
    force?: boolean
  ): Promise<CalcGroupable | undefined>
  abstract modifyCalc(
    file: string,
    val: Partial<CalcGroupable>,
    type?: CfgFileType
  ): Promise<boolean>
  abstract saveCalc(file: string, type?: CfgFileType): Promise<boolean>

  abstract loadCalcText(
    file: string,
    type?: CfgFileType,
    force?: boolean
  ): Promise<string | undefined>
  abstract saveCalcText(file: string, text: string, type?: CfgFileType): Promise<boolean>

  static override get NAME() {
    return 'Calc'
  }

  static get OnOptions() {
    return 'Calc.OnOptions'
  }

  static get OnCalc() {
    return 'Calc.OnCalc'
  }
}

export type CalcItr = BaseItr<ICalc>
export type CalcMtr = BaseMtr<ICalc>

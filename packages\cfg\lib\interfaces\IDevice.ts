import { BaseItr, BaseMtr, IBase } from './IBase'
import { PickField } from '../base'
import { MapProto } from '@wuk/wkp'
import { plcdriver } from '../proto'

export type PlcRangeValue = PickField<plcdriver.RangeValue, MapProto<plcdriver.RangeValue>>
export type PlcCalcLib = PickField<plcdriver.PlcCalcLib, MapProto<plcdriver.PlcCalcLib>>
export interface PlcSignal {
  name: string
  signal_range: PlcRangeValue
  units_range: PlcRangeValue
  type: string
  scale_range: PlcRangeValue
  scale_factor: number
  calib_mode: number
  comment: number
  calib: number
  calib_data: PlcCalcLib
  comments: string[]
}
export interface PlcSignals {
  input_float: boolean
  list: PlcSignal[]
}

export interface PlcDevice {
  type: number
  addr: number
  num: number
  signals: PlcSignals
}

export interface PlcCfgOption {
  device_name: string
  scan_rate: number
  devices: PlcDevice[]
}

export interface PlcDriverOptions {
  scan_rate: number
  plcs: PlcCfgOption[]
}

export interface VibSystemOption {
  /**
   * Measurement rate, 0 - 10 Hz
   */
  measurement_rate: number
  /**
   * Bandwidth
   * 0 = 500 Hz
   * 1 = 1000 Hz
   * 2 = 2000 Hz
   * 3 = 5000 Hz
   * 4 = 10000 Hz
   */
  band_width: number
  /**
   * FFT Lines
   * 0 = 200 lines
   * 1 = 400 lines
   * 2 = 800 lines
   * 3 = 1600 lines
   */
  fft_lines: number
}

export interface VibInputOption {
  /**
   * Sensitivity (pC/g)
   * Transducer sensitivity, 1.0 to 2000.0
   */
  sensitivity: number
  /**
   * Gain of amplifier, 0, 1, or 10
   * 0 = Vib. System will select gain
   * 1 = Gain of 1
   * 10 = Gain of 10
   */
  gain: number
  /**
   * Expected input range, 0.1 to 150.0 units
   */
  input_range: number
  /**
   * Transducer type
   * 0 = Disabled
   * 1 = Single Ended Accel
   * 2 = Differential Accel
   * 3 = Velocity
   * 4 = ICP Accel
   */
  transducer_type: number
  /**
   * A/D gain,
   * 0 = Auto Select
   * 1 = 10.00 volts
   * 2 = 5.00 volts
   * 4 = 2.50 volts
   * 8 = 1.25 volts
   */
  ad_gain: number
  /**
   * Fast Fourier Transform (FFT)
   */
  fft_size: number
  /**
   * Sampling Rate
   */
  sampling_rate: number
}

export interface VibBandFilterOption {
  /**
   * 1 to 4, Selects input channel to filter
   */
  vib_channel: number
  /**
   * 0 to 3
   * 0 = Disables Filter
   * 1 = Acceleration (g's) Output
   * 2 = Velocity (IPS) Output
   * 3 = Displacement (Mils) Output
   * 4 = Acceleration (m/sec2) Output
   * 5 = Velocity (mm/sec) Output
   * 6 = Displacement (um) Output
   */
  output_units: number
  /**
   * 0 to 3
   * 0 = Peak Detector
   * 2 = RMS Detector
   * 3 = Average Detector
   */
  detector_type: number
  /**
   * 0 to 6
   * 0 = No Smoothing
   * 1 = 0.25 Sec Smoothing
   * 2 = 0.50 Sec Smoothing
   * 3 = 0.75 Sec Smoothing
   * 4 = 1.00 Sec Smoothing
   * 5 = 1.25 Sec Smoothing
   * 6 = 1.50 Sec Smoothing
   */
  time_constant: number
  /**
   * 0 to 3
   * 0 = Rectangular
   * 1 = 7 Pole Chebyshev
   * 2 = 6 Pole Butterworth
   * 3 = User Defined
   */
  filter_shape: number
  /**
   * 0 to 3
   * 0 = No Filtering
   * 1 = Low Pass Filter
   * 2 = High Pass Filter
   * 3 = Band Pass Filter
   */
  filter_type: number
  /**
   * 2 to 10000, Upper Cutoff of filter
   */
  upper_cutoff: number
  /**
   * 1 to 999, Lower Cutoff of filter
   */
  lower_cutoff: number
}

export interface VibTachInputOption {
  /**
   * 0 to 100,000, 100% Rotor Speed in RPM
   */
  rotor_speed: number
  /**
   * 1 to 100,000, 100% Tach. Frequency in Hz
   */
  tach_freq: number
  /**
   * Low amplitude shutdown (Vp-p), 0.0 to 1.1
   */
  sensitivity: number
  /**
   * 0 to 3, Selects 1/Rev tach. processing
   * 0 = Disabled
   * 1 = Encode High Pulse
   * 2 = Encode Low Pulse
   * 3 = Single Pulse or Strobe
   */
  tach_processing: number
}

export interface VibTFInputOption {
  /**
   * 1 to 4, Selects Input channel to filter
   */
  vib_channel: number
  /**
   * 1 to 4, Selects Tach. input to track
   */
  tach_channel: number
  /**
   * 0 to 3
   * 0 = Disables Filter
   * 1 = Acceleration (g's) Output
   * 2 = Velocity (IPS) Output
   * 3 = Displacement (Mils) Output
   * 4 = Acceleration (m/sec2) Output
   * 5 = Velocity (mm/sec) Output
   * 6 = Displacement (um) Output
   */
  output_units: number
  /**
   * 0 to 3
   * 0 = Peak Detector
   * 1 = Peak to Peak Detector
   * 2 = RMS Detector
   * 3 = Average Detector
   */
  detector_type: number
  /**
   * 0.1 to 150.0, Full Scale Output
   * in detector units
   */
  full_scale_units: number
  /**
   * 1.0 to 10.0, Full Scale Output
   * in Volts
   */
  full_scale_volts: number
  /**
   * 0 to 1
   * 0 = Constant Bandwidth
   * 1 = Constant Q
   */
  filter_specification: number
  /**
   * 10, 20, or 30
   * 10 = Q of 10
   * 20 = Q of 20
   * 30 = Q of 20
   */
  filter_q: number
  /**
   * 5 or 11
   * 5 = 5 Hz Bandwidth
   * 11 = 11 Hz Bandwidth
   */
  band_width: number
  /**
   * 0 to 6
   * 1 = 0.25 Sec Smoothing
   * 2 = 0.50 Sec Smoothing
   * 3 = 0.75 Sec Smoothing
   * 4 = 1.00 Sec Smoothing
   * 5 = 1.25 Sec Smoothing
   * 6 = 1.50 Sec Smoothing
   */
  time_constant: number
  /**
   * 0.1 to 2000.0, Order of tach
   * signal to track against
   */
  order_tracking: number
}

export interface VibSpectrumOption {
  /**
   * 0 to 6
   * 0 = Disables Filter
   * 1 = Acceleration (g's) Output
   * 2 = Velocity (IPS) Output
   * 3 = Displacement (Mils) Output
   * 4 = Acceleration (m/sec2) Output
   * 5 = Velocity (mm/sec) Output
   * 6 = Displacement (um) Output
   */
  output_units: number
  /**
   * 0 to 9999, Spectrum range starting freq, must be less than End_Frequency.
   */
  start_frequency: number
  /**
   * 1 to 10000, Spectrum range ending freq, cannot exceed system bandwidth
   */
  end_frequency: number
  /**
   * 0 to 3
   * 0 = Peak Detector
   * 1 = Peak to Peak Detector
   * 2 = RMS Detector
   * 3 = Average Detector
   */
  detector_type: number
  /**
   * 0.1 to 150.0, Full Scale Output
   * in detector units
   */
  full_scale_units: number
}

export interface VibSignalOption {
  /**
   * Sig Name
   */
  name: string
  /**
   * Filter Type -> SignalFilterType
   */
  filter_type: number
  /**
   * Sig Type -> SignalSigType
   */
  sig_type: number
  /**
   * Calib Mode -> SignalCalibMode
   */
  calib_mode: number
}

export interface SetupOptions {
  /**
   * Setup Name
   */
  name: string
  /**
   * Setup Exp
   */
  exp: string
  /**
   * Setup File
   */
  file?: string
}

export abstract class IDevice extends IBase<IDevice> {
  abstract readSetups(): Promise<Array<SetupOptions>>
  abstract removeSetup(index: number): Promise<boolean>
  abstract addSetup(val: SetupOptions, index?: number): Promise<boolean>
  abstract modifySetup(index: number, val: Partial<SetupOptions>): Promise<boolean>

  // Vibration System Setup
  abstract readVibSystem(index: number): Promise<VibSystemOption>
  abstract writeVibSystem(index: number, val: Partial<VibSystemOption>): Promise<boolean>

  // Vibration Input Setup
  abstract readVibInput(index: number): Promise<Array<VibInputOption>>
  abstract writeVibInput(index: number, idx: number, val: Partial<VibInputOption>): Promise<boolean>

  // BROAD BAND FILTER SETUP
  abstract readVibBandFilter(index: number): Promise<Array<VibBandFilterOption>>
  abstract writeVibBandFilter(
    index: number,
    idx: number,
    val: Partial<VibBandFilterOption>
  ): Promise<boolean>

  // TACHOMETER INPUT SETUP
  abstract readVibTachInput(index: number): Promise<Array<VibTachInputOption>>
  abstract writeVibTachInput(
    index: number,
    idx: number,
    val: Partial<VibTachInputOption>
  ): Promise<boolean>

  // TRACKING FILTER INPUT SETUP
  abstract readVibTFInput(index: number): Promise<Array<VibTFInputOption>>
  abstract writeVibTFInput(
    index: number,
    idx: number,
    val: Partial<VibTFInputOption>
  ): Promise<boolean>

  // SPECTRUM ANALYSIS SETUP
  abstract readVibSpectrum(index: number): Promise<Array<VibSpectrumOption>>
  abstract writeVibSpectrum(
    index: number,
    idx: number,
    val: Partial<VibSpectrumOption>
  ): Promise<boolean>

  /**
   * Vibration Signals
   */
  abstract readVibSignals(): Promise<Array<VibSignalOption>>
  abstract removeVibSignal(index: number): Promise<boolean>
  abstract addVibSignal(val: VibSignalOption, index?: number): Promise<boolean>
  abstract modifyVibSignal(index: number, val: Partial<VibSignalOption>): Promise<boolean>

  /**
   * PLC Setup
   */
  abstract readPLCSetups(): Promise<PlcDriverOptions>
  abstract modifyPLCSetup(index: number, val: Partial<PlcCfgOption>): Promise<boolean>

  static override get NAME() {
    return 'Device'
  }

  static get OnSetupOptions() {
    return 'Device.OnSetupOptions'
  }

  static get OnVibSystemOption() {
    return 'Device.VibSystemOption'
  }

  static get OnVibInputOption() {
    return 'Device.OnVibInputOption'
  }

  static get OnVibBandFilterOption() {
    return 'Device.OnVibBandFilterOption'
  }

  static get OnVibTachInputOption() {
    return 'Device.OnVibTachInputOption'
  }

  static get OnVibTFInputOption() {
    return 'Device.OnVibTFInputOption'
  }

  static get OnVibSpectrumOption() {
    return 'Device.OnVibSpectrumOption'
  }

  static get OnVibSignalOption() {
    return 'Device.OnVibSignalOption'
  }

  static get OnPLCSetupOption() {
    return 'Device.OnPLCSetupOption'
  }
}

export type DeviceItr = BaseItr<IDevice>
export type DeviceMtr = BaseMtr<IDevice>

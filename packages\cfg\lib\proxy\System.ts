import { BrowserWindow } from 'electron'
import {
  AppEngine,
  AudioOptions,
  AudoItem,
  ColorOptions,
  CRSOptions,
  CrtItem,
  CrtOptions,
  DataFormatOptions,
  DateFormatType,
  DeviceOptions,
  ICfg,
  InputParamOrderType,
  ISystem,
  kCrtControlVal,
  kCrtStatusVal,
  kCrtValControl,
  kCrtValStatus,
  kDateFormatVal,
  kInputParamOrderVal,
  kTimeFormatVal,
  kValDateFormat,
  kValTimeFormat,
  PrintModeOptions,
  ResolutionItem,
  ResolutionOptions,
  RgbItem,
  SystemOptions,
  TestHistoryType,
  TimeFormatType
} from '../interfaces'
import { BaseProxy, method } from './Base'

import { audios, cust, rgbs } from '../proto'
import { Hardware } from 'lib/proto/cust'

export class System extends BaseProxy<ISystem> implements ISystem {
  private _engines: Map<string, AppEngine>

  private _options: SystemOptions
  private _prints: PrintModeOptions
  private _formats: DataFormatOptions
  private _crs: CRSOptions
  private _crts: CrtOptions
  private _devices: DeviceOptions
  private _colors: ColorOptions
  private _audios: AudioOptions
  private _resolutions: ResolutionOptions

  private _operators: string[]
  private _functions: string[]

  constructor(private readonly cfg: ICfg, win?: BrowserWindow, master = false) {
    super(ISystem.NAME, master, win)

    this._engines = new Map()

    this._options = {
      initial_alarm_state: true,
      input_param_order: InputParamOrderType.AsListed,
      reference_test: false,
      automatic_open_on_startup: false,
      store_messages_to_database: true,
      rcs_control: false,
      test_history: TestHistoryType.Standard,
      invalid_value: '',
      test_id_prompt: '',
      system_scan_rate: '',
      trigger_control_paramter: ''
    }

    this._prints = {
      print_messages: false,
      print_stored_scans: false,
      print_stored_comments: false,
      print_stored_displays: false
    }

    this._formats = {
      time_format: TimeFormatType.Standard,
      date_format: DateFormatType.DDMMYY,
      param_color: ''
    }

    this._crs = {
      auto_backup: false,
      recording_control: false,
      maximum_number_of_tests: '',
      disk_space_to_leave_free: '',
      file_system: '',
      continuous_recording: {
        is_on: false,
        number_of_mins: 0,
        division_of_scan_rate: 0
      },
      cyclic_recording: {
        is_on: false,
        number_of_mins: 0,
        division_of_scan_rate: 0
      }
    }
    this._resolutions = {
      list: [
        {
          name: 'MEDRES',
          width: 1280,
          height: 1024
        },
        {
          name: 'WUXGA',
          width: 1920,
          height: 1200
        },
        {
          name: 'FHD',
          width: 1920,
          height: 1080
        },
        {
          name: 'HIRES',
          width: 1600,
          height: 1200
        },
        {
          name: '1280:576',
          width: 1280,
          height: 576
        },
        {
          name: '934:448',
          width: 934,
          height: 448
        },
        {
          name: '346:448',
          width: 346,
          height: 448
        },
        {
          name: '1280:448',
          width: 1280,
          height: 448
        }
      ]
    }
    this._operators = [
      '+',
      '-',
      '/',
      '*',
      '^',
      '(',
      ')',
      '&',
      '|',
      '=',
      '<',
      '>',
      '<=',
      '>=',
      '~',
      '%'
    ]
    this._functions = [
      'Compare',
      'lookup',
      'abs',
      'psi2inhg',
      'psi2mbar',
      'inhg2psi',
      'temp_f2c',
      'temp_c2f',
      'temp_f2r',
      'temp_r2f',
      'temp_c2k',
      'temp_k2c',
      'pints2gals',
      'pints2liters',
      'pints2qts',
      'gals2pints',
      'gals2liters',
      'gals2qts',
      'liters2pints',
      'liters2gals',
      'liters2qts',
      'qts2pints',
      'qts2gals',
      'qts2liters',
      'calchumidity',
      'shum_gpp',
      'calcwf',
      'fueldensity',
      'pct2value',
      'value2pct',
      'smoothsignal',
      'stable',
      'evalratio',
      'max',
      'min',
      'inrange',
      'curveeval',
      'concat',
      'bittest',
      'bitset',
      'bitclear',
      'setbits',
      'setecutime',
      'setecudate',
      'decodebcd',
      'log',
      'fuelused',
      'wave',
      'decodebits',
      'maxlookup',
      'minlookup',
      'airmassflow',
      'StoreEvent',
      'setxvalue',
      'fluidmassflow',
      'startautotest',
      'changedisplay',
      'startcontrolloop',
      'stopcontrolloop',
      'modifycontrolloop',
      'debugcontrolloop',
      'calcpga',
      'calcfuelflow',
      'grns2lbs',
      'lbs2grns',
      'retwfinterm',
      'averagen',
      'calcfuelflow2',
      'setarincsdi',
      'setarincssm',
      'fueldensitycorr',
      'average',
      'set_valid',
      'set_invalid',
      'lookupat',
      'roundup',
      'temp_c2r',
      'temp_r2c',
      'sqrt'
    ]
    this._devices = {
      list: []
    }

    this._crts = {
      rate: 0,
      list: []
    }

    this._colors = {
      list: []
    }

    this._audios = {
      audio_host_address: '',
      list: []
    }
  }

  get engines() {
    return this._engines
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readSystemOptions() {
    return this._options
  }

  @method()
  async writeSystemOptions(val: Partial<SystemOptions>) {
    if (val.initial_alarm_state !== undefined)
      this._options.initial_alarm_state = val.initial_alarm_state
    if (val.input_param_order !== undefined) this._options.input_param_order = val.input_param_order
    if (val.reference_test !== undefined) this._options.reference_test = val.reference_test
    if (val.automatic_open_on_startup !== undefined)
      this._options.automatic_open_on_startup = val.automatic_open_on_startup
    if (val.store_messages_to_database !== undefined)
      this._options.store_messages_to_database = val.store_messages_to_database
    if (val.rcs_control !== undefined) this._options.rcs_control = val.rcs_control
    if (val.test_history !== undefined) this._options.test_history = val.test_history
    if (val.invalid_value !== undefined) this._options.invalid_value = val.invalid_value
    if (val.test_id_prompt !== undefined) this._options.test_id_prompt = val.test_id_prompt
    if (val.system_scan_rate !== undefined) this._options.system_scan_rate = val.system_scan_rate
    if (val.trigger_control_paramter !== undefined)
      this._options.trigger_control_paramter = val.trigger_control_paramter

    const changed = await this.writeSystemsCache()
    if (changed) {
      this.emit(ISystem.OnSystemOptions)
    }

    return changed
  }

  private async writeSystemsCache() {
    const { test_history } = this._options
    let changed = this.cfg.assign(cust.Cust, {
      ...this._options,
      test_history: test_history === TestHistoryType.Standard
    })
    changed = changed && (await this.cfg.write(cust.Cust))

    return changed
  }

  @method()
  async readPrintModes() {
    return this._prints
  }

  @method()
  async writePrintModes(val: Partial<PrintModeOptions>) {
    if (val.print_messages !== undefined) this._prints.print_messages = val.print_messages
    if (val.print_stored_scans !== undefined)
      this._prints.print_stored_scans = val.print_stored_scans
    if (val.print_stored_comments !== undefined)
      this._prints.print_stored_comments = val.print_stored_comments
    if (val.print_stored_displays !== undefined)
      this._prints.print_stored_displays = val.print_stored_displays

    const changed = await this.writePrintsCache()
    if (changed) {
      this.emit(ISystem.OnPrintModes)
    }

    return changed
  }

  private async writePrintsCache() {
    let changed = this.cfg.assign(cust.Cust, this._prints)
    changed = changed && (await this.cfg.write(cust.Cust))

    return changed
  }

  @method()
  async readDataFormat() {
    return this._formats
  }

  @method()
  async writeDataFormat(val: Partial<DataFormatOptions>) {
    if (val.time_format !== undefined) this._formats.time_format = val.time_format
    if (val.date_format !== undefined) this._formats.date_format = val.date_format
    if (val.param_color !== undefined) this._formats.param_color = val.param_color

    const changed = await this.writeFormatCache()
    if (changed) {
      this.emit(ISystem.OnDataFormat)
    }

    return changed
  }

  private async writeFormatCache() {
    const { param_color, date_format, time_format } = this._formats
    let changed = this.cfg.assign(cust.Cust, {
      param_color,
      date_format: kValDateFormat[date_format],
      time_format: kValTimeFormat[time_format]
    })
    changed = changed && (await this.cfg.write(cust.Cust))

    return changed
  }

  @method()
  async readCRSOptions() {
    return this._crs
  }

  @method()
  async writeCRSOptions(val: Partial<CRSOptions>) {
    if (val.auto_backup !== undefined) this._crs.auto_backup = val.auto_backup
    if (val.recording_control !== undefined) this._crs.recording_control = val.recording_control
    if (val.maximum_number_of_tests !== undefined)
      this._crs.maximum_number_of_tests = val.maximum_number_of_tests
    if (val.disk_space_to_leave_free !== undefined)
      this._crs.disk_space_to_leave_free = val.disk_space_to_leave_free
    if (val.file_system !== undefined) this._crs.file_system = val.file_system
    if (val.continuous_recording !== undefined)
      this._crs.continuous_recording = val.continuous_recording
    if (val.cyclic_recording !== undefined) this._crs.cyclic_recording = val.cyclic_recording

    const changed = await this.writeCRSCache()
    if (changed) {
      this.emit(ISystem.OnCRSOptions)
    }

    return changed
  }

  private async writeCRSCache() {
    const { cyclic_recording, continuous_recording } = this._crs
    const crs = new cust.Crs({
      ...this._crs,
      cyclic_recording: new cust.CrsRecording(cyclic_recording),
      continuous_recording: new cust.CrsRecording(continuous_recording)
    })

    let changed = this.cfg.assign(cust.Cust, { crs })
    changed = changed && (await this.cfg.write(cust.Cust))

    return changed
  }

  @method()
  async readCrtOptions() {
    return this._crts
  }

  @method()
  async setCrtRate(rate: number) {
    this._crts.rate = rate

    const changed = await this.writeCrtCache()
    if (changed) {
      this.emit(ISystem.OnCrtOptions)
    }

    return changed
  }

  @method()
  async removeCrt(index: number) {
    this._crts.list.splice(index, 1)

    const changed = await this.writeCrtCache()
    if (changed) {
      this.emit(ISystem.OnCrtOptions)
    }

    return changed
  }

  @method()
  async addCrt(val: CrtItem, index?: number) {
    if (index === undefined || index === null) {
      this._crts.list.push(val)
    } else {
      this._crts.list.splice(index, 0, val)
    }

    const changed = await this.writeCrtCache()
    if (changed) {
      this.emit(ISystem.OnCrtOptions)
    }

    return changed
  }

  @method()
  async modifyCrt(index: number, val: Partial<CrtItem>) {
    if (this._crts.list.length <= index) {
      return false
    }
    const item = this._crts.list[index]
    if (val.name !== undefined) item.name = val.name
    if (val.x_address !== undefined) item.x_address = val.x_address
    if (val.status !== undefined) item.status = val.status
    if (val.resolution !== undefined) item.resolution = val.resolution
    if (val.control !== undefined) item.control = val.control

    const changed = await this.writeCrtCache()
    if (changed) {
      this.emit(ISystem.OnCrtOptions)
    }

    return changed
  }

  private async writeCrtCache() {
    const rate = this._crts.rate
    const list: cust.CustCrt[] = this._crts.list.map(
      ({ name, x_address, status, resolution, control }) => {
        return new cust.CustCrt({
          name,
          x_address,
          status: kCrtValStatus[status],
          resolution,
          control: kCrtValControl[control]
        })
      }
    )

    const crt = new cust.CustCrts({ rate, list })
    let changed = this.cfg.assign(cust.Cust, { crt })
    changed = changed && (await this.cfg.write(cust.Cust))

    return changed
  }

  @method()
  async readDeviceOptions() {
    return this._devices
  }

  @method()
  async removeDevice(index: number) {
    this._devices.list.splice(index, 1)

    const changed = await this.writeDeviceCache()
    if (changed) {
      this.emit(ISystem.OnDeviceOptions)
    }

    return changed
  }

  @method()
  async addDevice(val: Hardware, index?: number) {
    if (index === undefined || index === null) {
      this._devices.list.push(val)
    } else {
      this._devices.list.splice(index, 0, val)
    }

    const changed = await this.writeDeviceCache()
    if (changed) {
      this.emit(ISystem.OnDeviceOptions)
    }

    return changed
  }

  @method()
  async modifyDevice(index: number, val: Partial<Hardware>) {
    if (this._devices.list.length <= index) {
      return false
    }
    const item = this._devices.list[index]
    if (val.id !== undefined) item.id = val.id
    if (val.name !== undefined) item.name = val.name
    if (val.inter !== undefined) item.inter = val.inter
    if (val.printer_type !== undefined) item.printer_type = val.printer_type
    if (val.scan_rate !== undefined) item.scan_rate = val.scan_rate
    if (val.inst_addr !== undefined) item.inst_addr = val.inst_addr

    const changed = await this.writeDeviceCache()
    if (changed) {
      this.emit(ISystem.OnDeviceOptions)
    }

    return changed
  }

  private async writeDeviceCache() {
    const hardware_list: cust.Hardware[] = this._devices.list.map(val => new cust.Hardware(val))
    let changed = this.cfg.assign(cust.Cust, { hardware_list })
    changed = changed && (await this.cfg.write(cust.Cust))

    return changed
  }

  @method()
  async readColorOptions() {
    return this._colors
  }

  @method()
  async removeColor(index: number) {
    this._colors.list.splice(index, 1)

    const changed = await this.writeColorsCache()
    if (changed) {
      this.emit(ISystem.OnColorOptions)
    }

    return changed
  }

  @method()
  async addColor(val: RgbItem, index?: number) {
    if (index === undefined || index === null) {
      this._colors.list.push(val)
    } else {
      this._colors.list.splice(index, 0, val)
    }

    const changed = await this.writeColorsCache()
    if (changed) {
      this.emit(ISystem.OnColorOptions)
    }

    return changed
  }

  @method()
  async modifyColor(index: number, val: Partial<RgbItem>) {
    if (this._colors.list.length <= index) {
      return false
    }
    const item = this._colors.list[index]
    if (val.name !== undefined) item.name = val.name
    if (val.r !== undefined) item.r = Number(val.r || 0) % 256
    if (val.g !== undefined) item.g = Number(val.g || 0) % 256
    if (val.b !== undefined) item.b = Number(val.b || 0) % 256
    if (val.a !== undefined) item.a = Number(val.a || 255) % 256
    if (val.name !== undefined) item.name = val.name

    const changed = await this.writeColorsCache()
    if (changed) {
      this.emit(ISystem.OnColorOptions)
    }

    return changed
  }

  private async writeColorsCache() {
    const list: rgbs.Rgb[] = this._colors.list.map(val => new rgbs.Rgb(val))
    let changed = this.cfg.assign(rgbs.Rgbs, { rgbs: list })
    changed = changed && (await this.cfg.write(rgbs.Rgbs))

    return changed
  }

  @method()
  async readAudioOptions() {
    return this._audios
  }

  @method()
  async setHostAddress(val: string) {
    this._audios.audio_host_address = val

    const changed = await this.writeAudioCache()
    if (changed) {
      this.emit(ISystem.OnAudioOptions)
    }

    return changed
  }

  @method()
  async removeAudio(index: number) {
    this._audios.list.splice(index, 1)

    const changed = await this.writeAudioCache()
    if (changed) {
      this.emit(ISystem.OnAudioOptions)
    }

    return changed
  }

  @method()
  async addAudio(val: AudoItem, index?: number) {
    if (index === undefined || index === null) {
      this._audios.list.push(val)
    } else {
      this._audios.list.splice(index, 0, val)
    }

    const changed = await this.writeAudioCache()
    if (changed) {
      this.emit(ISystem.OnAudioOptions)
    }

    return changed
  }

  @method()
  async modifyAudio(index: number, val: Partial<AudoItem>) {
    if (this._audios.list.length <= index) {
      return false
    }
    const item = this._audios.list[index]
    if (val.name !== undefined) item.name = val.name
    if (val.file !== undefined) item.file = val.file
    if (val.repeat !== undefined) item.repeat = val.repeat
    if (val.priority !== undefined) item.priority = val.priority

    const changed = await this.writeAudioCache()
    if (changed) {
      this.emit(ISystem.OnAudioOptions)
    }

    return changed
  }

  private async writeAudioCache() {
    const path = this._audios.audio_host_address
    const list: audios.Audio[] = this._audios.list.map(val => new audios.Audio(val))
    let changed = this.cfg.assign(audios.Audios, { audios: list, path })
    changed = changed && (await this.cfg.write(audios.Audios))

    return changed
  }

  @method()
  async readResolutionOptions() {
    return this._resolutions
  }

  @method()
  async removeResolution(index: number) {
    this._resolutions.list.splice(index, 1)

    const changed = await this.writeResolutionCache()
    if (changed) {
      this.emit(ISystem.OnResolutionOptions)
    }

    return changed
  }

  @method()
  async addResolution(val: ResolutionItem, index?: number) {
    if (index === undefined || index === null) {
      this._resolutions.list.push(val)
    } else {
      this._resolutions.list.splice(index, 0, val)
    }

    const changed = await this.writeResolutionCache()
    if (changed) {
      this.emit(ISystem.OnResolutionOptions)
    }

    return changed
  }

  @method()
  async modifyResolution(index: number, val: Partial<ResolutionItem>) {
    if (this._resolutions.list.length <= index) {
      return false
    }
    const item = this._resolutions.list[index]
    if (val.name !== undefined) item.name = val.name
    if (val.width !== undefined) item.width = val.width
    if (val.height !== undefined) item.height = val.height

    const changed = await this.writeResolutionCache()
    if (changed) {
      this.emit(ISystem.OnResolutionOptions)
    }

    return changed
  }

  private async writeResolutionCache() {
    const list = this._resolutions.list
    const changed = true // this.cfg.assign(audios.Audios, { audios: list })
    if (changed) {
      // await this.cfg.write(audios.Audios)
    }
    return changed
  }

  private async loadColors(force?: boolean) {
    const val: Partial<rgbs.Rgbs> = (await this.cfg.read(rgbs.Rgbs, undefined, force)) || {}
    this._colors.list = val.rgbs || []
    this.log('loadColors====== colors={%1}', val)

    this.emit(ISystem.OnColorOptions)
  }

  private async loadAudios(force?: boolean) {
    const val: Partial<audios.Audios> = (await this.cfg.read(audios.Audios, undefined, force)) || {}
    this._audios.audio_host_address = val.path || ''
    this._audios.list = val.audios || []
    this.log('loadAudios====== val={%1}', val)

    this.emit(ISystem.OnAudioOptions)
  }

  private async loadAppCust(force?: boolean) {
    const {
      customer_name = '',
      engine_list = [],
      date_format = '',
      time_format = '',
      reference_test = false,
      shared_mem = '',
      input_param_order = '',
      param_color = '',
      system_scan_rate = '',
      automatic_open_on_startup = false,
      rcs_control = false,
      hardware_list = [],
      print_messages = false,
      print_stored_comments = false,
      print_stored_displays = false,
      print_stored_scans = false,
      initial_alarm_state = true,
      invalid_value = '',
      test_history = true,
      store_messages_to_database = true,
      trigger_control_paramter = '',
      test_id_prompt = '',
      crs,
      crt
    } = (await this.cfg.read(cust.Cust, undefined, force)) || {}
    // this._customer = customer_name

    {
      this._engines.clear()
      engine_list.forEach(item => this._engines.set(item.name, item))
    }

    {
      this._devices.list = hardware_list
      this.emit(ISystem.OnDeviceOptions)
    }

    if (crs) {
      this._crs = crs
      this.emit(ISystem.OnCRSOptions)
    }

    if (crt) {
      this._crts.rate = crt.rate
      this._crts.list = crt.list.map(({ name, x_address, status, resolution, control }) => {
        return {
          name,
          x_address,
          status: kCrtStatusVal[status],
          resolution,
          control: kCrtControlVal[control]
        }
      })
      this.emit(ISystem.OnCrtOptions)
    }

    this._formats.date_format = kDateFormatVal[date_format] || DateFormatType.DDMMYY
    this._formats.time_format = kTimeFormatVal[time_format] || TimeFormatType.Standard
    this._formats.param_color = param_color || ''

    this._options.reference_test = reference_test
    this._options.system_scan_rate = system_scan_rate
    this._options.test_id_prompt = test_id_prompt
    this._options.rcs_control = rcs_control
    this._options.automatic_open_on_startup = automatic_open_on_startup
    this._options.input_param_order =
      kInputParamOrderVal[input_param_order] || InputParamOrderType.AsListed
    this._options.initial_alarm_state = initial_alarm_state
    this._options.test_history =
      (test_history && TestHistoryType.Standard) || TestHistoryType.NoHistoryData
    this._options.invalid_value = invalid_value
    this._options.trigger_control_paramter = trigger_control_paramter
    this._options.store_messages_to_database = store_messages_to_database

    this._prints.print_messages = print_messages
    this._prints.print_stored_comments = print_stored_comments
    this._prints.print_stored_displays = print_stored_displays
    this._prints.print_stored_scans = print_stored_scans

    this.log('loadAppCust====== engines={%1}', this._engines)

    this.emit(ISystem.OnChanged)
  }

  private async loadOptions(force?: boolean) {
    await this.loadAppCust(force)
    await this.loadColors(force)
    await this.loadAudios(force)
  }

  private async clearOptions() {}
}

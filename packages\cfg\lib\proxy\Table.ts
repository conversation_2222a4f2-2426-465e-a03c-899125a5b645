import { <PERSON><PERSON><PERSON><PERSON>indow } from 'electron'
import {
  ICfg,
  IEngine,
  ITable,
  TableCfg,
  TableData,
  TableGroup,
  TableLib,
  TablesOptions
} from '../interfaces'
import { BaseProxy, method } from './Base'

import { tablecfg, tables } from '../proto'

export class Table extends BaseProxy<ITable> implements ITable {
  private _options: TablesOptions

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(ITable.NAME, master, win)

    this._options = {
      groups: []
    }
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readOptions(): Promise<TablesOptions> {
    return this._options
  }

  @method()
  async removeGroup(index: number): Promise<boolean> {
    this._options.groups.splice(index, 1)

    const changed = await this.writeTablesCache()
    changed && this.emit(ITable.OnTablesOptions)

    return changed
  }

  @method()
  async addGroup(val: TableGroup, index?: number): Promise<boolean> {
    if (index === undefined || index === null) {
      this._options.groups.push(val)
    } else {
      this._options.groups.splice(index, 0, val)
    }

    const changed = await this.writeTablesCache()
    changed && this.emit(ITable.OnTablesOptions)

    return changed
  }

  @method()
  async modifyGroup(index: number, val: Partial<TableGroup>): Promise<boolean> {
    if (this._options.groups.length <= index) {
      return false
    }
    const item = this._options.groups[index]
    if (val.name !== undefined) item.name = val.name
    if (val.tables !== undefined) item.tables = val.tables

    const changed = await this.writeTablesCache()
    changed && this.emit(ITable.OnTablesOptions)

    return changed
  }

  @method()
  async removeTable(groupName: string, index: number): Promise<boolean> {
    const group = this._options.groups.find(group => group.name === groupName)
    if (!group || group.tables.length <= index) {
      return false
    }
    group.tables.splice(index, 1)

    const changed = await this.writeTablesCache()
    changed && this.emit(ITable.OnTablesOptions)

    return changed
  }

  @method()
  async addTable(groupName: string, val: TableData, index?: number): Promise<boolean> {
    const group = this._options.groups.find(group => group.name === groupName)
    if (!group) {
      return false
    }
    if (index === undefined || index === null) {
      group.tables.push(val)
    } else {
      group.tables.splice(index, 0, val)
    }

    const changed = await this.writeTablesCache()
    changed && this.emit(ITable.OnTablesOptions)

    return changed
  }

  @method()
  async modifyTable(groupName: string, index: number, val: Partial<TableData>): Promise<boolean> {
    const group = this._options.groups.find(group => group.name === groupName)
    if (!group || group.tables.length <= index) {
      return false
    }
    const item = group.tables[index]
    if (val.name !== undefined) item.name = val.name
    if (val.type !== undefined) item.type = val.type
    if (val.save !== undefined) item.save = val.save
    if (val.dynexp !== undefined) item.dynexp = val.dynexp
    if (val.exp !== undefined) item.exp = val.exp
    if (val.size !== undefined) item.size = val.size
    if (val.ascii !== undefined) item.ascii = val.ascii
    if (val.format !== undefined) item.format = val.format
    if (val.xparam_id !== undefined) item.xparam_id = val.xparam_id
    if (val.yparam_id !== undefined) item.yparam_id = val.yparam_id
    if (val.zparam_id !== undefined) item.zparam_id = val.zparam_id
    if (val.constant !== undefined) item.constant = val.constant
    if (val.interp !== undefined) item.interp = val.interp
    if (val.offset !== undefined) item.offset = val.offset
    if (val.extrap !== undefined) item.extrap = val.extrap
    if (val.extrap_const !== undefined) item.extrap_const = val.extrap_const
    if (val.coeff !== undefined) item.coeff = val.coeff
    if (val.maxpoly !== undefined) item.maxpoly = val.maxpoly
    if (val.minpoly !== undefined) item.minpoly = val.minpoly
    if (val.revdate !== undefined) item.revdate = val.revdate
    if (val.mandate !== undefined) item.mandate = val.mandate
    if (val.manrev !== undefined) item.manrev = val.manrev
    if (val.comment !== undefined) item.comment = val.comment

    const changed = await this.writeTablesCache()
    changed && this.emit(ITable.OnTablesOptions)

    return changed
  }

  @method()
  async readTableCfg(groupName: string): Promise<TableCfg | undefined> {
    const cfg = await this.loadTableCfg(groupName)
    return (
      (cfg && {
        name: cfg.name,
        tables: cfg.tables.map(el => {
          return {
            name: el.name,
            dim: el.dim,
            data: el.data
          }
        })
      }) ||
      undefined
    )
  }

  @method()
  async modifyTableCfg(groupName: string, val: Partial<TableCfg>): Promise<boolean> {
    const cfg = await this.loadTableCfg(groupName)
    if (!cfg) {
      return false
    }
    if (val.name !== undefined) cfg.name = val.name
    if (val.tables !== undefined) {
      cfg.tables = val.tables.map(el => {
        return new tablecfg.TableLib(el)
      })
    }
    const changed = await this.writeTableCfg(groupName, cfg)
    changed && this.emit(ITable.OnTableCfg, groupName)
    return changed
  }

  @method()
  async removeTableLib(groupName: string, tableName: string): Promise<boolean> {
    const cfg = await this.loadTableCfg(groupName)
    if (!cfg) {
      return false
    }
    cfg.tables = cfg.tables.filter(table => table.name !== tableName)
    const changed = await this.writeTableCfg(groupName, cfg)
    changed && this.emit(ITable.OnTableCfg, groupName)
    return changed
  }

  @method()
  async addTableLib(groupName: string, tableName: string, val: TableLib): Promise<boolean> {
    const cfg = await this.loadTableCfg(groupName)
    if (!cfg) {
      return false
    }
    const tables = cfg.tables
    if (tables.find(table => table.name === tableName)) {
      return false
    }
    const table = new tablecfg.TableLib(val)
    table.name = tableName
    cfg.tables.push(table)
    const changed = await this.writeTableCfg(groupName, cfg)
    changed && this.emit(ITable.OnTableCfg, groupName)
    return changed
  }

  @method()
  async modifyTableLib(
    groupName: string,
    tableName: string,
    val: Partial<TableLib>
  ): Promise<boolean> {
    const cfg = await this.loadTableCfg(groupName)
    if (!cfg) {
      return false
    }
    cfg.tables = cfg.tables.map(table => new tablecfg.TableLib(table))
    const table = cfg.tables.find(table => table.name === tableName)
    if (!table) {
      return false
    }
    table.assign(val)

    const changed = await this.writeTableCfg(groupName, cfg)
    changed && this.emit(ITable.OnTableCfg, groupName)
    return changed
  }

  private get engineName() {
    return this.engine?.engineName || ''
  }

  private async loadTables(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    const { groups = [] } = (await this.cfg.read(tables.Tables, this.engineName, force)) || {}
    this._options.groups = groups.map(val => {
      const name = val.name
      const tables = val.tables.map(table => {
        return { ...table }
      })
      return { name, tables }
    })
    this.emit(ITable.OnTablesOptions)
  }

  private async writeTablesCache() {
    if (this.engine && !this.engine.engineName) {
      return false
    }

    const groups: tables.TableGroup[] = this._options.groups.map(val => {
      const { name, tables: list } = val
      const tbs: tables.Table[] = list.map(tb => {
        return new tables.Table(tb)
      })
      return new tables.TableGroup({ name, tables: tbs })
    })
    let changed = this.cfg.assign(tables.Tables, { groups }, '', this.engineName)
    changed = changed && (await this.cfg.write(tables.Tables, this.engineName))

    return changed
  }

  private async loadTableCfg(groupName: string) {
    const fileName = `tables/${groupName}.dat`
    return await this.cfg.read(tablecfg.TableCfg, this.engineName, false, fileName)
  }

  private async writeTableCfg(groupName: string, cfg: Partial<tablecfg.TableCfg>) {
    const fileName = `tables/${groupName}.dat`
    let changed = this.cfg.assign(tablecfg.TableCfg, cfg, fileName, this.engineName)
    changed = changed && (await this.cfg.write(tablecfg.TableCfg, this.engineName, fileName))

    return changed
  }

  private async loadOptions(force?: boolean) {
    await this.loadTables(force)
  }

  private async clearOptions() {
    this.cfg.remove(tables.Tables, '', this.engineName)
  }
}

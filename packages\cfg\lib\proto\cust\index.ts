/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  CustEngine as CustEngineModal,
  CustCrt as CustCrtModal,
  CustCrts as CustCrtsModal,
  Hardware as HardwareModal,
  CrsRecording as CrsRecordingModal,
  Crs as CrsModal,
  FieldValueItem as FieldValueItemModal,
  RequiredField as RequiredFieldModal,
  Cust as CustModal
} from '../modal/cust.mode'

export class CustEngine extends MapProto<CustEngine> {
  name = ''
  label = ''

  constructor(val?: Partial<CustEngine>) {
    super(CustEngineModal, 'CustEngine')
    val && this.assign(val)
  }
}

export class CustCrt extends MapProto<CustCrt> {
  x_address = ''
  name = ''
  status = ''
  resolution = ''
  control = ''

  constructor(val?: Partial<CustCrt>) {
    super(CustCrtModal, 'CustCrt')
    val && this.assign(val)
  }
}

export class CustCrts extends MapProto<CustCrts> {
  rate = 0
  list: Array<CustCrt> = []

  constructor(val?: Partial<CustCrts>) {
    super(CustCrtsModal, 'CustCrts')
    val && this.assign(val)
  }
}

export class Hardware extends MapProto<Hardware> {
  id = ''
  name = ''
  inter = ''
  printer_type = ''
  scan_rate = 0
  inst_addr = 0

  constructor(val?: Partial<Hardware>) {
    super(HardwareModal, 'Hardware')
    val && this.assign(val)
  }
}

export class CrsRecording extends MapProto<CrsRecording> {
  is_on = false
  division_of_scan_rate = 0
  number_of_mins = 0

  constructor(val?: Partial<CrsRecording>) {
    super(CrsRecordingModal, 'CrsRecording')
    val && this.assign(val)
  }
}

export class Crs extends MapProto<Crs> {
  auto_backup = false
  recording_control = false
  maximum_number_of_tests = ''
  disk_space_to_leave_free = ''
  continuous_recording = new CrsRecording()
  cyclic_recording = new CrsRecording()
  file_system = ''

  constructor(val?: Partial<Crs>) {
    super(CrsModal, 'Crs')
    val && this.assign(val)
  }
}

export class FieldValueItem extends MapProto<FieldValueItem> {
  value = ''
  label = ''

  constructor(val?: Partial<FieldValueItem>) {
    super(FieldValueItemModal, 'FieldValueItem')
    val && this.assign(val)
  }
}

export class RequiredField extends MapProto<RequiredField> {
  name = ''
  type = 0
  value_required = false
  label = ''
  value_vec: Array<FieldValueItem> = []

  constructor(val?: Partial<RequiredField>) {
    super(RequiredFieldModal, 'RequiredField')
    val && this.assign(val)
  }
}

export class Cust extends RootProto<Cust> {
  customer_name = ''
  engine_list: Array<CustEngine> = []
  date_format = ''
  time_format = ''
  reference_test = false
  shared_mem = ''
  crt = new CustCrts()
  hardware_list: Array<Hardware> = []
  input_param_order = ''
  param_color = ''
  crs = new Crs()
  system_scan_rate = ''
  automatic_open_on_startup = false
  rcs_control = false
  initial_alarm_state = false
  test_history = false
  store_messages_to_database = false
  test_id_prompt = ''
  invalid_value = ''
  trigger_control_paramter = ''
  print_messages = false
  print_stored_scans = false
  print_stored_comments = false
  print_stored_displays = false
  answer_process = false
  hide_open_question_cancel = false
  interface_style = 0
  field_vec: Array<RequiredField> = []

  constructor(val?: Partial<Cust>) {
    super(CustModal, Cust.key)
    val && this.assign(val)
  }

  static get maxType() {
    return CustModal.$MAX
  }

  static get minType() {
    return CustModal.$MIN
  }

  static get uri() {
    return URI(CustModal.$MAX, CustModal.$MIN)
  }

  static get types(): [number, number] {
    return [CustModal.$MAX, CustModal.$MIN]
  }

  static get key() {
    return 'Cust'
  }
}

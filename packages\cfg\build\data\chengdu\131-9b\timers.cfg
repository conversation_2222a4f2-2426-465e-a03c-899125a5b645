$TIMERS
FuelOnTime:Sec NORESET FuelOn
FuelOntoLOTimer:Sec RESET FuelOntoLOTimer_F
StarterOntoIdleTimer:Sec RESET StarterOntoIdleTimer_F
StarterOnTimer:Sec RESET StarterOnTimer_F
AccelTimer:Sec RESET TimingAccel
EGTSTartOTTimer:Sec NORESET EGTSTartOTTimer_F
EGTLim1OTTimer:Sec NORESET EGTLim1OTTimer_F
EGTLim2OTTimer:Sec NORESET EGTLim2OTTimer_F
N1RnDnTimer:Sec RESET N1RnDnTimerFlag
N2RnDnTimer:Sec RESET N2RnDnTimerFlag
StableTimer:Sec RESET StableTimer_F
StableFCTimer:Sec RESET StableFCTimer_F
TotalTestTime:Sec NORESET FuelOn
N1OverSpeedTimer:Sec NORESET N1OverSpeedTimer_F
N2OverSpeedTimer:Sec NORESET N2OverSpeedTimer_F
ECMTimer:Sec RESET ECMTimerFlag
IDGWaitTimer:Sec RESET IDGWaitTimer_F
PointTimer:Sec RESET PStoreData_F
CellTimerOn:Sec NORESET AdapterLock_I
CellTimerOff:Sec NORESET CellTimerOffFlag
$END-TIMERS

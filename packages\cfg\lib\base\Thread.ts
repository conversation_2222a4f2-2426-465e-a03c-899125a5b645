import { Dispatch } from './Dispatch'
import { IConsole } from './Emitter'

export interface WorkerCls {
  new (): Worker
}

export class Thread extends Dispatch {
  static readonly onMessage = 'Thread.onMessage'
  static readonly onError = 'Thread.onError'

  private _cls: WorkerCls
  private _worker: Worker | null
  protected _working: boolean

  constructor(cls: WorkerCls, console?: IConsole) {
    super(console)

    this._cls = cls
    this._worker = null
    this._working = false

    this.handleMessage = this.handleMessage.bind(this)
    this.handleError = this.handleError.bind(this)
  }

  get running() {
    return !!this._worker
  }

  get working() {
    return this._working
  }

  override async destroy() {
    await super.destroy()
    this._worker?.terminate()
    this._working = false
    this._worker = null
  }

  override async start() {
    this._worker = this._worker || this.initWorker()
    await super.start()
  }

  override async stop() {
    await super.stop()
    this._worker?.terminate()
    this._worker = null
    this._working = false
  }

  send(data: Record<string, any>, trans: Transferable[] = []) {
    this._working = !!this._worker
    this._worker?.postMessage(data, trans)
  }

  restart() {
    this._working = false
    this._worker?.terminate()
    this._worker = this.initWorker()
  }

  private initWorker() {
    const worker = new this._cls()
    worker.addEventListener('message', this.handleMessage)
    worker.addEventListener('error', this.handleError)
    return worker
  }

  protected handleMessage({ data }: Record<string, any>) {
    this._working = false
    this.emit(Thread.onMessage, data)
  }

  protected handleError({ data }: Record<string, any>) {
    this._working = false
    this.emit(Thread.onError, data)
  }
}

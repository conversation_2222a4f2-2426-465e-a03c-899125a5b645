$DISPLAY "Main Second" background=NewBlue editres=1280:448 movegrid=0 resizegrid=0 showgrid=0 gridcolor=Cyan
$MSGQ
$STOREDISPLAY Append
$OBJECT Digital font=20 startx=40 starty=20 endx=160 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
CITAvg type="FLOAT" width=6 prec=2 label="Inlet\nTemperature" units="Degrees F" 
CalibVB_1 type="FLOAT" width=6 prec=2 label="Gearbox\nVibration" units="In/S"
IGV_POSITION type="FLOAT" width=6 prec=2 label=" \nIGV Position" units="Deg" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=190 starty=20 endx=310 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
CalibTCell type="FLOAT" width=6 prec=2 label="Cell\nTemperature" units="Degrees F" 
CalibVB_2 type="FLOAT" width=6 prec=2 label="Turbine\nVibration" units="In/S"
GenVAvg type="FLOAT" width=6 prec=2 label="Generator\nVolts" units="Volts" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=340 starty=20 endx=460 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
TBAvg type="FLOAT" width=6 prec=2 label="Bleed Air\nTemperature" units="Degrees F" 
CalibVB_3 type="FLOAT" width=6 prec=2 label="Cooling Fan\nVib" units="In/S"
PS9Psia type="FLOAT" width=6 prec=2 label="Exhaust Static\nPressure" units="In H2O" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=490 starty=20 endx=610 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
OriTempAvg type="FLOAT" width=6 prec=2 label="Bleed Orifice\nTemp" units="Degrees F" 
CalibPCell type="FLOAT" width=6 prec=2 label=" \nBarometer" units="PSIA"
WFAvg type="FLOAT" width=6 prec=2 label=" \nFuel Flow" units="Lb/Hr" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=640 starty=20 endx=760 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
TCDAvg type="FLOAT" width=6 prec=2 label="Compressor\nDis Temp" units="Degrees F" 
OilSumpPressPsia type="FLOAT" width=6 prec=2 label="Compressor\nDis Press" units="PSIG"
CalibFPress type="FLOAT" width=6 prec=2 label="Fuel Boost\nPressure" units="PSI" 
$END-OBJECT
***********
$OBJECT Digital font=20 startx=790 starty=20 endx=910 endy=448
labelspace=5 unitspace=5 spacing=20 labelcolor=Black parambox=2 paramboxcolor=White shading=-1 \
direction=1 fontsize=28 fontweight=500 labelfontsize=18 \
labelfontweight=500 unitfontsize=12 unitfontweight=400 
CalibOilTemp type="FLOAT" width=6 prec=2 label="Oil\nTemp" units="Degrees F" 
OilSumpPressH2O type="FLOAT" width=6 prec=2 label=" Sump\nPressure" units="inH2O"
Digital3 type="FLOAT" width=6 prec=2 label=" \nPWGEN" units="KW" 
$END-OBJECT
***********
$OBJECT FuncButton font=66 startx=935 starty=5 endx=1051 endy=45
type="CHANGEDSP" label="ARUNC" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1056 starty=5 endx=1168 endy=45
type="CHANGEDSP" label="Strip Charts" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1173 starty=5 endx=1275 endy=45
type="CHANGEDSP" label="Tare" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=935 starty=50 endx=1051 endy=90
type="CHANGEDSP" label="Flow Sensor" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1056 starty=50 endx=1168 endy=90
type="CHANGEDSP" label="Averaging" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
crtname="SECOND CRT" quadindx=0 displayname="Temperature"
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1173 starty=50 endx=1275 endy=90
type="CHANGEDSP" label="Start Screen" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
crtname="Test CRT" quadindx=1 displayname="Start"
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=935 starty=95 endx=1051 endy=135
type="CHANGEDSP" label="Surge Check" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1056 starty=95 endx=1168 endy=135
type="CHANGEDSP" label="Spectrum" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
crtname="Test CRT" quadindx=1 displayname="Spectrum"
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1173 starty=95 endx=1275 endy=135
type="CHANGEDSP" label="Performance" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
crtname="Test CRT" quadindx=1 displayname="Performance"
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=935 starty=140 endx=1051 endy=180
type="CHANGEDSP" label="IGV Offset" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1056 starty=140 endx=1168 endy=180
type="PRINTPOINT" label="Log Data" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1173 starty=140 endx=1275 endy=180
type="CHANGEDSP" label="Calibrate" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
crtname="SECOND CRT" quadindx=0 displayname="Calibration"
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=935 starty=185 endx=1051 endy=225
type="PARAMLIST" label="Param List" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1056 starty=185 endx=1168 endy=225
type="CHANGEDSP" label="Fuel" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
crtname="SECOND CRT" quadindx=0 displayname="Fuel"
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1173 starty=185 endx=1275 endy=225
type="CHANGEDSP" label="Limits" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
crtname="Test CRT" quadindx=1 displayname="Limit"
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1056 starty=230 endx=1168 endy=270
type="CHANGEDSP" label="Generator" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1173 starty=230 endx=1275 endy=270
type="CHANGEDSP" label="Copyright" backgroundcolor=BrightBlue textcolor=White fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1056 starty=275 endx=1168 endy=315
type="CHANGEDSP" label="Logger" backgroundcolor=BrightBlue textcolor=Black fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1173 starty=275 endx=1275 endy=315
type="OPENTEST" label="OpenTest" backgroundcolor=BrightBlue textcolor=Black fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1056 starty=320 endx=1168 endy=360
type="CHANGEDSP" label="About" backgroundcolor=BrightBlue textcolor=Black fontsize=16 fontweight=500
$END-OBJECT
*******
$OBJECT FuncButton font=66 startx=1173 starty=320 endx=1275 endy=360
type="EXIT" label="Exit" backgroundcolor=Gold textcolor=Black fontsize=16 fontweight=500
$END-OBJECT
*******
$END-DISPLAY


import { <PERSON><PERSON>erWindow } from 'electron'
import {
  CalcFile,
  CalcsOptions,
  ICalc,
  ICfg,
  IEngine,
  CalcGroupable,
  CalcWhenToExcuteType,
  CfgFileType
} from '../interfaces'
import { BaseProxy, method } from './Base'

import { calc, calcs } from '../proto'
import { baseName } from '../utils/file'

export class Calc extends BaseProxy<ICalc> implements ICalc {
  private _options: Record<CfgFileType, CalcsOptions>
  private _caches: Map<string, CalcGroupable>

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(ICalc.NAME, master, win)

    this._options = {
      [CfgFileType.Common]: {
        files: []
      },
      [CfgFileType.EngineSpecific]: {
        files: []
      },
      [CfgFileType.Calibrate]: {
        files: []
      }
    }

    this._caches = new Map()
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readOptions(type?: CfgFileType): Promise<CalcsOptions> {
    const options = this.getOptionCache(type)
    return options
  }

  @method()
  async removeFile(index: number, type?: CfgFileType): Promise<boolean> {
    const options = this.getOptionCache(type)
    const item = options.files[index]
    if (!item) {
      return false
    }
    const engineName = this.getFileDir(type)
    if (!(await this.cfg.removeCalc(item.file, engineName))) {
      return false
    }
    this.cfg.remove(calc.Calc, item.file, engineName)
    options.files.splice(index, 1)

    const changed = await this.writeCalcsCache()
    changed && this.emit(ICalc.OnOptions)

    return changed
  }

  @method()
  async addFile(val: CalcFile, index?: number, type?: CfgFileType): Promise<boolean> {
    const options = this.getOptionCache(type)
    const name = val.name
    const idx = options.files.findIndex(v => v.name === name)
    if (idx >= 0) {
      return false
    }
    const engineName = this.getFileDir(type)
    const file = (name && (await this.cfg.createCalc(name, engineName))) || ''
    if (!file) {
      return false
    }
    val.file = file
    if (index === undefined || index === null) {
      options.files.push(val)
    } else {
      options.files.splice(index, 0, val)
    }

    const changed = await this.writeCalcsCache()
    changed && this.emit(ICalc.OnOptions)

    return changed
  }

  @method()
  async modifyFile(index: number, val: Partial<CalcFile>, type?: CfgFileType): Promise<boolean> {
    const options = this.getOptionCache(type)
    const item = options.files[index]
    if (!item) {
      return false
    }

    const engineName = this.getFileDir(type)
    if (val.name !== undefined && val.name !== item.name) {
      const file = await this.cfg.renameCalc(item.name, val.name, engineName)
      if (!file) {
        return false
      }
      val.file = file
    }
    if (val.name !== undefined) item.name = val.name
    if (val.file !== undefined) item.file = val.file

    if (engineName !== 'common' && val.type !== item.type && val.type === CfgFileType.Common) {
      const ret = await this.cfg.moveFile(item.name, engineName, 'common')
      if (!ret) {
        return false
      }
      item.type = val.type
    }

    if (val.depends !== undefined) item.depends = val.depends

    const changed = await this.writeCalcsCache()
    changed && this.emit(ICalc.OnOptions)

    return changed
  }

  @method()
  async loadCalc(
    file: string,
    type?: CfgFileType,
    force = false
  ): Promise<CalcGroupable | undefined> {
    return this.loadFile(file, type, force)
  }

  @method()
  async modifyCalc(
    file: string,
    val: Partial<CalcGroupable>,
    type?: CfgFileType
  ): Promise<boolean> {
    const engineName = this.getFileDir(type)
    const fileKey = `${engineName}/${file}`
    if (!this._caches.has(fileKey)) {
      this._caches.set(fileKey, {
        comments: [],
        excute: CalcWhenToExcuteType.kNone,
        name: '',
        lines: []
      })
    }
    const item = this._caches.get(fileKey)!
    if (val.comments !== undefined) item.comments = val.comments
    if (val.excute !== undefined) item.excute = val.excute
    if (val.name !== undefined) item.name = val.name
    if (val.lines !== undefined) item.lines = val.lines

    const changed = await this.writeFile(file, type)
    changed && this.emit(ICalc.OnCalc, file)

    return changed
  }

  @method()
  async saveCalc(file: string, type?: CfgFileType): Promise<boolean> {
    const changed = await this.writeFile(file, type)
    changed && this.emit(ICalc.OnCalc, file)

    return changed
  }

  @method()
  async loadCalcText(
    file: string,
    type?: CfgFileType,
    force?: boolean
  ): Promise<string | undefined> {
    const engineName = this.getFileDir(type)
    file = file.split('/').at(-1) || file
    return this.cfg.readText(calc.Calc, engineName, file, force)
  }

  @method()
  async saveCalcText(file: string, text: string, type?: CfgFileType): Promise<boolean> {
    const engineName = this.getFileDir(type)
    file = file.split('/').at(-1) || file
    return this.cfg.writeText(calc.Calc, text, engineName, file)
  }

  private get engineName() {
    return this.engine?.engineName || 'common'
  }

  private getOptionCache(type?: CfgFileType) {
    if (type === undefined) {
      type = this.engineName === 'common' ? CfgFileType.Common : CfgFileType.EngineSpecific
    }
    return this._options[type] || (this._options[type] = { files: [] })
  }

  private getFileDir(type?: CfgFileType) {
    return type === CfgFileType.Calibrate
      ? 'calibrate'
      : type === CfgFileType.Common
      ? 'common'
      : this.engineName
  }

  private async loadFile(file: string, type?: CfgFileType, force = false) {
    const engineName = this.getFileDir(type)
    const fileKey = `${engineName}/${file}`
    if (force || !this._caches.has(fileKey)) {
      const fileName = baseName(file)
      const { group } = (await this.cfg.read(calc.Calc, engineName, force, `/${fileName}`)) || {}
      const {
        comments = [],
        excute = CalcWhenToExcuteType.kNone,
        name = '',
        lines: list = []
      } = group || {}
      const lines =
        list.map(({ comments, type, name, unit, value }) => ({
          comments: comments.map(comment => comment.replace(/^#C/, '')),
          type,
          name,
          value,
          unit
        })) || []
      const data: CalcGroupable = {
        comments: comments.map(comment => comment.replace(/^#C/, '')),
        excute,
        name,
        lines
      }
      this._caches.set(fileKey, data)
    }

    return this._caches.get(fileKey)
  }

  private async writeFile(file: string, type?: CfgFileType) {
    const engineName = this.getFileDir(type)
    const fileKey = `${engineName}/${file}`
    let changed = false
    const data = this._caches.get(fileKey)
    if (!data) {
      return changed
    }
    const group: calc.Groupable = new calc.Groupable()
    const { name, comments = [], excute = CalcWhenToExcuteType.kNone, lines = [] } = data
    group.name = name
    group.comments = comments.map(comment => `#C${comment}`)
    group.excute = excute
    group.lines = lines.map(
      line => new calc.Lineable({ ...line, comments: line.comments.map(comment => `#C${comment}`) })
    )

    const fileName = baseName(file)
    changed = this.cfg.assign(calc.Calc, { group }, `/${fileName}`, engineName)
    changed = changed && (await this.cfg.write(calc.Calc, engineName, `/${fileName}`))

    return changed
  }

  private async loadCalcs(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    // const { includes = [] } = (await this.cfg.read(calcs.Calcs, this.engineName, force)) || {}
    // this._options.files = includes.map(inc => {
    //   const file = inc.name
    //   const name = fileName(file)
    //   const depends = inc.depends
    //   const type = file.includes('common/') ? CfgFileType.Common : CfgFileType.EngineSpecific

    //   return { file, name, depends, type }
    // })
    const type = this.engineName === 'common' ? CfgFileType.Common : CfgFileType.EngineSpecific
    const engineName = this.getFileDir(type)
    const files = await this.cfg.scanFiles(engineName, true, undefined, ['.cal'])
    const options = this.getOptionCache(type)
    options.files = files.map(file => ({
      file: file.entry,
      name: file.name,
      depends: [],
      type
    }))
    this.emit(ICalc.OnOptions)
  }

  private async writeCalcsCache() {
    if (this.engine && !this.engine.engineName) {
      return false
    }

    // const includes: calcs.IncludeFile[] = this._options.files.map(
    //   val => new calcs.IncludeFile({ name: val.file, depends: val.depends || [] })
    // )
    // let changed = this.cfg.assign(calcs.Calcs, { includes }, '', this.engineName)
    // changed = changed && (await this.cfg.write(calcs.Calcs, this.engineName))

    return true
  }

  private async loadOptions(force?: boolean) {
    await this.loadCalcs(force)
  }

  private async clearOptions() {
    this.cfg.remove(calcs.Calcs, '', this.engineName)
  }
}

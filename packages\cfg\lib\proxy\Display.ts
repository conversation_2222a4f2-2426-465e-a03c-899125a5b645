import { <PERSON><PERSON>erWindow } from 'electron'
import {
  ICfg,
  IEngine,
  IDisplay,
  DisplayOptions,
  DisplayItem,
  UDisplay,
  UObject,
  CrtResolutionType,
  kCrtValResolution,
  kDisplayProto,
  DisplayObjectType,
  UDigital,
  UString,
  UBar,
  UIndicator,
  USwitch,
  UECMButton,
  UPlot,
  CfgFileType
} from '../interfaces'
import { BaseProxy, method } from './Base'
import { fileName, formatFileName, formatFileTitle } from '../utils'

import { display, displays, kUris } from '../proto'

export class Display extends BaseProxy<IDisplay> implements IDisplay {
  private _options: DisplayOptions
  private _caches: Map<string, UDisplay>

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(IDisplay.NAME, master, win)

    this._options = {
      files: []
    }

    this._caches = new Map()
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readOptions(): Promise<DisplayOptions> {
    return this._options
  }

  @method()
  async removeFile(index: number): Promise<boolean> {
    const item = this._options.files[index]
    if (!item) {
      return false
    }

    if (!(await this.cfg.removeDisplay(this.engineName, item.file))) {
      return false
    }
    this.cfg.remove(display.Display, item.file, this.engineName)
    this._options.files.splice(index, 1)

    const changed = await this.writeDisplaysCache()
    changed && this.emit(IDisplay.OnOptions)

    return changed
  }

  @method()
  async addFile(val: DisplayItem, index?: number): Promise<boolean> {
    const name = formatFileName(val.name)
    const idx = this._options.files.findIndex(v => v.name === name)
    if (idx >= 0) {
      return false
    }
    const file = (name && (await this.cfg.createDisplay(this.engineName, name))) || ''
    if (!file) {
      return false
    }
    val.file = file
    val.title = formatFileTitle(name)
    if (index === undefined || index === null) {
      this._options.files.push(val)
    } else {
      this._options.files.splice(index, 0, val)
    }

    const changed = await this.writeDisplaysCache()
    changed && this.emit(IDisplay.OnOptions)

    return changed
  }

  @method()
  async modifyFile(index: number, val: Partial<DisplayItem>): Promise<boolean> {
    const item = this._options.files[index]
    if (!item) {
      return false
    }
    const name = val.name && formatFileName(val.name)
    if (name && name !== item.name) {
      const file = await this.cfg.renameDisplay(this.engineName, item.name, name)
      if (!file) {
        return false
      }
      val.file = file
    }

    if (name) {
      item.name = name
      item.title = formatFileTitle(name)
    }
    if (val.file !== undefined) item.file = val.file
    if (val.description !== undefined) item.description = val.description

    const changed = await this.writeDisplaysCache()
    changed && this.emit(IDisplay.OnOptions)

    return changed
  }

  @method()
  async loadDisplay(index: number): Promise<UDisplay | undefined> {
    const item = this._options.files[index]
    if (!item?.file) {
      return undefined
    }
    return this.loadFile(item.name, item.file)
  }

  @method()
  async modifyDisplay(index: number, val: Partial<UDisplay>): Promise<boolean> {
    const { name, file, title } = this._options.files[index] || {}
    if (!name || !file) return false

    if (!this._caches.has(name)) {
      this._caches.set(name, {
        name,
        description: '',
        background: '',
        editres: 'MEDRES',
        move_grid: 0,
        resize_grid: 0,
        show_grid: 0,
        grid_color: '',
        obj_vec: []
      })
    }
    const item = this._caches.get(name)!
    item.name = title
    if (val.description !== undefined) item.description = val.description
    if (val.background !== undefined) item.background = val.background
    if (val.editres !== undefined) item.editres = val.editres
    if (val.move_grid !== undefined) item.move_grid = val.move_grid
    if (val.resize_grid !== undefined) item.resize_grid = val.resize_grid
    if (val.show_grid !== undefined) item.show_grid = val.show_grid
    if (val.grid_color !== undefined) item.grid_color = val.grid_color
    if (val.obj_vec !== undefined) item.obj_vec = val.obj_vec

    const changed = await this.writeFile(name, file)
    changed && this.emit(IDisplay.OnFile, name)

    return changed
  }

  @method()
  async saveDisplay(index: number): Promise<boolean> {
    return true
  }

  private get engineName() {
    return this.engine?.engineName || 'common'
  }

  private async loadDisplays(force?: boolean) {
    if (this.engine && !this.engine.engineName) {
      return
    }

    const { includes = [] } = (await this.cfg.read(displays.Displays, this.engineName)) || {}
    this._options.files = includes.map(inc => {
      const file = inc.name
      const description = ''
      let name = fileName(file)
      name = formatFileName(name)
      const title = formatFileTitle(name)
      const type = file.includes('common/') ? CfgFileType.Common : CfgFileType.EngineSpecific

      return { file, title, name, description, type }
    })

    this.emit(IDisplay.OnOptions)
  }

  private async writeDisplaysCache() {
    if (this.engine && !this.engine.engineName) {
      return false
    }

    const includes: displays.IncludeFile[] = this._options.files.map(
      val => new displays.IncludeFile({ name: val.file })
    )
    let changed = this.cfg.assign(displays.Displays, { includes }, '', this.engineName)
    changed = changed && (await this.cfg.write(displays.Displays, this.engineName))

    return changed
  }

  private async loadFile(fileName: string, file: string) {
    if (!this._caches.has(fileName)) {
      const {
        name = '',
        background = '',
        editres = 'MEDRES',
        move_grid = 0,
        resize_grid = 0,
        show_grid = 0,
        grid_color = '',
        obj_vec: list = []
      } = (await this.cfg.read(display.Display, this.engineName, false, file)) || {}

      const obj_vec: UObject[] = []
      list.forEach(({ uri, bytes }) => {
        const Cls = kUris[uri]
        if (Cls) {
          const obj = new Cls()
          obj.unmarshal(bytes)

          const {
            start_x = 0,
            start_y = 0,
            end_x = 0,
            end_y = 0,
            font = 0.0,
            flag_id = '',
            obj_type = ''
          } = obj.base || {}
          const left = start_x
          const top = start_y
          const width = end_x - start_x
          const height = end_y - start_y
          delete obj.base
          obj_vec.push({
            left,
            top,
            width,
            height,
            font,
            flag_id,
            obj_type,
            ...obj.toJSON()
          })
        }
      })

      const data: UDisplay = {
        name,
        description: '',
        background,
        editres,
        move_grid,
        resize_grid,
        show_grid,
        grid_color,
        obj_vec
      }
      this._caches.set(fileName, data)
    }

    return this._caches.get(fileName)
  }

  private async writeFile(fileName: string, file: string) {
    let changed = false
    const data = this._caches.get(fileName)
    if (!data) {
      return changed
    }
    const val: display.Display = new display.Display()
    const {
      name,
      description = '',
      background,
      editres = CrtResolutionType.RL_1920x1080,
      move_grid,
      resize_grid,
      show_grid,
      grid_color,
      obj_vec: list = []
    } = data
    val.name = name
    val.background = background
    val.editres = (kCrtValResolution[editres as CrtResolutionType] || editres).replace('x', ':')
    val.move_grid = move_grid
    val.resize_grid = resize_grid
    val.show_grid = show_grid
    val.grid_color = grid_color

    list.forEach(obj => {
      const { left: start_x, top: start_y, width, height, font, flag_id, obj_type, ...others } = obj
      const Proto = kDisplayProto[obj_type as DisplayObjectType]
      if (Proto) {
        const end_x = start_x + width
        const end_y = start_y + height
        const base = new display.UObject({
          start_x,
          start_y,
          end_x,
          end_y,
          font,
          flag_id,
          obj_type
        })
        const proto = new Proto()
        switch (obj_type) {
          case DisplayObjectType.Digital:
            {
              const { item_vec: vec = [] } = obj as UDigital
              const item_vec = vec.map(item => new display.DigitalItem(item))
              proto.assign({ base, ...others, item_vec })
            }
            break
          case DisplayObjectType.String:
            {
              const { item_vec: vec = [] } = obj as UString
              const item_vec = vec.map(item => new display.StringItem(item))
              proto.assign({ base, ...others, item_vec })
            }
            break
          case DisplayObjectType.Bar:
            {
              const { item_vec: vec = [] } = obj as UBar
              const item_vec = vec.map(item => new display.BarItem(item))
              proto.assign({ base, ...others, item_vec })
            }
            break
          case DisplayObjectType.StatusIndicator:
            {
              const { item_vec: vec = [] } = obj as UIndicator
              const item_vec = vec.map(item => new display.IndicatorItem(item))
              proto.assign({ base, ...others, item_vec })
            }
            break
          case DisplayObjectType.Switch:
            {
              const { release_msg = {}, push_msg = {} } = obj as USwitch
              proto.assign({
                base,
                ...others,
                release_msg: new display.CalMsg(release_msg),
                push_msg: new display.CalMsg(push_msg)
              })
            }
            break
          case DisplayObjectType.Buttonobj:
            {
              const { panel = {}, state_vec: vec = [] } = obj as UECMButton
              const state_vec = vec.map(state => {
                const {
                  wait_msg = {},
                  lock_msg = {},
                  init_msg = {},
                  main_msg = {},
                  final_msg = {}
                } = state
                return new display.ECMButtonState({
                  ...state,
                  wait_msg: new display.CalMsg(wait_msg),
                  lock_msg: new display.CalMsg(lock_msg),
                  init_msg: new display.CalMsg(init_msg),
                  main_msg: new display.CalMsg(main_msg),
                  final_msg: new display.CalMsg(final_msg)
                })
              })
              proto.assign({ base, ...others, panel: new display.ECMButtonPanel(panel), state_vec })
            }
            break
          case DisplayObjectType.Plot:
            {
              const plot = obj as Record<string, any>
              const has_x_axis = !!plot.x_axis
              const has_reference_x = !!plot.reference_x
              const has_hard_copy_plot = !!plot.hard_copy_plot
              const {
                x_axis = {},
                reference_x = {},
                hard_copy_plot: hard_plot,
                yaxis_vec: vec = []
              } = obj as UPlot
              const yaxis_vec = vec.map(state => {
                const { table_plot_vec: vec = [] } = state
                const table_plot_vec = vec.map(item => new display.TablePlot(item))
                return new display.Yaxis({
                  ...state,
                  table_plot_vec
                })
              })
              const plot_item_vec = hard_plot?.plot_item_vec?.map(
                item => new display.HardCopyPlotItem(item)
              )
              proto.assign({
                base,
                ...others,
                has_x_axis,
                has_reference_x,
                has_hard_copy_plot,
                x_axis: new display.XAxis(x_axis),
                reference_x: new display.ReferenceX(reference_x),
                hard_copy_plot: new display.HardCopyPlot({
                  ...hard_plot,
                  plot_item_vec
                }),
                yaxis_vec
              })
            }
            break
          case DisplayObjectType.Text:
            {
              proto.assign({
                base,
                ...others,
                item_vec: []
              })
            }
            break
          default:
            proto.assign({ base, ...others })
            break
        }
        const bytes = proto.marshal()
        const uri = proto.uri

        val.obj_vec.push(new display.ObjectItem({ uri, bytes }))
      }
    })
    changed = this.cfg.assign(display.Display, val, file, this.engineName)
    changed = changed && (await this.cfg.write(display.Display, this.engineName, file))

    return changed
  }

  private async scanFile() {
    // const files = scanDir()
  }

  private async loadOptions(force?: boolean) {
    await this.loadDisplays(force)
  }

  private async clearOptions() {
    this._caches.clear()
  }
}

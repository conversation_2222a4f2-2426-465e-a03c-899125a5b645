import { types } from '@wuk/wkp'

export const CfgOfLimit = {
  $keys: ['limit', 'deadband', 'wait_sec', 'below_message', 'above_message', 'phase', 'color', 'alarm', 'normality', 'store_event'],

  limit: types.string,
  deadband: types.float32,
  wait_sec: types.float32,
  below_message: types.string,
  above_message: types.string,
  phase: types.string,
  color: types.string,
  alarm: types.string,
  normality: types.int32,
  store_event: types.boolean,
}

export const RateOfLimit = {
  $keys: ['eng_units', 'time_sec', 'exceeded_message', 'back_message', 'phase', 'color', 'alarm', 'store_event'],

  eng_units: types.float32,
  time_sec: types.float32,
  exceeded_message: types.string,
  back_message: types.string,
  phase: types.string,
  color: types.string,
  alarm: types.string,
  store_event: types.boolean,
}

export const LimitCfg = {
  $keys: ['cfg_list', 'rate_list'],

  cfg_list: types.arrayOf(CfgOfLimit),
  rate_list: types.arrayOf(RateOfLimit)
}

export const LimitParameter = {
  $keys: ['param_id', 'start', 'run'],

  param_id: types.string,
  start: LimitCfg,
  run: LimitCfg,
}

export const AlarmRelay = {
  $keys: ['input', 'clear', 'output'],

  input: types.string,
  clear: types.string,
  output: types.string,
}

export const LimitCounter = {
  $keys: ['color', 'param_id'],

  color: types.string,
  param_id: types.string,
}

export const Limits = {
  $MAX: 4000,
  $MIN: 100,
  $keys: ['alarm_relay', 'limit_counters', 'parameters'],

  alarm_relay: AlarmRelay,
  limit_counters: types.arrayOf(LimitCounter),
  parameters: types.arrayOf(LimitParameter),
}

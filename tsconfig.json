{
	"compilerOptions": {
		"target": "es2022",
		"module": "ESNext",
		"lib": [
			"ES2022",
			"DOM",
			"DOM.Iterable",
			"WebWorker.ImportScripts"
		],
		"moduleResolution": "node",
		"strict": true,
		"allowJs": true,
		"declaration": true,
		"noImplicitOverride": true,
		"noUnusedLocals": false,
		"esModuleInterop": true,
		// "useUnknownInCatchVariables": false,
		// "checkJs": false,
		"skipLibCheck": true,
		"skipDefaultLibCheck": true,
		"resolveJsonModule": true,
		"noImplicitReturns": true,
		"allowUnreachableCode": false,
		//
		"experimentalDecorators": true,
    	"useUnknownInCatchVariables": false,
		"allowSyntheticDefaultImports": true,
		"sourceMap": true
	},
	"exclude": [
		"node_modules",
		"./node_modules",
		"./node_modules/*",
    	"*/node_modules/*"
	]
}

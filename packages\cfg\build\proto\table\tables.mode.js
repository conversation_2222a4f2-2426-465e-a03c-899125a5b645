import { types } from '@wuk/wkp'

export const Table = {
  $keys: [
    'name', 'type', 'save', 'dynexp', 'exp', 'size', 'ascii', 'format', 'xparam_id', 'yparam_id', 
    'zparam_id', 'constant', 'interp', 'offset', 'extrap', 'extrap_const', 'coeff', 'maxpoly',
    'minpoly', 'revdate', 'mandate', 'manrev', 'comment'
  ],

  name: types.string,
  type: types.int32,
  save: types.int32,
  dynexp: types.int32,
  exp: types.string,
  size: types.int32,
  ascii: types.int32,
  format: types.string,
  xparam_id: types.string,
  yparam_id: types.string,
  zparam_id: types.string,
  constant: types.float32,
  interp: types.int32,
  offset: types.int32,
  extrap: types.int32,
  extrap_const: types.float32,
  coeff: types.arrayOf(types.float32),
  maxpoly: types.float32,
  minpoly: types.float32,
  revdate: types.string,
  mandate: types.string,
  manrev: types.string,
  comment: types.string
}

export const TableGroup = {
  $keys: ['name', 'format', 'tables', 'comment'],

  name: types.string,
  format: types.string,
  tables: types.arrayOf(Table),
  comment: types.string
}

export const Tables = {
  $MAX: 5000,
  $MIN: 1000,
  $keys: ['groups'],

  groups: types.arrayOf(TableGroup)
}

/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  BitValue as BitValueModal,
  RangeValue as RangeValueModal,
  ArincRecvSignal as ArincRecvSignalModal,
  Arinc429RecvCfg as Arinc429RecvCfgModal
} from '../modal/arinc429_recv.mode'

export class BitValue extends MapProto<BitValue> {
  name = ''
  bit_num = 0
  val_0 = ''
  val_1 = ''
  lable = ''

  constructor(val?: Partial<BitValue>) {
    super(BitValueModal, 'BitValue')
    val && this.assign(val)
  }
}

export class RangeValue extends MapProto<RangeValue> {
  min = 0.0
  max = 0.0
  unit = ''

  constructor(val?: Partial<RangeValue>) {
    super(RangeValueModal, 'RangeValue')
    val && this.assign(val)
  }
}

export class ArincRecvSignal extends MapProto<ArincRecvSignal> {
  name = ''
  label = ''
  data = ''
  bin_range = 0.0
  sig_bits = 0
  rate = 0
  rate_limit = 0
  signal_range = new RangeValue()
  bits: Array<BitValue> = []

  constructor(val?: Partial<ArincRecvSignal>) {
    super(ArincRecvSignalModal, 'ArincRecvSignal')
    val && this.assign(val)
  }
}

export class Arinc429RecvCfg extends RootProto<Arinc429RecvCfg> {
  channel = 0
  eec = ''
  speed = ''
  parity = ''
  active = ''
  recv_signals: Array<ArincRecvSignal> = []

  constructor(val?: Partial<Arinc429RecvCfg>) {
    super(Arinc429RecvCfgModal, Arinc429RecvCfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return Arinc429RecvCfgModal.$MAX
  }

  static get minType() {
    return Arinc429RecvCfgModal.$MIN
  }

  static get uri() {
    return URI(Arinc429RecvCfgModal.$MAX, Arinc429RecvCfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [Arinc429RecvCfgModal.$MAX, Arinc429RecvCfgModal.$MIN]
  }

  static get key() {
    return 'Arinc429RecvCfg'
  }
}

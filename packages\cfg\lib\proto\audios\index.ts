/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  Audio as AudioModal,
  Audios as AudiosModal
} from '../modal/audios.mode'

export class Audio extends MapProto<Audio> {
  name = ''
  file = ''
  repeat = 0
  priority = 0

  constructor(val?: Partial<Audio>) {
    super(AudioModal, 'Audio')
    val && this.assign(val)
  }
}

export class Audios extends RootProto<Audios> {
  path = ''
  audios: Array<Audio> = []

  constructor(val?: Partial<Audios>) {
    super(AudiosModal, Audios.key)
    val && this.assign(val)
  }

  static get maxType() {
    return AudiosModal.$MAX
  }

  static get minType() {
    return AudiosModal.$MIN
  }

  static get uri() {
    return URI(AudiosModal.$MAX, AudiosModal.$MIN)
  }

  static get types(): [number, number] {
    return [AudiosModal.$MAX, AudiosModal.$MIN]
  }

  static get key() {
    return 'Audios'
  }
}

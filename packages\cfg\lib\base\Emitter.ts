export interface IConsole extends Record<string, any> {
  log(...args: any[]): void
  debug(...args: any[]): void
  error(...args: any[]): void
  info(...args: any[]): void
}

export abstract class Emitter {
  abstract emit(event: string, ...args: any[]): boolean
  abstract init(...args: any[]): Promise<void>
  abstract start(): Promise<void>
  abstract stop(): Promise<void>
  abstract destroy(): Promise<void>
  abstract on: (event: string, fn: (...args: any[]) => void) => this
  abstract off: (event: string, fn: (...args: any[]) => void) => this
  abstract listenerCount(event: string): number

  // 模块启动
  static get UP() {
    return 'Emitter.UP'
  }

  // 模块停止
  static get DOWN() {
    return 'Emitter.DOWN'
  }
}
